(function () {

    'use strict';
    angular
        .module('app')
        .component('buildingServicesData', {
            bindings: {
                serviceCategoryList: '<',
                building: '<',           // The specific building (proposed or reference)
                buildingType: '<',
                comparisonBuilding: '<', // Building to compare against (usually the baseline)
                baselineOption: '<',
                option: '<',             // The compliance option
                disabled: '<',           // Disable all inputs?
                complianceOptions: '<',  // Full list of all compliance options for copying from,
            },
            templateUrl: 'app/ui/assessment/building-services-data.html',
            controller: BuildingServicesData,
            controllerAs: 'vm'
        });

    BuildingServicesData.$inject = ['$rootScope', '$scope', '$mdDialog',
        'servicetemplateservice', 'manufacturerservice',
        'uuid4', 'constants', '$anchorScroll', 'common', 'buildingservicestemplateservice', 'zoneservice',
        'columnvisibilityservice'];

    function BuildingServicesData($rootScope, $scope, $mdDialog,
        servicetemplateservice, manufacturerservice,
        uuid4, constants, $anchorScroll, common, buildingservicestemplateservice, zoneservice,
        columnvisibilityservice) {

        let vm = this;

        let intervalTimer = null;

        // List of functions we want to expose.
        vm.copyServicesToFrom = copyServicesToFrom;
        vm.searchChanged = searchChanged;
        vm.addServiceParentFromTemplate = addServiceParentFromTemplate;
        vm.addServiceElement = addServiceElement;
        vm.substituteParent = substituteParent;
        vm.duplicateParent = duplicateParent;
        vm.removeParentService = removeParentService;
        vm.copyElement = copyElement;
        vm.hasElements = hasElements;
        vm.switchToAddMode = switchToAddMode;
        vm.filteredTemplateList = filteredTemplateList;
        vm.launchParentActionModal = launchParentActionModal;
        vm.launchElementActionModal = launchElementActionModal;

        $scope.building = this.building; // this allows 'building' to be accessed directly in the HTML template.
        $scope.option = this.option;
        vm.buildingType = this.buildingType;
        vm.comparisonBuilding = this.comparisonBuilding;

        vm.allServiceTemplates = [];
        vm.serviceTypes = [];
        vm.icRatings = [];
        vm.batteryTypes = [];
        vm.heatingSystemTypes = [];
        vm.serviceControlDevices = [];
        vm.serviceFuelTypes = [];
        vm.servicePumpTypes = [];

        vm.getMappedSystemType = servicetemplateservice.getMappedSystemType;

        // Strings as want to search for likeness (ie. 1 will show 10)
        vm.numbersList = [
            '1', '2', '3', '4', '5', '6', '7', '8', '9', '10',
            '11', '12', '13', '14', '15', '16', '17', '18', '19', '20'
        ];

        function initialize() {

            servicetemplateservice.getAll().then(data => vm.allServiceTemplates = data);

            servicetemplateservice.getServiceTypes().then(data => vm.serviceTypes = data);

            servicetemplateservice.getICRatings().then(data => vm.icRatings = data);

            servicetemplateservice.getServiceBatteryTypes().then(data => vm.batteryTypes = data);

            servicetemplateservice.getHeatingSystemTypes().then(data => vm.heatingSystemTypes = data);

            servicetemplateservice.getServiceControlDevices().then(data => {
                vm.serviceControlDevices = data;
                vm.defaultControlDevice = vm.serviceControlDevices.find(x => x.serviceControlDeviceCode === "None");
            });

            servicetemplateservice.getServiceFuelTypes().then(data => vm.serviceFuelTypes = data);

            servicetemplateservice.getServicePumpTypes().then(data => vm.servicePumpTypes = data);

            intervalTimer = setInterval(
                () => setAllElementCodes(),
                1000
            );

        }

        initialize();

        function filteredTemplateList(category) {
            const list = vm.serviceTemplateList
                .filter(x => x.serviceCategory.serviceCategoryCode === category.serviceCategoryCode);

            return list;
        }

        function switchToAddMode(category) {
            category.inAddMode = true;

            // Set a small delay and then focus on our template input
            setTimeout(() => {
                let ele = document.getElementById("Template" + category.serviceCategoryCode);
                ele = ele.childNodes[1]; // Work our way through the DOM
                ele = ele.childNodes[2]; // until we get to the input
                //ele = ele.childNodes[3]; // we actually want to focus on...
                ele.focus();

            }, 50);
        }

        vm.disabledEx = function () {
            return vm.disabled;
        }

        vm.batteryTypeChange = function(serviceTemplate) {
            if (serviceTemplate.serviceBatteryType.serviceBatteryTypeCode == 'None') {
                serviceTemplate.batteryCapacity = '0.00';
            }
        }

        /** Copies the given data to the given option.building from the given building */
        function copyServicesToFrom(toBuilding, fromBuilding) {

            // USE TIMEOUTS TO AVOID WEIRD UI DISPLAY BUGS.
            setTimeout(() => {
                buildingservicestemplateservice.applyTemplate(null, toBuilding, fromBuilding)
            }, 100);

        }

        vm.optionsNotThisOrBaseline = function (option) {
            return vm.complianceOptions
                ?.filter(x => x.optionIndex !== 0 && x.optionIndex !== option.optionIndex);
        }

        /**
         * We only show the 'copy baseline' button for options if we are currently viewing
         * the 'proposed' building OR we are viewing the 'reference building' AND their is
         * a reference building available to copy from in the baseline option.
         *
         * We determine if a reference building is 'available' based upon its compliance method.
         */
        vm.showCopyBaselineForOption = function (option) {
            return vm.buildingType === 'proposed' ||
                   (vm.buildingType === 'reference' && hasReferenceBuilding(option.complianceMethod.complianceMethodCode));
        }

        function hasReferenceBuilding(complianceMethodCode) {
            return complianceMethodCode === "CMPerfSolutionDTS" ||
                   complianceMethodCode === "CMPerfSolution";
        }

        function clearCategory(category, building) {

            let list = building.services;

            if(list == null || list.length === 0)
                return;

            for (let i = list.length - 1; i >= 0; i--) {
                if (list[i].serviceCategory.serviceCategoryCode === category.serviceCategoryCode) {
                    list.splice(i, 1);
                }
            }
        }
        vm.clearCategory = clearCategory;

        function searchChanged(constructionItem, searchText, variable) {
            constructionItem[variable + 'PreviousSearchText'] = searchText;
        }

        /**
         * Adds a new construction item PARENT to the list (either a surface or opening) based on the given template.
         * Existing constructions with the same derived template can still exist when a new one is added (as it may
         * for instance be a different color).
         *
         * @param {any} category
         * @param {any} building
         * @param {any} templateView
         */
        async function addServiceParentFromTemplate(category, building, templateView) {

            category.inAddMode = false;

            // Ok so we got the generic item 'view', now retreive the full template.
            let template = await servicetemplateservice.getServiceTemplate(templateView.serviceTemplateId);

            // Merge our item with the template data but make sure we
            // retain our NEW id and also a ref to the template/'derived from' ID.
            let newItem = angular.copy(template);
            newItem.derivedFromServiceTemplateId = template.serviceTemplateId;
            newItem.serviceTemplateId = uuid4.generate();

            // Insert first first element which is a clone of itself its parent...
            newItem.elements = [{ ...newItem, serviceTemplateId: uuid4.generate(), number: 1, serviceControlDevice: vm.defaultControlDevice }];
            newItem.isExpanded = true;

            // Set isPrimary to true if there are no other elements with this category.
            if(category.hasChildRows === false) {
                const existingCount = vm.building.services.filter(x => x.serviceCategory.serviceCategoryCode === category.serviceCategoryCode).length;
                if(existingCount == null || existingCount === 0) {
                    newItem.isPrimary = true;
                }

            }

            vm.building.services.push(newItem);

            setElementCodesForCategory(category.serviceCategoryCode);

        }

        /**
         *  Adds a new construction item to the list (either a surface or opening)
         *  based on the given template. NOTE: A template is REQUIRED now.
         * @param {any} category
         * @param {any} building
         * @param {any} template
         */
        async function addServiceElement(category, building, template) {

            category.inAddMode = false;

            let list = building.services;

            // Ok so we got the generic item 'view', now retrieve the proper one..
            let t = await servicetemplateservice.getServiceTemplate(template.serviceTemplateId);

            // Merge our item with the template data but make sure we
            // retain our NEW id and also a ref to the template/'derived from' ID.
            let newItem = angular.copy(t);
            let guid = uuid4.generate();
            newItem.derivedFromServiceTemplateId = newItem.serviceTemplateId;
            newItem.serviceTemplateId = guid;
            newItem.isExpanded = true;

            list.push(newItem);

            setElementCodesForCategory(category.serviceCategoryCode);
        }

        vm.deleteElement = function (parent, item, building) {
            parent.elements = parent.elements
                .filter(x => x.serviceTemplateId !== item.serviceTemplateId);

            setElementCodesForCategory(item.serviceCategory.serviceCategoryCode);
        }

        function copyElement(parent, item, building) {

            // Need to insert this into the overall list so that it updates on save.
            // Can't just insert into the 'group'.
            let list = parent.elements;

            let index = list.indexOf(item);
            let clone = {
                ...list[index],
            };
            clone.serviceTemplateId = uuid4.generate();
            clone.source = "manual";

            // Insert below cloned item.
            list.splice(index + 1, 0, clone);

            setElementCodesForCategory(item.serviceCategory.serviceCategoryCode);

        }

        /**
         * @param {string} categoryCode
         */
        function setElementCodesForCategory(categoryCode) {

            const groups = vm.building.services.filter(x => x.serviceCategory.serviceCategoryCode === categoryCode);

            let elem = 1;
            for (let x = 0; x < groups.length; x++) {

                const elements = groups[x].elements;
                const code = vm.serviceCategoryList
                    .filter(x => x.serviceCategoryCode === categoryCode)[0].elementCode;

                for (let i = 0; i < elements.length; i++) {
                    elements[i].elementNumber = getElementCode(code, elem)
                    elem++;
                }
            }
        }
        vm.setElementCodesForCategory = setElementCodesForCategory;

        function setAllElementCodes() {
            setElementCodesForCategory('SpaceHeatingSystem');
            setElementCodesForCategory('SpaceCoolingSystem');
            setElementCodesForCategory('HotWaterSystem');
            setElementCodesForCategory('ArtificialLighting');
            setElementCodesForCategory('ExhaustFans');
            setElementCodesForCategory('CeilingVents');
            setElementCodesForCategory('CeilingFans');
            setElementCodesForCategory('PhotovoltaicSystem');
            setElementCodesForCategory('SwimmingPool');
            setElementCodesForCategory('Spa');
        }

        vm.bulkCopyElements = (parent) => {

            const elementsToCopy = parent.elements.filter(x => x.checkboxSelected === true);

            elementsToCopy.forEach(item => copyElement(parent, item));

            parent.elements.forEach(x => x.checkboxSelected = false);

            vm.updateBulkSelectStatus(parent);

        }

        vm.bulkDeleteElements = function (parent) {
            parent.elements = parent.elements.filter(x => x.checkboxSelected !== true);
            setElementCodesForCategory(parent.serviceCategory.serviceCategoryCode);
        }

        function getElementCode(code, number) {
            return code + '-' + common.addLeadingZero(number, 3);
        }

        /** Substitutes a parent-only row with a new template */
        async function substituteParent(parent, template, category, building) {

            if (parent == null || template == null || template == "")
                return;

            // Ok so we got the generic item, now retrieve the proper one..
            let t = await servicetemplateservice.getServiceTemplate(template.serviceTemplateId);

            // Merge our item with the template data but make sure we
            // retain our NEW id and also a ref to the template/'derived from' ID.
            let oldGuid = parent.serviceTemplateId;
            const backupElements = parent.elements;
            let newParent = common.nullAwareMerge(parent, t);

            newParent.derivedFromServiceTemplateId = t.serviceTemplateId;
            newParent.serviceTemplateId = oldGuid; // We want to keep our original ID
            newParent.elements = backupElements;
            newParent.isExpanded = parent.isExpanded;
            newParent.isPrimary = parent.isPrimary;
            newParent.overrideDisplayDescription = null;

            // nullAwareMerge replaced our reference, so we need to set what's in our
            // master list to the new values.
            let match = building.services.filter(x => x.serviceTemplateId === newParent.serviceTemplateId);

            for (let i = 0; i < newParent.elements.length; i++) {
                // Not much has to be updated on the child.
                const child = newParent.elements[i];
                child.derivedFromServiceTemplateId = t.serviceTemplateId;
            }

            let index = building.services.indexOf(match[0]);
            building.services[index] = newParent;

            newParent.isInChangeMode = false;
            newParent.isExpanded = true;
        }

        vm.heatingSystemTypesForCategoryCode = (code) => servicetemplateservice.heatingSystemTypesForCategoryCode(code, vm.heatingSystemTypes);

        function duplicateParent(parent, building) {

            const originalIndex = building.services.indexOf(parent);

            const clone = angular.copy(parent);
            clone.serviceTemplateId = uuid4.generate();
            clone.isPrimary = false;

            building.services.splice(originalIndex, 0, clone);

            setElementCodesForCategory(parent.serviceCategory.serviceCategoryCode);

        }

        function removeParentService(parent, building) {

            if(parent.isPrimary === true)
                vm.updatePrimaryServiceForCategory(false, parent.serviceCategory, parent);

            building.services = building.services
                .filter(x => x.serviceTemplateId !== parent.serviceTemplateId);

        }

        /** Returns true if there are any construction items present of the given code. */
        function hasElements(category, building) {
            return building.services
                ?.some(x => x.serviceCategory.serviceCategoryCode === category.serviceCategoryCode);
        }

        vm.toTitleCase = function(str) {
            return common.toSplitTitleCase(str);
        }

        vm.serviceTemplateList = [];
        let serviceTemplatePromise = servicetemplateservice.getAll()
            .then(data => {
                vm.serviceTemplateList = data;
            });

        vm.sectionExpansions = {}; // Used to keep track of which sections are expanded.
        vm.expand = function (section) {
            vm.sectionExpansions[section] = !vm.sectionExpansions[section];
        }

        vm.servicesInCategory = code => vm.building.services?.filter(x => x.serviceCategory.serviceCategoryCode === code);

        vm.cutOutAreaSum = (group) => group.elements
            .map(e => e.cutoutArea)
            .reduce((a,b) => a + b, 0);

        vm.numberSum = (group) => group.elements
            .map(x => x.number)
            .reduce((a, b) => a + b, 0)

        vm.cutOutAreaCalculation = function (parent, item) {

            const cutoutDiameter = parent.cutOutDiameter || 0;

            // THR-225: Cut Out Area (m2) = Cut Out Diameter (mm) squared divided by one million
            // Old: item.cutoutArea = item.number * Math.pow((Math.PI * (0.5 * cutoutDiameter)), 2) / 1000000;
                // Notice that PI was being squared as well.
            item.cutoutArea = item.number * Math.pow(cutoutDiameter, 2) / 1000000;

            return item.cutoutArea;
        }

        vm.cutOutAreaSquareCalculation = function (parent, item) {

            const width = parent.width || 0;
            const length = parent.length || 0;
            item.cutoutArea = item.number * (width  * length) / 1000000;
            return item.cutoutArea;
        }

        vm.panelAreaSum = (group) => group.elements
            .map(x => x.panelArea)
            .reduce((a, b) => a + b, 0);

        vm.roofSpaceZones = (zones) => zoneservice.roofSpaceZones(zones);

        /** Special rule: Photovoltaic systems storey to update automatically */
        vm.updateItemToMatchZoneData = function(item, linkId) {

            const zone = vm.building.zones.filter(x => x.linkId === linkId)[0];

            if(zone === null || zone === undefined)
                return;

            item.storey = zone.storeyBelow;

            // SPECIAL RULE - This value (i.e. Panel Pitch) matches the Roof Pitch value for the associated Parent Zone
            // found in the Roof Section of the Construction Tab (see Parent Zone note above)
            // i.e. if RoofSPace0 is selcted for Parent Zone, the value for Panel Pitch will be the same as the value of
            // Roof Pitch shown on the Construction Tab for RoofSpace0

            // LUKES NOTE: There are N roof constructions, and they can all potentially have different pitches.
            // So we're just grabbing the first one and using its value (...).
            const construction = vm.building.surfaces
                .find(s => s.category.constructionCategoryCode === "Roof" && s.parentZoneId === linkId);

            if(construction === null || construction === undefined)
                return;

            item.tilt = construction.tilt;

            // Run this funky func to make our tilt input show x.00 instead of just x (forces the formatted-number
            // directive to apply its magic formatting)
            setTimeout(() => {
                document.getElementById(item.serviceTemplateId + "Tilt").focus();
                document.getElementById(item.serviceTemplateId + "Tilt").blur();
            }, 1);
        }

        /**
         * There can only be ONE instance of this checkbox being checked in each section (i.e. one in Space Heating
         * System section, one in Space Cooling System section and one in Hot Water System section)
         * There MUST be one instance of this checkbox being checked at ALL times (in each section)
         *
         * When the first item (in the section) is added, the checkbox defaults to CHECKED
         *
         * When any subsequent item is added (into the SAME section), the checkbox for the new items is UNCHECKED
         * The user can then manually CHECK  one of the new entries (in each section), and this will automatically
         * UNCHECK the original (in the same section)
         *
         * In instances where there is more than one item, and the item that  has this CHECKED and it is deleted by the
         * user, the next available item (i.e. row) will be AUTOMATICALLY checked (in that section)
         */
        vm.updatePrimaryServiceForCategory = function(checked, category, service) {

            // TODO: Make sure this works.
            // I think it's working.

            // All services matching this category.
            const matching = vm.building.services
                .filter(x => x.serviceCategory.serviceCategoryCode === category.serviceCategoryCode);

            // If this service has been checked, uncheck everything then check this.
            if(checked) {

                matching.forEach(s => s.isPrimary = false);

                service.isPrimary = true;
            } else {

                // User has UNCHECKED the given item, so instead we find the previous (or failing that, next) item in
                // the row and check that instead.

                let index = matching.indexOf(service);
                let offset = index === 0 ? 1 : -1;

                // Update closest row to be primary
                if(matching[index + offset] !== null && matching[index + offset] !== undefined)
                    matching[index + offset].isPrimary = true;

            }

        }

        vm.isOnlyChild = function(category) {
            return vm.servicesInCategory(category.serviceCategoryCode).length === 1;
        }

        /** Simply returns the descriptive name of the floor this zone is on/above/below */
        vm.floorOfZone = function(linkId) {

            let zone = vm.building?.zones?.find(x => x.linkId === linkId);

            if(zone == null)
                zone = vm.building?.spaces?.find(x => x.linkId === linkId);

            if(zone == null)
                return "";

            const storey = zone.storey ?? zone.storeyBelow ?? zone.storeyAbove;
            return vm.building.storeys[storey]?.name;
        }

        /* Number Field Auto Complete Functions */

        vm.searchNumbersList = function(filter) {
            return vm.numbersList.filter(number => number.includes(filter));
        }

        vm.selectedNumberChanged = function(item, selectedNumber) {

            const parsedSelectedNumber = parseInt(selectedNumber);

            if (item.number !== parsedSelectedNumber && Number.isInteger(parsedSelectedNumber))
                item.number = parsedSelectedNumber;
        }

        // *** item.numberSearchText must be a string.

        vm.selectSearchedNumber = function(item) { // onBlur

            const parsedSearchFilter = parseInt(item.numberSearchText);

            // Previous value and new value are the same, so do nothing
            if (item.number === parsedSearchFilter)
                return;

            // Set to the parsed value
            if (Number.isInteger(parsedSearchFilter) && 0 < parsedSearchFilter) {
                // Select the number even if not in the list
                item.number = parsedSearchFilter;

                // Also change the input incase it was modified (rounded)
                item.numberSearchText = parsedSearchFilter.toString();
            }
            else {
                 // Invalid value so default it back to 1 (search text must be a string)
                item.number = 1;
                item.numberSearchText = '1';
            }
        }

        vm.openVisibilityModal = function(parent) {
            const modalSuccessCallback = (response) => parent.hiddenTableColumns = response;
            columnvisibilityservice.openVisibilityModal(parent, modalSuccessCallback);
        }

        vm.isTableColumnVisible = function(columnProperty, parent) {
            return columnvisibilityservice.isTableColumnVisible(columnProperty, parent);
        }

        vm.expandSection = function(serviceCategoryCode) {

            vm.sectionExpansions[serviceCategoryCode] = !vm.sectionExpansions[serviceCategoryCode]

            const s = vm.servicesInCategory(serviceCategoryCode);

            s.forEach(parent => parent.isExpanded = vm.sectionExpansions[serviceCategoryCode]);

        }

        vm.availableBuildingZones = function(row) {

            if(row.serviceCategory.serviceCategoryCode === 'ArtificialLighting' ||
               // row.serviceCategory.serviceCategoryCode === 'ExhaustFans' ||
               row.serviceCategory.serviceCategoryCode === 'CeilingFans') {

                return [...zoneservice.interiorZones(vm.building.zones), ...spaceTypesNeededInArtificialLighting()];

            } else {
                return zoneservice.interiorZones(vm.building.zones);
            }
        }

        function spaceTypesNeededInArtificialLighting() {
            return vm.building.spaces.filter(x => x.zoneType?.zoneTypeCode === 'ZTClass10A' ||
                                                  x.zoneType?.zoneTypeCode === 'ZTOutdoor');
        }

        /** Opens popup modal to rename building construction. **/
        function launchParentActionModal(action, parent, building) {

            var modalScope = $rootScope.$new();
            modalScope.action = action;
            modalScope.type = "service";
            modalScope.parent = parent;
            modalScope.building = building;
            modalScope.option = vm.option;
            modalScope.disabled = vm.disabledEx();

            // Choose the correct modal template based on the action
            var templateUrl = action === 'rename'
                ? 'app/ui/assessment/construction-rename-modal/construction-rename-modal.html'
                : 'app/ui/assessment/services-substitute-modal/services-substitute-modal.html';

            var modalOptions = {
                templateUrl: templateUrl,
                scope: modalScope
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);

            modalScope.modalInstance.then(

                // Submitted
                async function (data) {

                    console.log("Returned: ", data);

                    if(action === 'rename') {
                        parent.overrideDisplayDescription = data.overrideDisplayDescription;
                        parent.overrideInsulationDescription = data.overrideInsulationDescription;
                    }

                    if(action === 'substitute') {
                        await substituteParent(parent, data, parent.serviceCategory, building);
                    }

                },

                // Cancelled
                function () {}
            );
        }

        /** Opens popup modal to mvoe or copy element/s. **/
        function launchElementActionModal(parent, building, elements = null) {

            let fromBulk = false;
            if(elements === null) {
                fromBulk = true;
                elements = parent.elements.filter(x => x.checkboxSelected === true);
            }

            var modalScope = $rootScope.$new();
            modalScope.type = "service"; // Base type... "services" for services modal
            modalScope.parent = parent;
            modalScope.elements = elements;

            modalScope.building = building;
            modalScope.option = vm.option;
            modalScope.disabled = vm.disabledEx();

            // set available "destinations" for the move/copy based on parent category.
            modalScope.availableDestinations = building.services
                .filter(x => x.serviceCategory.serviceCategoryCode === parent.serviceCategory.serviceCategoryCode);

            var modalOptions = {
                templateUrl: 'app/ui/assessment/construction-element-action-modal/construction-element-action-modal.html',
                scope: modalScope
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);

            modalScope.modalInstance.then(
                // Submitted
                async function (data) {

                    if(data.action === 'move')
                        elements.forEach(ele => {
                            if(fromBulk) ele.checkboxSelected = false;
                            moveElementTo(ele, parent, data.destination);
                        });

                    if(data.action === 'copy')
                        elements.forEach(ele => {
                            if(fromBulk) ele.checkboxSelected = false;
                            copyElementTo(ele, data.destination);
                        });

                    if(fromBulk) {
                        elements.forEach(x => x.checkboxSelected = false);

                        parent.selectAllCheckboxState = false;
                        parent.bulkSelectCheckboxIsIndeterminate = false;
                    }

                },
                // Cancelled
                function () {}
            );
        }

        function moveElementTo(element, parent, destination) {
            copyElementTo(element, destination);
            parent.elements = parent.elements.filter(x => x.serviceTemplateId !== element.serviceTemplateId);
        }

        function copyElementTo(element, destination) {
            const copy = angular.copy(element);
            copy.serviceTemplateId = uuid4.generate();
            destination.elements.push(copy);
        }

        // TODO: This and the below are (close to) identical between construction/opening/services.
        // Should pull into one function in a service or something - but this goes for ALL bulk edit
        // checkboxes which currently show some different functionality (this is the best though)
        vm.selectAllElementCheckboxes = function (parent, state) {

            setTimeout(() => {

                let wantedResult = false;
                if (parent.bulkSelectCheckboxIsIndeterminate === true)
                    wantedResult = true;
                else if (state !== true)
                    wantedResult = true;

                parent.elements.forEach(x => x.checkboxSelected = wantedResult);

                parent.selectAllCheckboxState = wantedResult;
                vm.updateBulkSelectStatus(parent);

            }, 25);
        }

        vm.updateBulkSelectStatus = function (parent) {

            const elements = parent.elements;

            const allChecked = elements.every(x => x.checkboxSelected === true);
            const someChecked = elements.some(x => x.checkboxSelected === true);

            if(allChecked) {
                parent.bulkSelectCheckboxIsIndeterminate = false;
                parent.selectAllCheckboxState = true;

                // Have to manually apply this class (Probably due to clashes with the indeterminate value)
                const checkBoxId = parent.serviceTemplateId + "allCheckbox";
                setTimeout(() => {
                    let currentClass = document.getElementById(checkBoxId).className;
                    if (currentClass.indexOf("ng-empty-add md-checked") === -1) {
                        document.getElementById(checkBoxId).className += " ng-empty-add md-checked";
                    }
                }, 25);

            } else if(someChecked) {
                parent.selectAllCheckboxState = false;
                parent.bulkSelectCheckboxIsIndeterminate = true;
            } else {
                parent.selectAllCheckboxState = false;
                parent.bulkSelectCheckboxIsIndeterminate = false;
            }

            safeApply();

            function safeApply() {
                const phase = $rootScope.$$phase;
                if (!phase) {
                    $rootScope.$apply();
                }
            }
        }

        vm.templateFieldHasValue = (serviceTemplateId, fieldName) => vm.allServiceTemplates.some(t => t.serviceTemplateId == serviceTemplateId && t[fieldName] != null);

    }
})();