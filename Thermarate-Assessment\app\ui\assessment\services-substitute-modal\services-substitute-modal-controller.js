(function () {

    'use strict';

    let controllerId = 'servicesSubstituteModalController';

    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state', '$q',
            'bootstrap.dialog', 'uuid4',
            'constructionservice',
            'servicetemplateservice',
            'common',
            servicesSubstituteModalController]);

    function servicesSubstituteModalController($rootScope, $scope, $mdDialog, $stateParams, $state, $q,
                                      modalDialog, uuid4,
                                      constructionservice,
                                      servicetemplateservice,
                                      common) {

        // The model for this form
        const vm = this;

        vm.action = $scope.action;
        vm.type = $scope.type;
        vm.generalSectionDisplay = $scope.generalSectionDisplay;
        vm.parent = $scope.parent;
        vm.building = $scope.building;
        vm.option = $scope.option;
        vm.disabled = $scope.disabled;

        function initialize() {

            vm.data = {
                overrideDisplayDescription: vm.parent.overrideDisplayDescription || vm.parent.displayDescription || vm.parent.description,
                originalDescription: vm.parent.displayDescription || vm.parent.description,
                overrideInsulationDescription: vm.parent.overrideInsulationDescription || getOriginalInsulationDescription(),
                originalInsulation: getOriginalInsulationDescription(),
                chenathConstructionCode: vm.parent.externalConstructionId || null,
            }

            function getOriginalInsulationDescription() {
                // For surfaces, get insulation description from InsulationData
                if (vm.parent.insulationData && vm.parent.insulationData.description) {
                    return vm.parent.insulationData.description;
                }
                // For other types, return empty string or appropriate default
                return "";
            }

            vm.substituteParent = null;

            if(vm.action === "rename") {
                const displayDescription = vm.parent.overrideDisplayDescription || vm.parent.displayDescription || vm.parent.description;
                vm.title = `Rename "${displayDescription}"`;
            }



            if(vm.action === "substitute" && vm.type === "service") {

                vm.templateListInCategory = [];
                vm.servicesList = [];
                vm.selectedSubstitute = null;
                vm.searchString = null;
                vm.showDisplayDesc = false;
                vm.sortBy = null;
                vm.actionButtons = [];
                vm.totalServices = 0;
                vm.totalFilteredServices = 0;
                vm.totalRecords = 0;
                vm.showingFromCnt = 0;
                vm.showingToCnt = 0;
                vm.showMoreBusy = false;

                // Pagination variables
                vm.resultsPageSize = 100;

                // Initialize the services list
                vm.initialRefreshList();

                const displayDescription = vm.parent.overrideDisplayDescription || vm.parent.displayDescription || vm.parent.description;
                vm.title = `Substitute "${displayDescription}"`
                vm.searchTitle = `Search Services Database`;

            }

        }

        vm.cancel = function () {
            $mdDialog.cancel();
        }

        vm.save = async function () {

            if(vm.action === "rename")
                $mdDialog.hide(vm.data);

            if(vm.action === "substitute")
                $mdDialog.hide(vm.selectedSubstitute);

        }

        vm.copyCodeToClipboard = async function(code) {

            if (!navigator.clipboard) {
                return;
            }

            await navigator.clipboard.writeText(code);
        }


        // Initial refresh function - uses normal getList call
        vm.initialRefreshList = function () {
            // Build filter for the specific service category
            let categoryFilter = [{
                field: 'ServiceCategoryTitle',
                operator: 'eq',
                value: vm.parent.serviceCategory.title
            }];

            servicetemplateservice.getList('Active', null, null, vm.resultsPageSize, 1, vm.sortBy, categoryFilter, null).then(result => {
                if (result == undefined || result == null)
                    return;

                vm.servicesList = result.data;
                vm.totalServices = result.total;
                vm.totalFilteredServices = vm.totalServices;
                vm.totalRecords = vm.totalServices;
                vm.showingFromCnt = vm.servicesList.length > 0 ? 1 : 0;
                vm.showingToCnt = vm.servicesList.length;

                // Sort favorited items to the top
                if (vm.servicesList && vm.servicesList.length > 0) {
                    vm.servicesList.sort((a, b) => {
                        if (a.isFavourite && !b.isFavourite) return -1;
                        if (!a.isFavourite && b.isFavourite) return 1;
                        return 0;
                    });
                }
                vm.showMoreBusy = false;
            }).catch(error => {
                console.error('Error loading services:', error);
                vm.showMoreBusy = false;
            });
        };

        // Search functionality with normal getList call
        vm.refreshList = function() {
            // Build combined filter for category and search
            let filters = [{
                field: 'ServiceCategoryTitle',
                operator: 'eq',
                value: vm.parent.serviceCategory.title
            }];

            // Add search filters if search string exists
            if (vm.searchString && vm.searchString.trim() !== "") {
                let searchFilters = [];
                searchFilters.push({ field: 'Description', operator: 'contains', value: vm.searchString, logic: 'or' });
                searchFilters.push({ field: 'ServiceCategoryTitle', operator: 'contains', value: vm.searchString, logic: 'or' });
                searchFilters.push({ field: 'ManufacturerDescription', operator: 'contains', value: vm.searchString });

                // Combine category filter with search filters using AND logic
                filters.push({
                    logic: 'and',
                    filters: searchFilters
                });
            }

            servicetemplateservice.getList('Active', null, null, vm.resultsPageSize, 1, vm.sortBy, filters, null).then(function (result) {
                if (result == undefined || result == null)
                    return;

                vm.servicesList = result.data;
                vm.totalFilteredServices = result.total;
                vm.totalRecords = result.total;
                vm.showingFromCnt = vm.servicesList.length > 0 ? 1 : 0;
                vm.showingToCnt = vm.servicesList.length;

                // Sort favorited items to the top
                if (vm.servicesList && vm.servicesList.length > 0) {
                    vm.servicesList.sort((a, b) => {
                        if (a.isFavourite && !b.isFavourite) return -1;
                        if (!a.isFavourite && b.isFavourite) return 1;
                        return 0;
                    });
                }
                vm.showMoreBusy = false;
            }).catch(error => {
                console.error('Error filtering services:', error);
                vm.showMoreBusy = false;
            });
        }

        // - TABLE FUNCTIONALITY - //

        // Selection functionality
        vm.selectService = function(service) {
            vm.selectedSubstitute = service;
        }

        // - SORTING - //
        vm.orderBy = function (tableState, e, filterOption) {
            console.log(tableState, e, filterOption);
            console.log("Sort is: ", tableState.sort);

            if (tableState.sort && tableState.sort.predicate) {
                vm.sortBy = {
                    field: tableState.sort.predicate,
                    dir: tableState.sort.reverse ? 'desc' : 'asc'
                };

                // Apply sorting to servicesList
                vm.servicesList.sort((a, b) => {
                    let aVal = a[vm.sortBy.field] || '';
                    let bVal = b[vm.sortBy.field] || '';

                    // Handle boolean values (like isFavourite)
                    if (typeof aVal === 'boolean') {
                        aVal = aVal ? 1 : 0;
                        bVal = bVal ? 1 : 0;
                    }

                    // Convert to string for comparison
                    aVal = aVal.toString().toLowerCase();
                    bVal = bVal.toString().toLowerCase();

                    if (vm.sortBy.dir === 'desc') {
                        return bVal.localeCompare(aVal);
                    } else {
                        return aVal.localeCompare(bVal);
                    }
                });
            } else {
                // No sort applied, sort favorited items to the top
                vm.servicesList.sort((a, b) => {
                    if (a.isFavourite && !b.isFavourite) return -1;
                    if (!a.isFavourite && b.isFavourite) return 1;
                    return 0;
                });
            }
        };

        // Action buttons functionality (placeholder for future use)
        vm.hasPermissionForAnyAction = function() {
            return vm.actionButtons && vm.actionButtons.length > 0;
        }

        vm.hasPermissionForAction = function(action) {
            return true; // Placeholder - implement permission logic if needed
        }

        vm.performAction = function(action, service) {
            // Placeholder for action handling
            console.log('Action performed:', action, service);
        }

        // Show more results function - matches Services Database list page pattern
        vm.getMoreResults = function () {
            vm.showMoreBusy = true;
            vm.resultsPageSize += 100;
            vm.refreshList();
        }

        initialize();
    }
})();