(function () {

    'use strict';
    var controllerId = 'EnergyLabsOptimiseController';
    angular.module('app')
        .controller(controllerId, ['$stateParams', '$state', '$rootScope', 'common', 'bootstrap.dialog', 'standardmodelservice', 'projectservice', 'addressservice', 'energylabsservice', '$mdDialog', energyLabsOptimiseController]);

    function energyLabsOptimiseController($stateParams, $state, $rootScope, common, modalDialog, standardmodelservice, projectservice, addressservice, energylabsservice, $mdDialog) {

        // ------------- //
        // - VARIABLES - //
        // ------------- //

        let vm = this;

        vm.projectId = $stateParams.projectId;
        vm.project = null;

        vm.modelList = null;

        vm.filterData = {};
        vm.viewingModels = null;

        vm.isBusy = false;
        vm.mode = $stateParams.standardHomeModelId != null ? 'optimise' : 'select'; // 'select' | 'optimise'
        vm.showResults = false;

        vm.determineHeatCoolResultColour = common.determineELHeatCoolResultColour;

        vm.showDesignInsights = standardmodelservice.showDesignInsights;
        vm.showCostEstimates = standardmodelservice.showCostEstimates;
        vm.toSplitTitleCase = common.toSplitTitleCase;
        vm.initialiseOptions = true;

        const NOT_SPECIFICATION_VARIABLES = standardmodelservice.notSpecificationVariables;
        const PROP_ORDER = standardmodelservice.specPropOrder;

        vm.keyToName = standardmodelservice.keyToName;

        let resultsPageSize = 20;

        // Breadcrumbs
        vm.backToProjects = function () {
            $state.go('energy-labs-parent-menu');
        }

        // -------------- //
        // - INITIALISE - //
        // -------------- //

        async function initialize() {
            projectservice.getProject(vm.projectId).then(async data => {
                if (data == null || data == "" || (data.clientId && !data.projectName)) {
                    // Project not found or no permission - redirect to projects page
                    $state.go('energy-labs-parent-menu');
                    return;
                }
                vm.project = data;
                vm.updateFilteredList({});
                if ($stateParams.standardHomeModelId) {
                    vm.initialiseOptions = false;
                    var model = await standardmodelservice.getStandardModel($stateParams.standardHomeModelId);
                    model.selectedVariation = { standardHomeModelId: $stateParams.variationId };
                    let prefilledData = {
                        suburb: $stateParams.suburb,
                        optionData: await standardmodelservice.getStandardModelOption($stateParams.standardHomeModelOptionId)
                    };
                    // Delete these values so doesn't cause issue where same results show even when changing Variation.
                    delete prefilledData.optionData.standardHomeModelId;
                    delete prefilledData.optionData.standardHomeModelOptionId;
                    prefilledData.optionData.targetEnergyRating = vm.project.energyLabsSettings.targetEnergyRating;
                    for (const key in prefilledData.optionData) {
                        if (standardmodelservice.specPropOrder[key] != null) {
                            prefilledData.optionData[key] = [prefilledData.optionData[key]];
                        }
                    }
                    vm.select(model, prefilledData, true);
                }
            }).catch(error => {
                // Error accessing project - redirect to projects page
                $state.go('energy-labs-parent-menu');
            });
        }

        // ----------------------------------- //
        // - HANDLES (Select Design Process) - //
        // ----------------------------------- //

        // Sort
        vm.updateSort = function (sortBy) {
            vm.modelList = standardmodelservice.applySort(sortBy, vm.modelList);
        }

        // Update filters
        vm.updateFilteredList = function ({ filterData }) {
            vm.currentTotal = null;
            // Get list of variations
            let selectedVariationsForModels = {};
            vm.modelList?.forEach(m => {
                selectedVariationsForModels[m.standardHomeModelId] = m.selectedVariation.standardHomeModelId;
            });
            let designOptions = vm.project.energyLabsSettings.distinctDesignOptions;
            designOptions.features = standardmodelservice.allFeatures;
            designOptions.categories = standardmodelservice.categories;
            vm.modelList = null;
            var phase = $rootScope.$$phase;
            if (!phase) {
                $rootScope.$apply();
            }
            standardmodelservice.getForProjectMultiFiltered(
                vm.projectId,
                {
                    fields: standardmodelservice.multiFiltersFields,
                    distinctDesignOptions: vm.project.energyLabsSettings.distinctDesignOptions,
                    appliedFilters: filterData,
                    selectedVariationsForModels,
                    countParentModelsOnly: true
                }
            ).then(response => {
                vm.modelList = response.parentModelList;
                vm.modelList.forEach(m => {
                    m.selectedForCompare = false;
                    m.defaultVariation = m.selectedVariation;
                });
                vm.filterCountData = response.filterCountData;
                if (filterData == null) {
                    vm.totalWithoutFilters = response.totalItems;
                } else {
                    vm.currentTotal = response.totalItems;
                }
                vm.anyFiltersApplied = standardmodelservice.anyFiltersApplied(filterData);
                vm.initialised = true;
            });
        }

        // Switcher button click, close dropdowns on all other cards
        vm.switcherOptionButtonClicked = function (model) {
            vm.modelList.filter(m => m.standardHomeModelId != model.standardHomeModelId).forEach(m => {
                Object.keys(m.dropdownFields ?? []).forEach(key => {
                    m.dropdownFields[key].expanded = false;
                    m.dropdownFields[key].justExpanded = false;
                });
                m.inOptionsSelectionsMode = false;
            });
        }

        // Select
        vm.select = function (building, prefilledData = null, autoRunCalc = false) {
            standardmodelservice.getStandardModel(building.selectedVariation.standardHomeModelId).then(model => {
                vm.mode = 'optimise';
                building.selectedVariation = model;
                building.include = true;
                vm.viewingModels = [ building ];
                vm.resetBuilding(building.selectedVariation);
                vm.resetBuildingToDefaults(building.selectedVariation);
                if (prefilledData != null) {
                    let optionData = {
                        ...building.selectedVariation.optionData,
                        ...prefilledData.optionData
                    }
                    building.selectedVariation = {
                        ...building.selectedVariation,
                        ...prefilledData,
                        optionData: optionData
                    };
                } else {
                    standardmodelservice.assignDefaults([building.selectedVariation]);
                }
                if (autoRunCalc) {
                    vm.runCalc();
                }
            });
        }

        // ---------------------------- //
        // - HANDLES (Configure Page) - //
        // ---------------------------- //

        // EnergyLabs Properties
        vm.propertiesInOrder = function () {
            const allProps = vm.project?.energyLabsSettings.properties;
            const props = [];
            for(const key in allProps) {
                if (allProps[key] === true && NOT_SPECIFICATION_VARIABLES.find(x => x === key) == null) {
                    props.push(key);
                }
            }
            props.sort((a, b) => {
                const ax = PROP_ORDER[a];
                const bx = PROP_ORDER[b];
                if (ax == null)
                    throw "Detected property not found in PROP_ORDER. This indicates a new variable has been added to the extraction process without updating the corresponding logic. Property was: " + a;
                if (bx == null)
                    throw "Detected property not found in PROP_ORDER. This indicates a new variable has been added to the extraction process without updating the corresponding logic. Property was: " + b;
                return ax - bx;
            });
            return props;
        }

        // Go back to selecting Model
        vm.reset = function () {
            vm.mode = 'select';
            vm.compare = false;
            vm.showResults = false;
            vm.initialiseOptions = true;

            vm.modelList.forEach(model => {
                model.selected = false;
                model.optionData = {};
            });

            vm.viewingModels = null;
            vm.filterData = {};
            vm.updateFilteredList({ filterData: vm.filterData });
        }

        // Reset to defaults
        vm.resetBuildingToDefaults = function (building, ignoreSuburbData = false) {
            let origNorthOffset = building.optionData.northOffset;
            let origNatHers = building.optionData.natHERSClimateZone;
            standardmodelservice.assignDefaults([building]);
            if (ignoreSuburbData) {
                building.optionData.northOffset = origNorthOffset;
                building.optionData.natHERSClimateZone = origNatHers;
            } else {
                // Suburb
                if (vm.project.lockWOHLocation && vm.project.suburb != null) {
                    building.suburb = vm.project.suburbName;
                    // Get nathers zone from suburb
                    addressservice.climateZoneCodeByPostCode(vm.project.suburb.postcode).then(climateZone => {
                        building.optionData.natHERSClimateZone = climateZone.slice(3);
                    });
                } else {
                    building.suburb = null;
                    building.suburbObject = null;
                }
                building.modelBlockShowError = false;
                // State
                if (vm.project.lockWOHLocation && vm.project.stateCode != null) {
                    building.stateCode = vm.project.stateCode;
                } else {
                    building.stateCode = null;
                }
                if (vm.energyLabsForm != null) {
                    vm.energyLabsForm.$setPristine();
                    vm.energyLabsForm.$setUntouched();
                }
            }
            building.optionData.targetEnergyRating = vm.project.energyLabsSettings.targetEnergyRating;
            building.optionData.costEstimateEnabledDefault = building.costEstimateEnabled;
            for (const key in vm.project.energyLabsSettings.properties) {
                if (!standardmodelservice.notSpecificationVariables.includes(key)) {
                    building.optionData[key] = ['Any'];
                }
            }
            vm.showResults = false;
        }

        // Clear
        vm.resetBuilding = function (building, ignoreSuburbData = false) {
            if (!ignoreSuburbData) {
                // Only clear natHERSCliamteZone, suburb and state if lockWOHLocation toggled off
                if (!vm.project.lockWOHLocation) {
                    building.optionData = {};
                    building.suburb = null;
                    building.suburbObject = null;
                    building.stateCode = null;
                } else if (!ignoreSuburbData) {
                    building.optionData = { natHERSClimateZone: building.optionData?.natHERSClimateZone };
                }
            }
            if (vm.energyLabsForm != null) {
              vm.energyLabsForm.$setPristine();
              vm.energyLabsForm.$setUntouched();
            }
            for (const key in vm.project.energyLabsSettings.properties) {
                if (!standardmodelservice.notSpecificationVariables.includes(key)) {
                    building.optionData[key] = ['Any'];
                }
            }
            building.modelBlockShowError = false;
            vm.showResults = false;
        }

        // Run
        vm.runCalc = async function() {

            if(vm.isBusy)
                return;

            try  {

                vm.isBusy = true;

                // Clear old results
                vm.viewingModels.forEach(model => model.selectedVariation.optionPerformance = null);

                var optionData = vm.viewingModels.map(x => {
                    // If any 'Any' selected, replace with actual list of options
                    let serviceOptionData = common.getOptionsForAnySelected(x.selectedVariation.variableOptions, x.selectedVariation.optionData);
                    return { standardHomeModelId: x.selectedVariation.standardHomeModelId, ...serviceOptionData, stateCode: x.selectedVariation.stateCode };
                });

                let results = await standardmodelservice.optimise(vm.project.projectId, optionData);

                for(let i = 0; i < vm.viewingModels.length; i++) {

                    // For each result for this building
                    for (let j = 0; j < results[i].results.length; j++) {
                        let standardModelResult = angular.copy(vm.viewingModels[i].selectedVariation);
                        standardModelResult.optionData = results[i].results[j];

                        // Copy drawing files and floorplanner link from the variation to the result
                        results[i].results[j].standardHomeModelFiles = vm.viewingModels[i].selectedVariation.standardHomeModelFiles;
                        results[i].results[j].floorplannerLink = vm.viewingModels[i].selectedVariation.floorplannerLink;
                        results[i].results[j].view3dFloorPlans = vm.viewingModels[i].selectedVariation.view3dFloorPlans;

                        const linked = standardmodelservice.linkOptionsToEstimates(standardModelResult, vm.project.energyLabsSettings.properties);
                        let costEstimateData = {};
                        for(const key in vm.project.energyLabsSettings.properties) {
                            let costEstimate = linked.find(x => x.category == key);
                            if (costEstimate != null) {
                                if (costEstimate.rounding != null) {
                                    costEstimateData[key] = common.roundUpInt((costEstimate.quantity * costEstimate.ratePerUnit * (1 + (costEstimate.margin / 100))), costEstimate.rounding);
                                } else {
                                    costEstimateData[key] = (costEstimate.quantity * costEstimate.ratePerUnit * (1 + (costEstimate.margin / 100)));
                                }
                            } else {
                                costEstimateData[key] = 0;
                            }
                        }

                        costEstimateData.totalCost = linked?.map(x => Number(common.roundUpInt((x.quantity * x.ratePerUnit * (1 + (x.margin / 100))), x.rounding)))?.reduce((a,b) => a + b, 0) || 0;
                        results[i].results[j].costEstimateData = costEstimateData;
                    }

                    // Sort results
                    results.forEach(result => {
                        result.results = result.results.sort( (a,b) => {
                            if (!vm.viewingModels[i].selectedVariation.optionData.costEstimateEnabledDefault || a.costEstimateData.totalCost == b.costEstimateData.totalCost) {
                                return a.totalEnergyLoad < b.totalEnergyLoad ? -1 : 1
                            } else {
                                return a.costEstimateData.totalCost < b.costEstimateData.totalCost ? -1 : 1
                            }
                        });
                    })

                    vm.viewingModels[i].selectedVariation.optionPerformance = results[i];
                }

                showResultsByPage();

                vm.showResults = true;
                vm.isBusy = false;

                // Wait, then ensure 'design insight' cards are the same height...
                setTimeout(() => {

                    const cards = Array.from(document.getElementsByClassName("el-design-card-identifier"));
                    cards.forEach(card => card.style.height = 'auto');
                    const heights = cards.map(x => x.offsetHeight);

                    const maxHeight = Math.max(...heights);
                    cards.forEach(card => card.style.height = maxHeight + 'px');

                }, 100);

            } catch(e) {
                throw e;
            } finally {
                vm.isBusy = false;
            }

        }

        // Organise results
        function showResultsByPage() {
            for(let i = 0; i < vm.viewingModels.length; i++) {
                vm.viewingModels[i].selectedVariation.optionPerformance.resultsFull = vm.viewingModels[i].selectedVariation.optionPerformance.results;
                vm.viewingModels[i].selectedVariation.optionPerformance.resultsPaged = vm.viewingModels[i].selectedVariation.optionPerformance.resultsFull.slice(0, resultsPageSize);
            }
        }

        // Any results 'Launch' dropdowns expanded
        vm.anyLaunchDropdownsExpanded = function () {
            let check = vm.viewingModels.some(m => m.selectedVariation.optionPerformance?.resultsPaged.some(o => o.launchExpanded));
            return check;
        }

        // Collapse all results 'Launch' dropdowns
        vm.collapseAllLaunchDropdowns = function () {
            vm.viewingModels.forEach(m => m.selectedVariation.optionPerformance?.resultsPaged.forEach(o => o.launchExpanded = false));
        }

        vm.goToOtherTool = async function(toolName, option) {
          let result = await modalDialog.confirmationDialog(
            "Confirm",
            "Continuing will result in the data on this page to be lost. Are you sure you want to proceed?",
            "Proceed",
            "Cancel");

          if (result !== null) {
            $state.go(`energy-labs-${toolName}`, {
              projectId: vm.project.projectId,
              standardHomeModelId: vm.viewingModels[0].selectedVariation.isVariationOfHomeModelId,
              variationId: vm.viewingModels[0].selectedVariation.standardHomeModelId,
              standardHomeModelOptionId: option.standardHomeModelOptionId,
              suburb: vm.viewingModels[0].selectedVariation.suburb,
              stateCode: vm.viewingModels[0].selectedVariation.stateCode,
              nccClimateZone: vm.viewingModels[0].selectedVariation.optionData.nccClimateZone
            });
          }
        }

        // Clear results
        vm.clearComparison = function () {
            vm.showResults = false;
        }

        // Variation changes
        vm.variationChanged = function (parentModel) {
            vm.clearComparison();
            vm.resetBuilding(parentModel.selectedVariation, true);
            vm.resetBuildingToDefaults(parentModel.selectedVariation, true);
            // Set whether "multi-orientate" option should be available
            let allNorthOffsetOptions = [0, 45, 90, 135, 180, 225, 270, 315];
            vm.viewingModels.forEach(m => {
                if (allNorthOffsetOptions.every(o => m.selectedVariation.variableOptions.northOffset.map(no => no).includes(o)) && !m.selectedVariation.variableOptions.northOffset.includes("Multi-Orientation")) {
                    m.selectedVariation.variableOptions.northOffset.push("Multi-Orientation");
                }
            });
        }

        // Copy single option across
        vm.copyOptionAcross = function (copyAcrossData) {
            // IF copying suburb
            if (copyAcrossData.field == 'suburb') {
                vm.viewingModels.forEach(building => {
                    building.suburbObject = copyAcrossData.option;
                    energylabsservice.setBlockDataFromSuburb(
                        building,
                        copyAcrossData.option,
                        building.variableOptions,
                        () => vm.clearComparison()
                    );
                });
            }
            // ELSE do normal copy
            else {
                vm.viewingModels.forEach(building => {
                    if (building.variableOptions[copyAcrossData.field]?.includes(copyAcrossData.option)) {
                        building.optionData[copyAcrossData.field] = copyAcrossData.option;
                    }
                });
            }
        }

        // Copy all data to another building
        vm.copyFromTo = function (fromBuilding, toBuilding) {
            toBuilding.optionData = angular.copy(fromBuilding.optionData);
            toBuilding.stateCode = fromBuilding.stateCode;
            toBuilding.suburb = fromBuilding.suburb;
            toBuilding.suburbObject = fromBuilding.suburbObject;
            vm.clearComparison();
        }

        // Expand result
        vm.expandResult = function (building, option) {
            let wasExpanded = option.uiExpanded;
            building.optionPerformance.results.forEach(x => {
                x.uiExpanded = false;
                x.launchExpanded = false;
            });
            option.uiExpanded = !wasExpanded;
        }

        // Load more results
        vm.getMoreResults = function () {
            resultsPageSize += 20;
            showResultsByPage();
        }

        // Open 3D Model modal
        vm.open3dViewerModal = function (option, event) {
            if (event) event.stopPropagation();
            // Collapse the LAUNCH menu immediately when modal opens
            vm.collapseAllLaunchDropdowns();
            var modalScope = $rootScope.$new();
            modalScope.floorplannerLink = option.floorplannerLink;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-plan-3d-viewer-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
            });
        }

        // Open Floor Plan modal
        vm.openSwitcherModal = function (option, event) {
            if (event) event.stopPropagation();
            // Collapse the LAUNCH menu immediately when modal opens
            vm.collapseAllLaunchDropdowns();
            var modalScope = $rootScope.$new();
            modalScope.planViews = option.standardHomeModelFiles;
            modalScope.currentIndex = 0;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-plan-switcher-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
            });
        }

        // ------------------ //
        // - RUN INITIALISE - //
        // ------------------ //

        initialize();
    }
})();