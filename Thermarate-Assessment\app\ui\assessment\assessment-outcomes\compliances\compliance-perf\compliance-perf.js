(function () {
    'use strict';
    angular
        .module('app')
        .component('compliancePerf', {
            bindings: {
                assessment: '<',
                complianceData: '<',
                isOption: '<',
                jobFiles: '<',
                assessmentSoftwareList: '<',
                isLocked: '<',
                purchaseOrderDefault: '<',
                complianceMethodList: '<',
                complianceTypeChanged: '&',
                onComplianceChanged: '&'
            },
            templateUrl: 'app/ui/assessment/assessment-outcomes/compliances/compliance-perf/compliance-perf.html',
            controller: CompliancePerf,
            controllerAs: 'vm'
        });

    CompliancePerf.$inject = ['$rootScope', '$scope', 'clientservice',
        'compliancemethodservice', 'common', 'security', '$mdDialog', 'certificationservice'];

    function CompliancePerf($rootScope, $scope, clientservice, 
            compliancemethodservice, common, securityservice, $mdDialog, certificationservice) {

        var vm = this;
        var _formValid = false;

        vm.calculateCompliance = calculateCompliance;
        vm.calculateTotal = calculateTotal;

        vm.purchaseOrderList = clientservice.purchaseOrderSettingsList;

        vm.$onInit = onInit;
        vm.$onChanges = onChanges;

        // Permissions
        vm.permission_field_certification_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__certification__view']);
        vm.permission_field_certification_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__certification__edit']);
        vm.permission_field_assessmentmethod_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentmethod__view']);
        vm.permission_field_assessmentmethod_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentmethod__edit']);
        vm.permission_field_assessmentsoftware_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentsoftware__view']);
        vm.permission_field_assessmentsoftware_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentsoftware__edit']);
        vm.permission_field_herrequired_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__herrequired__view']);
        vm.permission_field_herrequired_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__herrequired__edit']);
        vm.permission_action_overrideenergyloads = securityservice.immediateCheckRoles(['assessment_actions__overrideenergyloads']);
        vm.permission_field_valid_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__valid__view']);
        vm.permission_field_valid_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__valid__edit']);
        vm.permission_field_showtoclient_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__showtoclient__view']);
        vm.permission_field_showtoclient_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__showtoclient__edit']);

        vm.sectorDeterminationList = [];
        let sectorsPromise = certificationservice.getSectorDeterminations()
            .then(data => {
                vm.sectorDeterminationList = data;
            });

        vm.certificationList = [];
        var certificationPromise = certificationservice.getList()
            .then(function (data) {
                vm.allCertificationList = data.data;
                vm.certificationList = vm.allCertificationList;
            });

        // Required to recalculate compliance
        let unwatchA = $scope.$watch('vm.complianceData.proposed.conditionedFloorArea', calculateCompliance);
        let unwatchB = $scope.$watch('vm.complianceData.proposed.unconditionedFloorArea', calculateCompliance);
        let unwatchC = $scope.$watch('vm.complianceData.proposed.attachedGarageFloorArea', calculateCompliance);
        let unwatchD = $scope.$watch('vm.complianceData.reference.conditionedFloorArea', calculateCompliance);
        let unwatchE = $scope.$watch('vm.complianceData.reference.unconditionedFloorArea', calculateCompliance);
        let unwatchF = $scope.$watch('vm.complianceData.reference.attachedGarageFloorArea', calculateCompliance);
        let unwatchG = $scope.$watch('vm.complianceData.proposed.scratchFileProcessedWatchProperty', calculateCompliance);
        let unwatchH = $scope.$watch('vm.complianceData.reference.scratchFileProcessedWatchProperty', calculateCompliance);

        $scope.$on("$destroy", () => {
            if(unwatchA != null)
                unwatchA();

            if(unwatchB != null)
                unwatchB();

            if(unwatchC != null)
                unwatchC();

            if(unwatchD != null)
                unwatchD();

            if(unwatchE != null)
                unwatchE();

            if(unwatchF != null)
                unwatchF();

            if(unwatchG != null)
                unwatchG();

            if(unwatchH != null)
                unwatchH();
        });

        function onInit() {
            if (vm.assessment != null) {
                calculateCompliance();
            }
            $scope.$watch('compliancePerfForm.$valid', function (newVal) {
                _formValid = newVal;
                calculateCompliance();
            });

            $scope.$watch('vm.assessment.nccClimateZoneCode', function (newVal) {
                calculateCompliance();
            });

            if (vm.complianceData.assessmentSoftwareCode == null && vm.assessmentSoftwareList !== undefined) {
                vm.complianceData.assessmentSoftwareCode = vm.assessment.assessmentSoftwareCode;
            }

            vm.complianceData.availableSoftware = compliancemethodservice.determineAvailableSoftware(vm.assessmentSoftwareList, vm.complianceData.complianceMethod.complianceMethodCode);
        }
        function onChanges(changes) {

            if (changes.complianceData && changes.complianceData.currentValue) {
                calculateCompliance();
            }
        }

        // Performance Solution (VURB) is compliant when
        // Compliance is achieved if the following conditions are met:
        //   1.When NCC Climate Zone = 1 OR 2: AND ProposedBuilding Cooling Load(kWh / m�) <= ReferenceBuildingCooling Load(kWh / m�)
        //   2.When NCC Climate Zone = 7 OR 8: AND ProposedBuilding Heating Load(kWh / m�) <= ReferenceBuildingHeating Load(kWh / m�) 
        //   3.When NCC Climate Zone = 3, 4, 5 OR 6: AND ProposedBuilding Heating Load(kWh / m�) <= ReferenceBuildingHeating Load(kWh / m�) AND ProposedBuilding Cooling Load(kWh / m�) <= ReferenceBuildingCooling Load(kWh / m�)
        // AND ALL FORM FIELDS ARE FILLED!
        function calculateCompliance() {

            let compliant = compliancemethodservice.calculatePerfCompliance(vm.assessment, vm.complianceData);

            // Form must also be filled. To prevent this from un-selecting already selected option
            // (e.g. when the form is loading on startup) we skip this check if the item is ALREADY selected.
            if (_formValid == false && !vm.complianceData.isSelected)
                compliant = false;

            vm.complianceData.isCompliant = compliant;
            if (!compliant) {
                vm.complianceData.isSelected = false;
            }

            // If we're compliant AND it's NO OPTIONS 
            // OR
            // If this is the baseline and we're compliant
            // Then-select the baseline
            if ((compliant && vm.assessment.allComplianceOptions.length == 1) ||
                compliant && vm.complianceData.isBaselineSimulation == true)
                vm.complianceData.isSelected = true;

            vm.onComplianceChanged({ compliant: compliant });
        }

        function calculateTotal(building) {
            building.totalEnergyLoad = building.heating + building.cooling;

            // force refresh of input to run 'formatted-number' directive...
            common.forceBlurInputWithId("proposedTotalEnergyLoadInput" + vm.complianceData.complianceOptionsId);
            common.forceBlurInputWithId("referenceTotalEnergyLoadInput" + vm.complianceData.complianceOptionsId);
        }

        vm.determineUINumber = function () {
            return vm.complianceData.optionIndex;
        }

        vm.classificationChanged = function (value) {
            vm.complianceData.classification = value;
        }

        vm.copyProposedAreaToReference = function() {

            // Atm we are only copying these values.
            vm.complianceData.reference.conditionedFloorArea = vm.complianceData.proposed.conditionedFloorArea;
            vm.complianceData.reference.unconditionedFloorArea = vm.complianceData.proposed.unconditionedFloorArea;
            vm.complianceData.reference.attachedGarageFloorArea = vm.complianceData.proposed.attachedGarageFloorArea;

            //... We need to do this so that the focus/unfocus cycle will run and the numbers get
            // formatted correctly.
            setTimeout(() => {

                document.getElementById("reference-unconditioned-floor-area").focus();
                document.getElementById("reference-unconditioned-floor-area").blur();

                document.getElementById("reference-garage-floor-area").focus();
                document.getElementById("reference-garage-floor-area").blur();

                document.getElementById("reference-conditioned-floor-area").focus();
                document.getElementById("reference-conditioned-floor-area").blur();

            }, 50);

        }

        vm.copyProposedEnergyToReference = function() {

            // Atm we are only copying these values.
            vm.complianceData.reference.heating = vm.complianceData.proposed.heating;
            vm.complianceData.reference.cooling = vm.complianceData.proposed.cooling;
            vm.complianceData.reference.totalEnergyLoad = vm.complianceData.proposed.totalEnergyLoad;
            vm.complianceData.reference.overrideEnergyLoads = vm.complianceData.proposed.overrideEnergyLoads || false;
            vm.complianceData.reference.houseEnergyRating = vm.complianceData.proposed.houseEnergyRating;
            vm.complianceData.reference.houseEnergyRatingOverride = vm.complianceData.proposed.houseEnergyRatingOverride;
        }

        vm.isSoftwareAvailable = function (code) {
            return vm.complianceData.availableSoftware?.find(x => x.assessmentSoftwareCode === code) != null;
        }

        vm.availableComplianceMethods = function() {
            return compliancemethodservice.determineAvailableComplianceMethods(vm.complianceMethodList, vm.assessment.worksDescription.worksDescriptionCode);
        }

        vm.showLoadsUpdateModal = function(building, buildingType, title) {

            let modalScope = $rootScope.$new();

            modalScope.title = title;
            modalScope.disabled = vm.isLocked;
            modalScope.assessment = vm.assessment;
            modalScope.option = vm.complianceData;
            modalScope.building = building;
            modalScope.buildingType = buildingType;
            modalScope.energyLoadUnits = vm.complianceData.assessmentSoftware.energyLoadUnits;

            // Modal Inputs
            modalScope.heating = building.heating;
            modalScope.cooling = building.cooling;
            modalScope.totalEnergyLoad = building.totalEnergyLoad;

            // We only care about the override, which could be null..?
            // Can mean equivalent OR calculated.
            modalScope.herOverride = building.houseEnergyRatingOverride;

            modalScope.heatingOriginal = building.heatingOriginal;
            modalScope.coolingOriginal = building.coolingOriginal;
            modalScope.totalEnergyLoadOriginal = building.totalEnergyLoadOriginal;

            modalScope.overrideEnergyLoads = building.overrideEnergyLoads || false;

            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/assessment/assessment-outcomes/compliance-options/loads-override-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(data => {
                // console.log("outside", data);

                building.heating = data.heating;
                building.cooling = data.cooling;
                building.totalEnergyLoad = data.totalEnergyLoad;
                building.overrideEnergyLoads = data.overrideEnergyLoads || false;
                building.houseEnergyRatingOverride = data.herOverride;

                calculateCompliance();

            });

        }

        vm.updateDataLinkedToCertification = function() {
            setTimeout(() => {
                certificationservice.updateDataLinkedToCertification(vm.assessment, vm.complianceData);
            }, 100);
        }

    }
})();