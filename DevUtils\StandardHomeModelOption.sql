SELECT TOP(100)
       [homeModelOption].[StandardHomeModelOptionId]
      ,[homeModelOption].[StandardHomeModelId]
	  ,[homeModel].[Title] [__HomeModel]
	  ,[client].[ClientName] [__Client]
      ,[homeModelOption].[NatHERSClimateZone]
      ,[homeModelOption].[NorthOffset]
      ,[homeModelOption].[BlockType]
      ,[homeModelOption].[RoofConstruction]
      ,[homeModelOption].[RoofInsulation]
      ,[homeModelOption].[RoofSolarAbsorptance]
      ,[homeModelOption].[CeilingConstruction]
      ,[homeModelOption].[CeilingInsulation]
      ,[homeModelOption].[ExteriorWallConstruction]
      ,[homeModelOption].[ExteriorWallInsulation]
      ,[homeModelOption].[ExteriorWallSolarAbsorptance]
      ,[homeModelOption].[InteriorWallConstruction]
      ,[homeModelOption].[InteriorWallInsulation]
      ,[homeModelOption].[ExteriorGlazing]
      ,[homeModelOption].[ExteriorGlazingFrameSolarAbsorptance]
      ,[homeModelOption].[FloorConstruction]
      ,[homeModelOption].[FloorInsulation]
      ,[homeModelOption].[FloorCoverings]
      ,[homeModelOption].[HeatingLoad]
      ,[homeModelOption].[CoolingLoad]
      ,[homeModelOption].[TotalEnergyLoad]
      ,[homeModelOption].[EnergyRating]
      ,[homeModelOption].[Row]
      ,[homeModelOption].[Description]
      ,[homeModelOption].[Comments]
      ,[homeModelOption].[Active]
      ,[homeModelOption].[AssessmentMethod]
      ,[homeModelOption].[SiteExposure]
      ,[homeModelOption].[FloorHeight]
      ,[homeModelOption].[InteriorWallSolarAbsorptance]
      ,[homeModelOption].[FloorSolarAbsorptance]
      ,[homeModelOption].[CeilingFans]
      ,[homeModelOption].[RecessedLightFittings]
      ,[homeModelOption].[ExteriorDoorSolarAbsorptance]
      ,[homeModelOption].[GarageDoorSolarAbsorptance]
  FROM [dbo].[RSS_StandardHomeModelOption] [homeModelOption]
  INNER JOIN [dbo].[RSS_StandardHomeModel] [homeModel] ON [homeModelOption].[StandardHomeModelId] = [homeModel].[StandardHomeModelId]
  LEFT JOIN [dbo].[RSS_Client] [client] ON [homeModel].[ClientId] = [client].[ClientId]
  WHERE 1=1
	--AND [homeModel].[Deleted] = 0
	--AND [homeModel].[ClientId] = 'B0139FEE-0D56-6D81-4442-3A0B3032EF11'
	--AND [homeModel].[StandardHomeModelId] = '38A5F060-09CB-4E0E-AF60-EB8D71E3C18C'
	AND [homeModel].[ProjectId] = '4fdc16f0-9e35-416a-aeaa-a715d0efaaa9'
  ORDER BY [homeModel].[CreatedOn] DESC