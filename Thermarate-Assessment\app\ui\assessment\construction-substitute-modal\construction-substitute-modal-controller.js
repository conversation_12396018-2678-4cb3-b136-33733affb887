(function () {

    'use strict';

    let controllerId = 'constructionSubstituteModalController';

    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state', '$q', '$timeout',
            'bootstrap.dialog', 'uuid4',
            'constructionservice',
            'manufacturerservice',
            'daterangehelper',
            'standardmodelservice',
            'common',
            constructionSubstituteModalController]);

    function constructionSubstituteModalController($rootScope, $scope, $mdDialog, $stateParams, $state, $q, $timeout,
                                      modalDialog, uuid4,
                                      constructionservice,
                                      manufacturerservice,
                                      daterangehelper,
                                      standardmodelservice,
                                      common) {

        // The model for this form
        const vm = this;

        // Modal properties
        vm.parent = $scope.parent;
        vm.building = $scope.building;
        vm.option = $scope.option;
        vm.disabled = $scope.disabled;

        // Construction Database properties - copied from construction-list-controller.js
        vm.isBusy = true;
        vm.constructionList = [];
        vm.listFilter = "";
        // Use the same logic as construction.js service to determine template type
        const categoryCode = vm.parent?.category?.constructionCategoryCode;
        const categoryType = vm.parent?.category?.type;

        console.log('Parent category info:', {
            categoryCode: categoryCode,
            categoryType: categoryType,
            parent: vm.parent
        });

        // Opening categories: type != 'surface' OR specifically ExteriorDoor, InteriorDoor, or PermanentOpening
        const isOpeningCategory = categoryType !== 'surface' ||
                                  categoryCode === 'ExteriorDoor' ||
                                  categoryCode === 'InteriorDoor' ||
                                  categoryCode === 'PermanentOpening';

        vm.templateType = isOpeningCategory ? 'Opening' : 'Construction';

        console.log('Template type determined:', vm.templateType, 'isOpeningCategory:', isOpeningCategory);

        // Determine specific category type for column configuration
        vm.categoryType = 'construction'; // default
        if (vm.templateType.toLowerCase() === 'opening') {
            if (categoryCode === 'ExteriorGlazing' || categoryCode === 'InteriorGlazing' ||
                categoryCode === 'Skylight' || categoryCode === 'RoofWindow') {
                vm.categoryType = 'glazing';
            } else if (categoryCode === 'ExteriorDoor' || categoryCode === 'InteriorDoor') {
                vm.categoryType = 'door';
            } else if (categoryCode === 'HorizontalOpening' || categoryCode === 'VerticalOpening') {
                vm.categoryType = 'opening';
            }
        }

        console.log('Category type for columns:', vm.categoryType);

        // Set modal title and category filtering based on category
        const displayDescription = vm.parent.overrideDisplayDescription || vm.parent.displayDescription || vm.parent.description;
        vm.modalTitle = `Substitute "${displayDescription}"`;
        vm.categoryFilter = null; // Will hold the categories to filter by

        if (vm.templateType.toLowerCase() === 'opening') {
            // Always filter by the specific category code only, not groups of related categories
            vm.categoryFilter = [categoryCode];
        } else if (vm.templateType.toLowerCase() === 'construction') {
            // Apply the same category filtering logic for construction types
            vm.categoryFilter = [categoryCode];
        }

        // Multi-filter properties - matches Construction Database page exactly
        vm.searchString = null;
        vm.searchStringOld = null;
        vm.searchFields = ['description', 'constructionCategoryTitle', 'constructionSubCategoryTitle', 'openingStyleTitle', 'manufacturerDescription', 'insulationDescription', 'glassTypeTitle', 'glassColourTitle', 'lowECoating', 'frameMaterialTitle'];

        // Initialize filters based on template type and category
        vm.filters = [];

        if (vm.templateType.toLowerCase() === 'construction') {
            // Construction filters
            vm.filters = [
                { section: 1, name: 'constructionType', field: 'constructionSubCategoryTitle' },
                { section: 1, name: 'insulation', field: 'insulationDescription' }
            ];
        } else if (vm.templateType.toLowerCase() === 'opening') {
            // Get the category code for opening-specific filtering
            const categoryCode = vm.parent?.category?.constructionCategoryCode;

            // Category-specific filters
            if (categoryCode === 'ExteriorGlazing' || categoryCode === 'InteriorGlazing' ||
                categoryCode === 'Skylight' || categoryCode === 'RoofWindow') {
                // Glazing categories: Manufacturer, Frame, Opening Style, Glass Type, Glass Colour, Low-E (in this exact order)
                vm.filters = [
                    { section: 1, name: 'manufacturer', field: 'manufacturerDescription' },
                    { section: 1, name: 'frame', field: 'frameMaterialTitle' },
                    { section: 1, name: 'constructionType', field: 'openingStyleTitle' },
                    { section: 1, name: 'glassType', field: 'glassTypeTitle' },
                    { section: 1, name: 'glassColour', field: 'glassColourTitle' },
                    { section: 1, name: 'low-E', field: 'lowECoating' }
                ];
            } else {
                // Base filters for all other openings
                vm.filters = [
                    { section: 2, name: 'manufacturer', field: 'manufacturerDescription' },
                    { section: 1, name: 'constructionType', field: 'openingStyleTitle' }
                ];
            }

            if (categoryCode === 'ExteriorDoor' || categoryCode === 'InteriorDoor') {
                // Door categories: Manufacturer, Insulation, Opening Style
                vm.filters.push({ section: 1, name: 'insulation', field: 'insulationDescription' });
            }
            // For HorizontalOpening and VerticalOpening: only Manufacturer and Opening Style (already added above)
        }

        // Initialize filter-related properties following Construction Database page pattern
        vm.filteredFilters = vm.filters.filter(f => f.name != null && f.name != "[blank]" && f.name != "[moreLessButton]");
        vm.filtersExpanded = false;
        vm.appliedFilters = {};
        vm.appliedFiltersOld = {};
        vm.filterCountData = {};
        vm.cachedFilterCounts = {}; // Cache for filter counts
        vm.cachedGroupCounts = {}; // Cache for group counts
        vm.cachedGroupOptions = {}; // Cache for group options
        vm.filterCountLookup = null; // Lookup map for faster filter count access
        vm.isTogglingFilters = false; // Flag to prevent API calls during filter expansion
        vm.filtersApplied = false;
        vm.totalFilteredConstructions = 0;
        vm.totalConstructions = 0;
        vm.totalRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.initialiseComplete = false;
        vm.showDisplayDesc = false;
        vm.showMoreBusy = false;

        // Pagination variables
        vm.resultsPageSize = 100;

        // Get all filters with group headings removed and add category filter if needed
        vm.filteredAppliedFilters = function () {
            let newFilters = angular.copy(vm.appliedFilters);
            Object.keys(newFilters).forEach(fieldName => {
                if (vm.filters.find(f => f.field == fieldName)?.groups != null) {
                    newFilters[fieldName] = newFilters[fieldName].filter(o => !o.startsWith("optionGroup"));
                }
            });

            return newFilters;
        }

        // Common service functions
        vm.keyToName = function(key) {
            // Override for Opening Database page - show "Opening Style" instead of "Construction Type"
            if (key === 'constructionType' && vm.templateType.toLowerCase() === 'opening') {
                return 'Opening Style';
            }
            return standardmodelservice.keyToName(key);
        };
        vm.getFilterSelectedText = common.getMultiFilterSelectedText;
        vm.optionName = common.getOptionName;
        vm.anyOptionsSelectedOnField = common.anyOptionsSelectedOnField;
        vm.anyFiltersApplied = common.anyFiltersApplied;

        // Clear filter functions
        vm.clearFilter = function (filter) {
            vm.appliedFilters[filter.field] = filter.isDate ? daterangehelper.getDefaultRange('All Time') : ['Any'];
            vm.filtersApplied = vm.anyFiltersApplied(vm.searchString, vm.filteredFilters, vm.appliedFilters);
            vm.refreshList();
        }

        vm.clearFilters = function () {
            vm.appliedFilters = {};
            vm.appliedFiltersOld = {};
            vm.filterCountData = {};
            vm.searchString = null;
            vm.searchStringOld = null;
            vm.filters.forEach(filter => {
                vm.appliedFilters[filter.field] = ['Any'];
            });
            vm.filtersApplied = false;
            vm.initialRefreshList();
        }

        // Filter count functions - matches Construction Database page exactly
        vm.clearFilterCaches = function() {
            vm.cachedFilterCounts = {};
            vm.cachedGroupCounts = {};
            vm.cachedGroupOptions = {};
            vm.filterCountLookup = null;
        }

        vm.updateFilterCountLookup = function() {
            vm.filterCountLookup = {};
            if (!vm.filterCountData) return;

            Object.keys(vm.filterCountData).forEach(fieldName => {
                vm.filterCountLookup[fieldName] = {};
                if (vm.filterCountData[fieldName]) {
                    Object.keys(vm.filterCountData[fieldName]).forEach(key => {
                        vm.filterCountLookup[fieldName][key.toLowerCase()] = vm.filterCountData[fieldName][key];
                    });
                }
            });
        }

        vm.getFilterCount = function(fieldName, value) {
            if (!value || !fieldName || !vm.filterCountData[fieldName]) return 0;

            let cacheKey = `${fieldName}_${value.toString().toLowerCase()}`;
            if (vm.cachedFilterCounts[cacheKey] !== undefined) {
                return vm.cachedFilterCounts[cacheKey];
            }

            if (vm.filterCountLookup && vm.filterCountLookup[fieldName]) {
                const count = vm.filterCountLookup[fieldName][value.toString().toLowerCase()];
                vm.cachedFilterCounts[cacheKey] = count || 0;
                return count || 0;
            }

            let valueKey = Object.keys(vm.filterCountData[fieldName]).find(option =>
                option.toLowerCase() === value.toString().toLowerCase());
            const count = valueKey ? vm.filterCountData[fieldName][valueKey] : 0;
            vm.cachedFilterCounts[cacheKey] = count;
            return count;
        }

        // Date range setup - matches Construction Database page exactly
        var persistRangeName = "constructionList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('All Time', persistRangeName);

        // Initialize filters
        vm.filters.forEach(filter => {
            vm.appliedFilters[filter.field] = ['Any'];
        });
        vm.appliedFiltersOld = angular.copy(vm.appliedFilters);

        // Initialize multi-filters - matches Construction Database page exactly
        vm.initialRefreshList = function () {
            vm.initialiseComplete = false;

            console.log('Template type:', vm.templateType);
            console.log('Category filter:', vm.categoryFilter);
            console.log('Filtered filters:', vm.filteredFilters);

            // Get applied filters without category filter - category will be passed separately
            let combinedAppliedFilters = angular.copy(vm.filteredAppliedFilters());

            // For both opening and construction types, pass category as separate parameter instead of in combinedAppliedFilters
            let categoryCodeParam = null;
            if ((vm.templateType.toLowerCase() === 'opening' || vm.templateType.toLowerCase() === 'construction') && vm.categoryFilter && vm.categoryFilter.length > 0) {
                categoryCodeParam = vm.categoryFilter[0]; // Use the first (and should be only) category code
            }

            console.log('Applied filters for initial load:', combinedAppliedFilters);
            console.log('Category code parameter:', categoryCodeParam);

            Promise.all([
                constructionservice.getListMultiFiltered(vm.resultsPageSize, 1, vm.sortBy, vm.filteredFilters, {}, combinedAppliedFilters, null, vm.templateType.toLowerCase(), categoryCodeParam),
                constructionservice.getMultiFilterOptions(vm.filteredFilters, vm.templateType.toLowerCase(), categoryCodeParam)
            ]).then(results => {
                // Process constructions data (first promise result)
                const constructionsResult = results[0];
                vm.constructionList = constructionsResult.data;
                vm.totalConstructions = constructionsResult.total;
                vm.totalFilteredConstructions = vm.totalConstructions;
                vm.totalRecords = constructionsResult.total;

                // Process filter options (second promise result)
                const filterOptions = results[1];
                console.log('Filter options received:', filterOptions);
                console.log('Filtered filters:', vm.filteredFilters);
                vm.filterOptions = filterOptions;

                // Check if filter options are properly populated
                if (vm.filterOptions) {
                    Object.keys(vm.filterOptions).forEach(key => {
                        console.log(`Filter options for ${key}:`, vm.filterOptions[key]);
                        if (key === 'insulationDescription') {
                            console.log('Insulation options found:', vm.filterOptions[key].length, 'items');
                        }
                    });
                } else {
                    console.error('Filter options is null or undefined');
                }

                // Check if insulation filter is in the filters array
                const insulationFilter = vm.filteredFilters.find(f => f.field === 'insulationDescription');
                if (insulationFilter) {
                    console.log('Insulation filter found in filteredFilters:', insulationFilter);
                } else {
                    console.log('Insulation filter NOT found in filteredFilters');
                    console.log('All filteredFilters:', vm.filteredFilters);
                }

                common.orderFilterOptions(vm.filterOptions);
                common.adjustFiltersForSelection(vm.filteredFilters, vm.appliedFilters, vm.appliedFiltersOld);
                vm.appliedFiltersOld = angular.copy(vm.appliedFilters);

                console.log('Applied filters after adjustment:', vm.appliedFilters);

                // Create search filter
                let searchFilter = [];
                if (vm.searchString) {
                    for (let i = 0; i < vm.searchFields.length; i++) {
                        if (i == vm.searchFields.length-1) {
                            searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString })
                        } else {
                            searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString, logic: "or" })
                        }
                    }
                }

                // Create combined applied filters for filter count data
                let combinedAppliedFilters = angular.copy(vm.filteredAppliedFilters());
                if (vm.categoryFilter && vm.categoryFilter.length > 0) {
                    combinedAppliedFilters.constructionCategoryCode = vm.categoryFilter;
                }

                // Make additional API call for filter count data after both initial calls are complete
                constructionservice.getFilterCountData(vm.filteredFilters, vm.filterOptions, combinedAppliedFilters, searchFilter, vm.templateType.toLowerCase(), categoryCodeParam).then(data => {
                    // Process filter count data
                    console.log('Filter count data received:', data);
                    vm.filterCountData = data;
                    vm.clearFilterCaches();
                    vm.updateFilterCountLookup();

                    // Update UI - matches Jobs/Construction list page pattern
                    vm.showingFromCnt = vm.constructionList.length > 0 ? 1 : 0;
                    vm.showingToCnt = vm.constructionList.length;
                    vm.isBusy = false;
                    $timeout(() => {
                        vm.initialiseComplete = true;
                        console.log('Initialization complete');
                    }, 1000);
                }).catch(error => {
                    console.error('Error loading filter count data:', error);
                    vm.initialiseComplete = true;
                    vm.isBusy = false;
                });
            }).catch(error => {
                console.error('Error loading construction data:', error);
                vm.initialiseComplete = true;
                vm.isBusy = false;
            });
        };

        // Main refresh method - matches Construction Database page pattern exactly
        vm.refreshList = function () {
            // Store date range in localStorage
            localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));

            common.adjustFiltersForSelection(vm.filteredFilters, vm.appliedFilters, vm.appliedFiltersOld);
            if ( vm.initialiseComplete && (!angular.equals(vm.appliedFilters, vm.appliedFiltersOld) || !angular.equals(vm.sortBy, vm.sortByOld) || vm.searchString != vm.searchStringOld) ) {
                vm.appliedFiltersOld = angular.copy(vm.appliedFilters);
                vm.sortByOld = angular.copy(vm.sortBy);
                vm.searchStringOld = vm.searchString;
                vm.isBusy = true;

                // Clear selection when filters change
                vm.selectedConstruction = null;

                // Create search filter - matches Construction Database page exactly
                let searchFilter = [];
                if (vm.searchString) {
                    for (let i = 0; i < vm.searchFields.length; i++) {
                        if (i == vm.searchFields.length-1) {
                            searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString })
                        } else {
                            searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString, logic: "or" })
                        }
                    }
                }

                // Get applied filters without category filter - category will be passed separately
                let combinedAppliedFilters = angular.copy(vm.filteredAppliedFilters());

                // For both opening and construction types, pass category as separate parameter instead of in combinedAppliedFilters
                let categoryCodeParam = null;
                if ((vm.templateType.toLowerCase() === 'opening' || vm.templateType.toLowerCase() === 'construction') && vm.categoryFilter && vm.categoryFilter.length > 0) {
                    categoryCodeParam = vm.categoryFilter[0]; // Use the first (and should be only) category code
                }

                // Get filtered constructions using multi-filter method - matches Construction Database page exactly
                constructionservice.getListMultiFiltered(vm.resultsPageSize, 1, vm.sortBy, vm.filteredFilters, vm.filterOptions, combinedAppliedFilters, searchFilter, vm.templateType.toLowerCase(), categoryCodeParam).then(function (result) {
                    if (result == undefined || result == null)
                        return; // Its been cancelled so get out of here.

                    if (result && result.data) {
                        vm.constructionList = result.data;
                        vm.totalFilteredConstructions = result.total;
                        vm.totalRecords = result.total;
                        vm.showingFromCnt = vm.constructionList.length > 0 ? 1 : 0;
                        vm.showingToCnt = vm.constructionList.length;

                        // Sort favorited items to the top only when no specific sort is applied
                        if (vm.constructionList && vm.constructionList.length > 0 && (!vm.sortBy || !vm.sortBy.field)) {
                            vm.constructionList.sort((a, b) => {
                                if (a.isFavourite && !b.isFavourite) return -1;
                                if (!a.isFavourite && b.isFavourite) return 1;
                                return 0;
                            });
                        }
                    }
                    vm.filtersApplied = vm.anyFiltersApplied(vm.searchString, vm.filteredFilters, vm.appliedFilters);

                    // Get filter count data
                    constructionservice.getFilterCountData(vm.filteredFilters, vm.filterOptions, combinedAppliedFilters, searchFilter, vm.templateType.toLowerCase(), categoryCodeParam).then(data => {
                        vm.filterCountData = data;
                        vm.clearFilterCaches();
                        vm.updateFilterCountLookup();
                        vm.isBusy = false;
                        vm.showMoreBusy = false;
                    });
                }).catch(function (error) {
                    console.error('Error refreshing construction list:', error);
                    vm.isBusy = false;
                    vm.showMoreBusy = false;
                });
            }
        };

        // Selection functionality
        vm.selectedConstruction = null;

        vm.selectConstruction = function(construction) {
            vm.selectedConstruction = construction;
        };



        // Show more results function - matches Jobs/Construction list page pattern
        vm.getMoreResults = function () {
            vm.showMoreBusy = true;
            vm.resultsPageSize += 100;
            // Force refresh by marking filters as changed
            vm.appliedFiltersOld = {};
            vm.refreshList();
        };

        // - SORTING - //
        vm.sortBy = null;
        vm.sortByOld = null;
        vm.orderBy = function (tableState, e, filterOption) {
            console.log(tableState, e, filterOption);
            console.log("Sort is: ", tableState.sort);

            if (tableState.sort && tableState.sort.predicate) {
                vm.sortBy = {
                    field: tableState.sort.predicate,
                    dir: tableState.sort.reverse ? 'desc' : 'asc'
                };
            }
            vm.refreshList();
        };

        // Additional functions needed for exact Construction Database functionality
        vm.isConstruction = function() {
            return vm.templateType.toLowerCase() === 'construction';
        };

        // Category-specific column visibility functions
        vm.isGlazing = function() {
            return vm.categoryType === 'glazing';
        };

        vm.isDoor = function() {
            return vm.categoryType === 'door';
        };

        vm.isOpening = function() {
            return vm.categoryType === 'opening';
        };

        vm.hasPermissionForAnyAction = function() {
            return false; // No action buttons in substitute modal
        };

        vm.actionButtons = []; // No action buttons in substitute modal

        // Group-related functions for advanced filtering (if needed)
        vm.getGroupOptions = function (filter, group) {
            let fieldName = filter.field;
            let groupKey = `${fieldName}_${group.value}`;

            // Return cached result if available
            if (vm.cachedGroupOptions[groupKey]) {
                return vm.cachedGroupOptions[groupKey];
            }

            let filteredFilterOptions = vm.filterOptions[fieldName]?.filter(f => group.startRange > group.endRange
                                                                                 ? +f.value > group.startRange || +f.value <= group.endRange
                                                                                 : +f.value > group.startRange && +f.value <= group.endRange);

            // Create a lookup map for faster filtering
            if (!vm.filterCountLookup) {
                vm.updateFilterCountLookup();
            }

            filteredFilterOptions = filteredFilterOptions?.filter(o => {
                const countItem = vm.getFilterCountItem(filter, o);
                return countItem > 0;
            });

            // Cache the result
            vm.cachedGroupOptions[groupKey] = filteredFilterOptions;
            return filteredFilterOptions;
        }

        vm.getGroupCount = function (filter, group) {
            let cacheKey = `${filter.field}_${group.value}`;

            // Return cached result if available
            if (vm.cachedGroupCounts[cacheKey] !== undefined) {
                return vm.cachedGroupCounts[cacheKey];
            }

            let optionsForThisGroup = vm.getGroupOptions(filter, group);
            if (!optionsForThisGroup || optionsForThisGroup.length === 0) {
                vm.cachedGroupCounts[cacheKey] = 0;
                return 0;
            }

            // Calculate the total once and cache it
            let thisGroupTotal = 0;
            for (let i = 0; i < optionsForThisGroup.length; i++) {
                const count = vm.getFilterCountItem(filter, optionsForThisGroup[i]);
                thisGroupTotal += count || 0;
            }

            vm.cachedGroupCounts[cacheKey] = thisGroupTotal;
            return thisGroupTotal;
        }

        vm.getFilterCountItem = function (filter, item) {
            if (!item) return null;

            let fieldName = filter.field;
            let itemValue = item.value?.toString().toLowerCase();

            if (!itemValue) return null;

            // Use cache if available
            let cacheKey = `${fieldName}_${itemValue}`;
            if (vm.cachedFilterCounts[cacheKey] !== undefined) {
                return vm.cachedFilterCounts[cacheKey];
            }

            // Use lookup map for faster access
            if (vm.filterCountLookup && vm.filterCountLookup[fieldName]) {
                const count = vm.filterCountLookup[fieldName][itemValue];
                vm.cachedFilterCounts[cacheKey] = count || 0;
                return count || 0;
            }

            // Fallback to original method if lookup not available
            if (!vm.filterCountData[fieldName]) {
                // During initial load, show all options with a default count of 1
                // This prevents options from being hidden before filter count data is loaded
                if (!vm.initialiseComplete) {
                    return 1;
                }
                vm.cachedFilterCounts[cacheKey] = 0;
                return 0;
            }

            let valueKey = Object.keys(vm.filterCountData[fieldName]).find(option =>
                option.toLowerCase() === itemValue);

            const count = valueKey ? vm.filterCountData[fieldName][valueKey] : 0;
            vm.cachedFilterCounts[cacheKey] = count;
            return count;
        }

        vm.selectOptionForGroup = function (filter, selected, group) {
            let fieldName = filter.field;
            let allOptionsForThisGroup = [...vm.getGroupOptions(filter, group).map(o => o.value), group.value];
            // function selectAllLogic(allOptions, listBefore, listChildIdKey, selected, selectAllCode)
            let newSelectedForThisGroup = common.selectAllLogic(
                [...allOptionsForThisGroup, group.value],
                vm.appliedFilters[fieldName].filter(o => allOptionsForThisGroup.includes(o)), // Only looking at this group's selected options
                null,
                selected.value,
                group.value
            )
            // Now that we have new selected options for this group, add this group's selections to full list of selections by removing this group's options from full selected list then adding this group's new selected
            vm.appliedFilters[fieldName] = vm.appliedFilters[fieldName].filter(o => !allOptionsForThisGroup.includes(o)).concat(newSelectedForThisGroup);
        }

        // Initialize the modal
        vm.initialRefreshList();

        // Modal functions
        vm.cancel = function () {
            $mdDialog.cancel();
        };

        vm.save = function () {
            if (vm.selectedConstruction) {
                $mdDialog.hide(vm.selectedConstruction);
            }
        };

    }
})();
