SELECT [userRole].[UserId]
      ,[user].[UserName] [__User]
      ,[userRole].[RoleId]
      ,[role].[Name] [__Role]
FROM [dbo].[AspNetUserRoles] [userRole]
INNER JOIN [dbo].[AspNetUsers] [user] ON [userRole].[UserId] = [user].[Id]
INNER JOIN [dbo].[AspNetRoles] [role] ON [userRole].[RoleId] = [role].[Id]
WHERE 1=1
	-- AND [user].[Id] IN (
        -- '6BD08875-CA99-4952-9FA3-6B47F9D530BE' -- admin
        -- 'A7DD815A-ABE3-411F-9705-853A8E78492F'
    -- )
ORDER BY [role].[SortOrder];


-- -- Get all Roles from a User as insert statements
-- SELECT 
--     'INSERT INTO [dbo].[AspNetUserRoles] ([UserId], [RoleId]) VALUES (''' 
--     + 'A368DD06-FFB9-4986-AE21-479904571E4E' + ''', ''' 
--     + CONVERT(varchar(36), [userRole].[RoleId]) + ''');' AS [-- SQL Insert Statement]
-- FROM [dbo].[AspNetUserRoles] [userRole]
-- WHERE [userRole].[UserId] = '82358da0-462e-4e7a-9575-ac6ca33dd028'


-- ------ --
-- REMOVE --
-- ------ --

-- -- Delete all Roles from User
-- DELETE FROM [dbo].[AspNetUserRoles] WHERE [UserId] = 'A368DD06-FFB9-4986-AE21-479904571E4E';

-- Remove all "Assessment Page (Tabs/sub-Tabs)" Roles from User
-- DELETE [userRole]
-- FROM [dbo].[AspNetUserRoles] [userRole]
-- JOIN [dbo].[AspNetRoles] [role] ON [userRole].[RoleId] = [role].Id
-- WHERE [userRole].[UserId] = 'A368DD06-FFB9-4986-AE21-479904571E4E' AND [role].[Name] LIKE '%assessment_page_(tabs/sub-tabs)%';


-- ------ --
-- INSERT --
-- ------ --

-- -- Give User all Roles
-- INSERT INTO [dbo].[AspNetUserRoles]
-- ([UserId], [RoleId])
-- SELECT 'A368DD06-FFB9-4986-AE21-479904571E4E', [role].[Id]
-- FROM [dbo].[AspNetRoles] [role]
-- WHERE NOT EXISTS (
--     SELECT 1 
--     FROM [dbo].[AspNetUserRoles] [userRole]
--     WHERE [userRole].[UserId] = 'A368DD06-FFB9-4986-AE21-479904571E4E'
--       AND [userRole].[RoleId] = [role].[Id]
-- );

-- -- Give all "Admin" Roles to User
-- INSERT INTO [dbo].[AspNetUserRoles] ([UserId], [RoleId])
-- SELECT 'A368DD06-FFB9-4986-AE21-479904571E4E' AS [UserId],
--        [role].[Id] AS [RoleId]
-- FROM [dbo].[AspNetRoles] [role]
-- WHERE 
--     [role].[Name] LIKE '%assessment_page_(tabs/sub-tabs)%'
--     AND NOT EXISTS (
--         SELECT 1
--         FROM [dbo].[AspNetUserRoles] [ur]
--         WHERE [ur].[UserId] = 'A368DD06-FFB9-4986-AE21-479904571E4E'
--           AND [ur].[RoleId] = [role].[Id]
--     );