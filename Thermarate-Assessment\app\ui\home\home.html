
<section id="home-view" class="main-content-wrapper" data-ng-controller="DashboardCtrl as vm">
    <section>
        <div>
            <div layout="row">
                    <h2 class="md-headline">Home - {{vm.jobTableTitle}}</h2>
            </div>
            <div layout="column">
                <md-toolbar class="widget-actionbar-wrapper header-container {{vm.filtersExpanded ? 'header-container-expanded' : null}}" flex>
                    <div class="md-toolbar-tools widget-actionbar header-inner-container">

                        <div class="filters-container">

                            <!-- Search -->
                            <div class="filter">
                                <div class="filter-label">Search</div>
                                <div class="search-input-container">
                                    <input class="search-input"
                                           type="text"
                                           placeholder="Quick Filter"
                                           ng-model="vm.searchString"
                                           ng-keydown="$event.keyCode == 13 ? vm.refreshList() : null"
                                           ng-blur="vm.refreshList()">
                                    <img src="/content/images/cross.png"
                                         class="search-clear-button"
                                         ng-show="vm.searchString"
                                         ng-click="vm.searchString = null; vm.refreshList(); $event.stopPropagation()">
                                </div>
                            </div>

                            <!-- Multi-Filters -->
                            <div ng-repeat="filter in vm.filters track by filter.field"
                                 ng-if="filter.name != null"
                                 ng-show="filter.section == 1 || vm.filtersExpanded"
                                 class="filter {{vm.anyOptionsSelectedOnField(filter, vm.appliedFilters) ? 'options-selected' : ''}}">

                                <div ng-if="filter.name != '[blank]' && filter.name != '[moreLessButton]'" class="filter-label">{{vm.keyToName(filter.name)}}</div>

                                <img src="/content/images/cross.png"
                                     class="filter-clear-button"
                                     ng-click="vm.clearFilter(filter);$event.stopPropagation()"
                                />

                                <!-- Normal Field -->
                                <md-select ng-if="filter.name != '[blank]' && filter.name != '[moreLessButton]' && !filter.isDate"
                                           class="filter-dropdown"
                                           multiple="true"
                                           md-selected-text="vm.getFilterSelectedText(filter, vm.filterOptions, vm.appliedFilters)"
                                           ng-model="vm.appliedFilters[filter.field]"
                                           ng-change="vm.refreshList()">

                                    <!-- Grouped Options -->
                                    <md-option ng-if="filter.groups != null"
                                               ng-show="vm.getFilterCountItem(filter, {value: 'Any'}) > 0"
                                               ng-value="'Any'">
                                        Any ({{vm.getFilterCountItem(filter, {value: 'Any'})}})
                                    </md-option>
                                    <div ng-if="filter.groups != null"
                                         ng-show="vm.getGroupCount(filter, group) > 0"
                                         ng-repeat="group in filter.groups track by group.value"
                                         ng-value="item.value">
                                        <div style="position:relative">
                                            <div class="group-arrow" ng-click="group.expanded = !group.expanded;$event.stopPropagation()">
                                                <div class="{{group.expanded ? 'expanded' : ''}}"/>
                                            </div>
                                            <md-option ng-value="group.value"
                                                       ng-click="vm.selectOptionForGroup(filter, group, group)"
                                                       class="group-option">
                                                {{group.name}} ({{vm.getGroupCount(filter, group)}})
                                            </md-option>
                                        </div>
                                        <md-option ng-show="group.expanded && vm.getFilterCountItem(filter, item) > 0"
                                                   ng-repeat="item in vm.getGroupOptions(filter, group) track by item.value"
                                                   ng-value="item.value"
                                                   ng-click="vm.selectOptionForGroup(filter, item, group)"
                                                   class="group-child-option">
                                            {{vm.optionName(item, filter.isBool, filter.isDecimal)}} ({{vm.getFilterCountItem(filter, item)}})
                                        </md-option>
                                    </div>

                                    <!-- Normal Option -->
                                    <md-option ng-if="filter.groups == null"
                                               ng-show="vm.getFilterCountItem(filter, item) > 0"
                                               ng-repeat="item in vm.filterOptions[filter.field] track by item.value"
                                               ng-value="item.value">
                                        {{vm.optionName(item, filter.isBool, filter.isDecimal)}} ({{vm.getFilterCountItem(filter, item)}})
                                    </md-option>

                                </md-select>

                                <!-- Date Field -->
                                <md-select ng-if="filter.name != '[blank]' && filter.name != '[moreLessButton]' && filter.isDate"
                                            class="filter-dropdown"
                                            multiple="true"
                                            md-selected-text="vm.appliedFilters[filter.field].label"
                                            ng-model="vm.appliedFilters[filter.field]">
                                </md-select>
                                <div ng-if="filter.isDate"
                                        rd-date-range-picker
                                        class="date-range-picker"
                                        ng-model="vm.appliedFilters[filter.field]"
                                        format="MMMM D, YYYY"
                                        ranges="vm.ranges"
                                        on-change="vm.refreshList">
                                </div>

                                <!-- More/Less Button -->
                                <div ng-if="filter.name == '[moreLessButton]'"
                                     class="more-filters-button {{vm.filtersExpanded ? 'adjust-padding' : null}}"
                                     ng-click="vm.filtersExpanded = !vm.filtersExpanded"
                                >
                                    <img class="more-filters-icon" src="../../../content/images/settings.png" /> {{vm.filtersExpanded ? 'Less Filters' : 'More Filters'}}
                                </div>

                                <!-- Blank -->
                                <div ng-if="filter.name == '[blank]'"></div>

                            </div>

                        </div>

                        <!-- Settings -->
                        <md-menu class="settings-button">
                            <md-button class="md-icon-button" ng-click="$mdOpenMenu()"><i class="material-icons">settings</i></md-button>
                            <md-menu-content>
                                <div class="columnFilter" ng-repeat="item in vm.columnOptions.columnList track by item.sortOrder">
                                    <md-checkbox ng-model="vm.columnOptions[item.reference]">
                                        {{item.description}}
                                    </md-checkbox>
                                </div>
                            </md-menu-content>
                        </md-menu>

                    </div>

                    <!-- Number of Items -->
                    <div class="current-filter-description {{vm.filtersExpanded ? null : 'add-margin'}}" ng-show="!vm.filtersApplied">
                        Showing {{vm.totalFilteredJobs}} jobs
                    </div>
                    <div class="current-filter-description {{vm.filtersExpanded ? null : 'add-margin'}}" ng-show="vm.filtersApplied">
                        {{vm.totalFilteredJobs}} of {{vm.totalJobs}} jobs match your filters
                        (<u ng-click="vm.clearFilters()" style="text-decoration: underline; cursor:pointer;">clear filters</u>)
                    </div>

                </md-toolbar>

                <div class="table-responsive-vertical shadow-z-1" flex
                     style="font-size: 12px;">
                    <table class="table table-striped table-hover table-condensed"
                           st-table="vm.allJobs"
                           st-safe-src="vm.safeSourceJobs"
                           st-pipe="vm.orderBy">
                        <thead>
                            <tr>
                                <!-- <th align="left" class="action-col">Action</th> -->

                                <th st-sort="jobReference" class="can-sort text-left" ng-if="vm.columnOptions['jobReference']">Ref</th>
                                <th st-sort="clientName" class="can-sort text-left" ng-if="vm.columnOptions['client']">Client</th>
                                <th st-sort="clientJobNumber" class="can-sort text-left" ng-if="vm.columnOptions['clientRef']">Client Ref</th>

                                <th st-sort="address" class="can-sort text-left" ng-if="vm.columnOptions['projectAddress']">Project Address</th>
                                <th st-sort="projectOwner" class="can-sort text-left" ng-if="vm.columnOptions['owner']">Owner</th>
                                <th st-sort="projectDescriptionDescription" class="can-sort text-left" ng-if="vm.columnOptions['projectDescription']">Building Description</th>
                                <th st-sort="assessmentDesign" class="can-sort text-left" ng-if="vm.columnOptions['assessmentDesign']">House Type</th>

                                <th st-sort="assessmentVersion" class="can-sort text-right" ng-if="vm.columnOptions['assessmentVersion']">Version</th>

                                <th st-sort="creatorFullName" class="can-sort text-left"
                                    redi-allow-roles="['assessment_page_(tabs/sub-tabs)__creator__edit']"
                                    ng-if="vm.columnOptions['creator']">Creator</th>
                                <th st-sort="clientAssigneeFullName" class="can-sort text-left" ng-if="vm.columnOptions['assignee']">Assignee</th>
                                <th st-sort="assessorFullName" class="can-sort text-left" ng-if="vm.columnOptions['assessor']">Assessor</th>
                                <th st-sort="assessmentPriorityCode" class="can-sort text-left" ng-if="vm.columnOptions['priority']">Priority</th>
                                <th st-sort="jobStatusDescription" class="can-sort text-left" ng-if="vm.columnOptions['status']">Status</th>

                                <th st-sort="orderDate" class="can-sort text-left" ng-if="vm.columnOptions['orderDate']">Created</th>
                                <th st-sort="assessmentCerficateDate" class="can-sort text-left" ng-if="vm.columnOptions['assessmentCerficateDate']">Certified</th>
                                <th st-sort="jobModifiedOn" class="can-sort text-left" ng-if="vm.columnOptions['jobModifiedOn']">Updated</th>
                            </tr>

                        </thead>

                        <tbody>
                            <!-- *** FILTERING FOR LIST IS DONE HERE *** -->
                            <tr ng-repeat="row in vm.jobsList"
                                class="{{vm.getPriorityColour(row, $index)}} {{row.canEditJob ? 'clickable' : 'not-clickable'}}"
                                ng-click="vm.goToJob(row)"
                                ng-mousedown="vm.jobRowClick($event, row)">

                                <!-- <td data-title="Action" class="action-col"><md-button class="md-primary list-select" ui-sref="assessment-updateform({ assessmentId: row.currentAssessmentId})">select</md-button>  </td> -->

                                <td data-title="Job Reference" class="text-left" ng-if="vm.columnOptions['jobReference']">{{::row.jobReference }}</td>
                                <td data-title="Client" class="text-left" ng-if="vm.columnOptions['client']">{{::row.clientName }}</td>
                                <td data-title="Client Job Ref" class="text-left" ng-if="vm.columnOptions['clientRef']">{{::row.clientJobNumber }}</td>

                                <td data-title="Project Address" class="text-left" ng-if="vm.columnOptions['projectAddress']">{{::row.address }}</td>
                                <td data-title="Project Owner" class="text-left" ng-if="vm.columnOptions['owner']">{{::row.projectOwner }}</td>
                                <td data-title="Building Description" class="text-left" ng-if="vm.columnOptions['projectDescription']">{{::row.projectDescriptionDescription }}</td>
                                <td data-title="House Type" class="text-left" ng-if="vm.columnOptions['assessmentDesign']">{{::row.assessmentDesign }}</td>

                                <td data-title="Version" class="text-right" ng-if="vm.columnOptions['assessmentVersion']">{{::row.assessmentVersion | number }}</td>

                                <td data-title="Creator" class="text-left" redi-allow-roles="['assessment_page_(tabs/sub-tabs)__creator__edit']"
                                    ng-if="vm.columnOptions['creator']">
                                    {{::row.creatorFullName }}
                                </td>
                                <td data-title="Assignee" class="text-right" ng-if="vm.columnOptions['assignee']">{{::row.clientAssigneeFullName }}</td>
                                <td data-title="Assessor" class="text-right" ng-if="vm.columnOptions['assessor']">{{::row.assessorFullName }}</td>
                                <td data-title="Priority"
                                    class="text-left"
                                    ng-class="{'label-red label-bold' : row.assessmentPriorityCode=='URGENT', 'label-orange label-bold' : row.assessmentPriorityCode=='HIGH'}"
                                    ng-if="vm.columnOptions['priority']">
                                    {{::row.assessmentPriorityDescription }}
                                </td>
                                <td data-title="Status" class="text-left"
                                    ng-class="{'label-red' : row.statusCode=='JCancelled',
                                                'label-green' : row.statusCode=='JIssued' || row.statusCode=='JComplete',
                                                'label-orange' : row.statusCode=='JDraft' || row.statusCode=='JInProgress' || row.statusCode=='JPreliminaryReview' || row.statusCode=='JCompliance' || row.statusCode=='JRecertification'}"
                                    ng-if="vm.columnOptions['status']">
                                    {{::row.jobStatusDescription }}
                                </td>

                                <td data-title="Created" class="text-left" ng-if="vm.columnOptions['orderDate']">{{::row.orderDate | date: 'dd/MM/yyyy HH:mm:ss' }}</td>
                                <td data-title="Certified" class="text-left" ng-if="vm.columnOptions['assessmentCerficateDate']">{{::row.assessmentCerficateDate | date: 'dd/MM/yyyy HH:mm:ss' }}</td>
                                <td data-title="Updated" class="text-left" ng-if="vm.columnOptions['jobModifiedOn']">{{::row.jobModifiedOn | date: 'dd/MM/yyyy HH:mm:ss' }}</td>

                            </tr>
                            <tr ng-show="vm.jobsList == null || vm.jobsList.length == 0">
                                <td colspan="{{vm.columnOptions.columnCount + 1}}" class="empty-list-message">No jobs found</td>
                            </tr>
                        </tbody>
                    </table>
                    <div ng-show="vm.showingToCnt < vm.totalFilteredJobs"
                         style="width:max-content; margin:auto; margin-top: -10px; margin-bottom: 20px;"
                         ng-class="{'clickable': !vm.showMoreLoading, 'loading-disabled': vm.showMoreLoading}"
                         ng-click="!vm.showMoreLoading && vm.getMoreResults()">
                        <span ng-show="!vm.showMoreLoading">Show more</span>
                        <i ng-show="vm.showMoreLoading" class="fa fa-spinner fa-spin" style="font-size: 16px;"></i>
                    </div>
                    <div class="widget-pager">
                        <span ng-if="vm.totalFilteredJobs != null && vm.totalFilteredJobs > 0">Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalFilteredJobs}}</span>
                    </div>
                </div>
            </div>
        </div>
    </section>
</section>

<style>
    .not-clickable {
        cursor: default;
    }

    .header-container {
        min-height: 150px !important;
        justify-content: center;
    }

    .header-container-expanded {
        min-height: 400px !important;
    }

        .header-inner-container {
            gap: 1%;
            max-height: max-content;
            height: max-content;
            align-items: flex-start;
        }

            .filters-container {
                flex: 10;
                display: grid;
                grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
                column-gap: 2%;
                row-gap: 15px;
            }

                .filter {
                    position: relative;
                    margin-top: 32px; /* To account for heading above that is positioned absolute */
                    font-size: 1.2rem !important;
                }

                    .search-input-container {
                        position: relative;
                        width: 100%;
                    }

                    .search-input {
                        width: 100%;
                        height: 45px;
                        margin-top: -6px;
                        padding: 7px 10px 6px 10px;
                        box-sizing: border-box;
                        border: none;
                        background-color: #e2e2e2;
                        color: black !important;
                    }
                    .search-input:focus-visible { border: none !important; }
                    .search-input::placeholder { color: black !important; }
                    .search-input:-ms-input-placeholder { color: black !important; }

                    .search-clear-button {
                        position: absolute;
                        right: 6px;
                        top: 46%;
                        transform: translateY(-50%);
                        z-index: 50;
                        width: 18px;
                        height: auto;
                        padding: 4px;
                        border-radius: 50%;
                        cursor: pointer;
                    }
                    .search-clear-button:hover {
                        background-color: #eeeeee;
                    }

                    .filter-label {
                        position: absolute;
                        top: -32px;
                        left: 15px;
                        font-size: 12px;
                        color: #8e888e;
                    }

                    .filter-dropdown {
                        margin: -6px 0 0 2px;
                        width: 100%;
                        height: 45px;
                        padding: 7px 10px 6px 10px;
                        box-sizing: border-box;
                        background-color: #e2e2e2;
                        color: black !important;
                    }

                    .filter-dropdown span {
                        margin-top: -2px;
                    }

                    .filter-dropdown .md-select-placeholder {
                        color: black !important;
                    }

                        .group-option {
                            margin-left: 20px;
                        }

                            .group-arrow {
                                position: absolute;
                                left: 5px;
                                top: 50%;
                                transform: translateY(-50%);
                                width: 18px;
                                height: 18px;
                                border-radius: 50%;
                                cursor: pointer;
                            }
                            .group-arrow:hover {
                                background-color: #eeeeee;
                            }

                                .group-arrow > div {
                                    position: absolute;
                                    top: 3px;
                                    left: 6px;
                                    border-top: 5px solid transparent;
                                    border-bottom: 5px solid transparent;
                                    border-left: 6px solid black;
                                }
                                .group-arrow > div.expanded {
                                    top: 7px;
                                    left: 4px;
                                    border-left: 5px solid transparent;
                                    border-right: 5px solid transparent;
                                    border-top: 6px solid black;
                                }

                                .group-child-option {
                                    margin-left: 30px;
                                }

                        .filter-clear-button {
                            display: none;
                            position: absolute;
                            right: 3px;
                            bottom: 13px;
                            z-index: 50;
                            width: 18px;
                            height: auto;
                            padding: 4px;
                            border-radius: 50%;
                            cursor: pointer;
                        }

                            .filter-clear-button:hover {
                                background-color: #eeeeee;
                            }

                        .filter.options-selected > .filter-clear-button {
                            display: inherit !important;
                        }

                        .filter.options-selected .md-select-icon {
                            margin-left: -36px;
                        }

                    .filter-dropdown .md-select-value {
                        border-bottom: none !important;
                    }

                    .more-filters-button {
                        margin-top: -6px;
                        position: relative;
                        height: 45px;
                        padding-top: 17px;
                        padding-left: 70px;
                        background-color: white;
                        color: #8e888e;
                        cursor: pointer;
                        user-select: none;
                    }
                    .more-filters-button.adjust-padding {
                        padding-top: 15px;
                    }



                        .more-filters-icon {
                            position: absolute;
                            left: 25px;
                            top: 11px;
                            width: 24px;
                            height: 24px;
                            margin-right: 10px;
                            opacity: 0.7;
                        }

                    .date-range-picker {
                        height: 125%;
                        width: 104% !important;
                        opacity: 0;
                        position: absolute;
                        top: -9px;
                        left: -12px;
                    }

            .settings-button {
                margin-top: 27px !important;
            }

    .filter-desciption-container {
        display: flex;
        justify-content: space-between;
    }

        .current-filter-description {
            margin-left: 20px;
            margin-top: 20px;
            color: #7b7b7b;
            font-size: 12px;
        }
        .current-filter-description.add-margin {
            margin-top: 30px;
        }

            .clear-filters-text {
                margin-left: 4px;
            }

        .sort-container {
            margin-top: 44px;
            margin-right: 20px;
            color: black;
        }

        .loading-disabled {
            cursor: default !important;
            opacity: 0.6;
        }

</style>
