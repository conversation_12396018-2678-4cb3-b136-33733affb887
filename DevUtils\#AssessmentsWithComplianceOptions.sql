-- This script finds 3 latest assessments (by CreatedOn date) that have at least 2 compliance options
-- and outputs their AssessmentId

-- First, create a CTE to count compliance options per assessment and include CreatedOn date
WITH AssessmentOptionCounts AS (
    SELECT
        a.AssessmentId,
        COUNT(co.ComplianceOptionsId) AS OptionCount,
        a.CreatedOn
    FROM
        dbo.RSS_Assessment a
    INNER JOIN
        dbo.RSS_AssessmentComplianceOption co ON a.AssessmentId = co.AssessmentId
    WHERE
        a.Deleted = 0
        AND co.Deleted = 0
    GROUP BY
        a.AssessmentId, a.CreatedOn
    HAVING
        COUNT(co.ComplianceOptionsId) >= 3
)

-- Select the top 3 assessments with their option counts, ordered by CreatedOn date
SELECT TOP 3
    aoc.AssessmentId,
    aoc.OptionCount AS NumberOfComplianceOptions,
    aoc.CreatedOn,
    (
        SELECT STRING_AGG(co.ComplianceMethodCode, ', ')
        FROM dbo.RSS_AssessmentComplianceOption co
        WHERE co.AssessmentId = aoc.AssessmentId AND co.Deleted = 0
    ) AS ComplianceMethods
FROM
    AssessmentOptionCounts aoc
ORDER BY
    aoc.CreatedOn DESC;
