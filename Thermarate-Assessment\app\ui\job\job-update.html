<form name="jobform" class="main-content-wrapper" novalidate data-ng-controller='JobUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
             data-title="{{vm.title}}"
             data-subtitle="{{vm.subtitle}}"
             data-is-modal="vm.isModal"
             data-cancel="vm.cancel()"
             data-back-button>
        </div>
        <div data-cc-widget-action-bar
             data-quick-find-model=''
             data-action-buttons='vm.actionButtons'
             data-refresh-list=''
             data-spinner-busy='vm.isBusy'
             data-new-record=""
             data-new-record-text=""
             data-is-modal="vm.isModal"
             data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
             data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column" style="min-width:600px;">
                <!--Main Body-->
                <div flex="100">
                    <md-card>
                        <md-card-header>
                            <span class="md-headline">Order Details</span>
                        </md-card-header>
                        <md-card-content>

                            <!-- ******** Created ******** -->
                            <md-input-container class="md-block vertically-condensed" style="margin-left:-16px;">
                                <label>Created</label>
                                <md-datepicker ng-model="vm.job.currentAssessment.assessmentProjectDetail.orderDate"
                                                name="orderDate"
                                                required
                                                md-placeholder="Enter date">
                                </md-datepicker>
                                <div class="validation-messages" ng-messages="jobform.orderDate.$error">
                                    <div ng-message="valid">The entered value is not a date!</div>
                                    <div ng-message="required">This date is required!</div>
                                    <div ng-message="mindate">Date is too early!</div>
                                    <div ng-message="maxdate">Date is too late!</div>
                                    <div ng-message="filtered">Only xxxxx dates are allowed!</div>
                                </div>
                            </md-input-container>

                            <div layout-gt-sm="row">

                                <!-- ******** Client ******** -->
                                <md-autocomplete ng-if="vm.permission_field_client_view"
                                                 md-input-name="clientId"
                                                 md-autofocus
                                                 ng-disabled="vm.type == vm.TYPE_RECERTIFY || !vm.permission_field_client_edit"
                                                 class="vertically-condensed"
                                                 flex="70"
                                                 required
                                                 md-input-minlength="2"
                                                 md-min-length="0"
                                                 md-selected-item="vm.job.client"
                                                 md-search-text="vm.clientIdSearchText"
                                                 md-items="item in vm.getclients(vm.clientIdSearchText)"
                                                 md-item-text="item.clientName"
                                                 md-require-match
                                                 md-floating-label="Client"
                                                 md-selected-item-change="vm.clientChanged();">
                                    <md-item-template>

                                        <!-- "New Client" Logo -->
                                        <i ng-if="item.clientId=='FAKE_ID_FOR_FAKE_CLIENT'"
                                           class="fa fa-address-book-o"
                                           aria-hidden="true" />
                                        <!-- "Favourite" Client Logo -->
                                        <i ng-if="item.isFavourite==true"
                                           class="fa fa-star"
                                           style="color: gold"
                                           aria-hidden="true" />
                                        &nbsp;
                                        <span md-highlight-text="vm.clientIdSearchText">
                                            {{item.clientName}}
                                        </span>
                                    </md-item-template>
                                    <div ng-messages="jobform.clientId.$error">
                                        <div ng-message="required">Client is required.</div>
                                    </div>
                                </md-autocomplete>

                                <!-- Custom Client Name Checkbox -->
                                <md-input-container ng-if="vm.permission_field_customclientnamecheckbox_view"
                                                    layout="column"
                                                    flex="30"
                                                    class="md-block"
                                                    style="margin: 0 0 auto 20px;">
                                    <md-checkbox ng-model="vm.job.currentAssessment.assessmentProjectDetail.useCustomClientName"
                                                 ng-change="vm.useCustomClientNameChanged(vm.job.currentAssessment.assessmentProjectDetail.useCustomClientName)"
                                                 name="useCustomClientName"
                                                 ng-disabled="vm.isLocked || !vm.permission_field_customclientnamecheckbox_edit">
                                        Custom Client Name
                                    </md-checkbox>
                                </md-input-container>

                            </div>

                            <!-- ******** Custom Client Name ******** -->
                            <md-input-container ng-if="vm.job.currentAssessment.assessmentProjectDetail.useCustomClientName && vm.permission_field_customclientname_view" class="md-block vertically-condensed" flex-gt-sm>
                                <label>Custom Client Name</label>
                                <input type="text" name="customClientName"
                                       ng-model="vm.job.currentAssessment.assessmentProjectDetail.customClientName"
                                       ng-disabled="!vm.permission_field_customclientname_edit"
                                       ng-required="true"/>
                                <div ng-messages="jobform.customClientName.$error">
                                    <div ng-message="required">Custom Client Name is required.</div>
                                </div>
                            </md-input-container>

                            <!-- ******** Assignee ******** -->
                            <md-autocomplete ng-if="vm.permission_field_assignee_view"
                                             md-input-name="contactId"
                                             class="vertically-condensed"
                                             ng-required="vm.permission_field_assignee_view"
                                             ng-disabled="!vm.permission_field_assignee_edit"
                                             md-input-minlength="2"
                                             md-min-length="0"
                                             md-selected-item="vm.job.currentAssessment.assessmentProjectDetail.clientAssignee"
                                             md-search-text="vm.contactIdSearchText"
                                             md-items="item in vm.getcontacts(vm.contactIdSearchText)"
                                             md-item-text="item.fullName"
                                             md-require-match
                                             md-no-cache="true"
                                             md-floating-label="Assignee">
                                <md-item-template>
                                    <span md-highlight-text="vm.contactIdSearchText">{{item.fullName}}</span>
                                </md-item-template>
                                <div ng-messages="jobform.contactId.$error">
                                    <div ng-message="required">Contact is required.</div>
                                </div>
                            </md-autocomplete>

                            <!-- ******** Client Job Number ******** -->
                            <md-input-container ng-if="vm.permission_field_clientjobnumber_view"
                                                class="md-block vertically-condensed"
                                                flex-gt-sm
                                                ng-click="vm.removeClientJobNumberHighlight = true">
                                <label>Client Job Number</label>
                                <input type="text" name="clientJobNumber"
                                       class="important-field"
                                       ng-class="{'highlight': !vm.removeClientJobNumberHighlight && vm.type == vm.TYPE_COPY}"
                                       ng-model="vm.job.currentAssessment.assessmentProjectDetail.clientJobNumber"
                                       ng-maxlength="100"
                                       required
                                       ng-disabled="vm.type === 'RECERTIFY' || !vm.permission_field_clientjobnumber_edit"
                                       ng-blur="vm.clientJobNumberChanged()"/>
                                <div ng-messages="jobform.clientJobNumber.$error">
                                    <div ng-message="required">Client Job Number is required.</div>
                                    <div ng-message="maxlength">Too many characters entered, max length is 100.</div>
                                </div>
                            </md-input-container>

                            <!-- ******** Purchase Order & File ******** -->
                            <md-input-container class="md-block vertically-condensed"
                                                ng-click="vm.removeHighlight = true"
                                                flex-gt-sm ng-if="vm.purchaseOrderCode!='NotRequired'">
                                <label>Purchase Order</label>
                                <input type="text"
                                       name="purchaseOrder"
                                       class="important-field"
                                       ng-class="{'highlight': !vm.removeHighlight && vm.type != vm.TYPE_NEWJOB}"
                                       ng-required="vm.purchaseOrderCode!='File'"
                                       ng-model="vm.job.currentAssessment.allComplianceOptions[0].purchaseOrder" />
                                <div ng-messages="jobform.purchaseOrder.$error">
                                    <div ng-message="required">Purchase order is required.</div>
                                </div>
                            </md-input-container>

                            <div class="md-block"
                                 ng-class="{'important-file-upload': !vm.removeHighlight2 && vm.type != vm.TYPE_NEWJOB}"
                                 style="margin-right: 8.5em;"
                                 flex="85"
                                 ng-if="vm.purchaseOrderCode=='NumberAndFile' || vm.purchaseOrderCode=='File'">
                                <label style="font-size: 9px; color: gray;">Purchase Order File</label>
                                <file-upload class="vertically-condensed"
                                             assessment="vm.job.currentAssessment"
                                             job="vm.job"
                                             category="'Invoicing'"
                                             classification="'Purchase Order File'"
                                             external-click-handler="vm.removeHighlight2 = true;"
                                             required-message="''"
                                             force-edit="true"
                                             file-object="vm.job.currentAssessment.allComplianceOptions[0]"
                                             prop-name="purchaseOrderFile"
                                             is-required="true"></file-upload>
                            </div>

                            <!-- Works Description -->
                            <md-input-container class="md-block vertically-condensed" flex="100" style="margin-top: 10px;">
                                <label>Works Description</label>
                                <md-select name="WorksDescription"
                                           style="margin-bottom: 14px"
                                           ng-required="true"
                                           ng-model="vm.job.currentAssessment.worksDescription"
                                           ng-model-options="{trackBy: '$value.worksDescriptionCode'}">
                                    <md-option ng-value="item"
                                               ng-repeat="item in vm.worksDescriptionList">
                                        {{item.description}}
                                    </md-option>
                                </md-select>
                            </md-input-container>

                            <!-- Building Description -->
                            <md-autocomplete flex="100"
                                             ng-required="true"
                                             ng-if="vm.job.currentAssessment.allComplianceOptions[0].proposed.projectDescription.projectDescriptionCode != 'PDOther'"
                                             md-input-name="BuildingDescription"
                                             md-selected-item="vm.job.currentAssessment.allComplianceOptions[0].proposed.projectDescription"
                                             md-search-text="vm.projectDescriptionSearchText"
                                             md-items="listItem in vm.projectDescriptionList"
                                             md-item-text="listItem.description"
                                             md-min-length="0"
                                             md-select-on-match="true"
                                             md-require-match="true"
                                             md-floating-label="Building Description"
                                             ng-class="{'input-black' : !vm.disabled }">
                                <md-item-template ng-class="{'input-black' : !vm.disabled }">
                                    <div md-highlight-text="vm.projectDescriptionSearchText"
                                         md-highlight-flags="^i"
                                         ng-click="vm.projectDescriptionChanged(vm.job.currentAssessment.allComplianceOptions[0].proposed, listItem)"
                                         ng-class="{'input-black' : !vm.disabled }">
                                        {{listItem.description}}
                                    </div>
                                </md-item-template>
                            </md-autocomplete>
                            <md-input-container class="md-block" flex="100" flex-gt-sm="50" ng-if="vm.job.currentAssessment.allComplianceOptions[0].proposed.projectDescription.projectDescription.projectDescriptionCode=='PDOther'">
                                <div layout="row" layout-wrap>
                                    <div flex="90">
                                        <label>Building Description Other</label>
                                        <input type="text" name="BuildingDescriptionOther"
                                               ng-model="vm.job.currentAssessment.allComplianceOptions[0].proposed.projectDescription.projectDescriptionOther" />
                                    </div>
                                    <div flex="5">
                                        <md-button class="md-icon-button" ng-click="vm.clearProjectDescription(vm.job.currentAssessment.allComplianceOptions[0])" ng-show="!vm.disabled"><i class="material-icons">clear</i></md-button>
                                    </div>
                                </div>
                            </md-input-container>

                            <!-- ******** Certification ******** -->
                            <md-input-container ng-if="vm.newRecord && vm.permission_field_certification_view"
                                                class="md-block vertically-condensed"
                                                flex-gt-sm>
                                <label>Certification</label>
                                <md-select name="certification"
                                           ng-required="vm.newRecord && vm.permission_field_certification_view"
                                           ng-disabled="!vm.permission_field_certification_edit"
                                           ng-model="vm.job.currentAssessment.allComplianceOptions[0].certification"
                                           ng-model-options="{trackBy: '$value.certificationId'}">
                                    <md-option ng-value="item"
                                               ng-repeat="item in vm.certificationList track by item.certificationId">
                                        {{item.title}}
                                    </md-option>
                                </md-select>
                                <div ng-messages="jobform.certification.$error">
                                    <div ng-message="required">Certification is required.</div>
                                </div>
                            </md-input-container>

                            <!-- ******** Assessment Method ******** -->
                            <md-input-container ng-if="vm.newRecord && vm.permission_field_assessmentmethod_view"
                                                class="md-block vertically-condensed"
                                                flex-gt-sm>
                                <label>Assessment Method</label>
                                <md-select name="preliminaryComplianceMethodCode"
                                           ng-required="vm.newRecord && vm.permission_field_assessmentmethod_view"
                                           ng-disabled="!vm.permission_field_assessmentmethod_edit"
                                           ng-model="vm.job.currentAssessment.allComplianceOptions[0].complianceMethod"
                                           ng-model-options="{trackBy: '$value.complianceMethodCode'}"
                                           ng-change="vm.complianceMethodChanged()">
                                    <md-option ng-value="item"
                                               ng-repeat="item in vm.availableComplianceMethods() track by item.complianceMethodCode">
                                        {{item.description}}
                                    </md-option>
                                </md-select>
                                <div ng-messages="jobform.preliminaryComplianceMethodCode.$error">
                                    <div ng-message="required">Assessment Method is required.</div>
                                </div>
                            </md-input-container>

                            <!-- ******** Min House Energy Rating ******** -->
                            <md-autocomplete ng-if="vm.permission_field_herrequired_view && vm.newRecord && vm.minHouseEnergyRating.description != 'Other' &&
                                             (vm.job.currentAssessment.allComplianceOptions[0].complianceMethod.complianceMethodCode == 'CMHouseEnergyRating' ||
                                              vm.job.currentAssessment.allComplianceOptions[0].complianceMethod.complianceMethodCode=='CMPerfSolutionHER' ||
                                              vm.job.currentAssessment.allComplianceOptions[0].complianceMethod.complianceMethodCode == 'CMPerfWAProtocolHER')"
                                             md-input-name="minHouseEnergyRating"
                                             class="vertically-condensed"
                                             ng-required="vm.permission_field_herrequired_view && vm.newRecord && vm.minHouseEnergyRating.description != 'Other' &&
                                             (vm.job.currentAssessment.allComplianceOptions[0].complianceMethod.complianceMethodCode == 'CMHouseEnergyRating' ||
                                              vm.job.currentAssessment.allComplianceOptions[0].complianceMethod.complianceMethodCode=='CMPerfSolutionHER' ||
                                              vm.job.currentAssessment.allComplianceOptions[0].complianceMethod.complianceMethodCode == 'CMPerfWAProtocolHER')"
                                             ng-disabled="!vm.permission_field_herrequired_edit"
                                             md-input-minlength="1"
                                             md-min-length="0"
                                             md-select-on-match="true"
                                             md-selected-item="vm.tempMinHouseEnergyRating"
                                             md-selected-item-change="vm.formatActualMinHouseEnergyRating()"
                                             md-search-text="vm.minHouseEnergyRatingSearchText"
                                             md-items="value in vm.availableHouseEnergyRatings | filter: vm.minHouseEnergyRatingSearchText"
                                             md-item-text="value"
                                             md-require-match
                                             md-no-cache="true"
                                             md-floating-label="Required House Energy Rating">
                                <md-item-template>
                                    <span md-highlight-text="vm.minHouseEnergyRatingSearchText">{{value}}</span>
                                </md-item-template>
                                <div ng-messages="jobform.minHouseEnergyRating.$error">
                                    <div ng-message="required">Required house energy rating is required.</div>
                                </div>
                            </md-autocomplete>
                            <md-input-container class="md-block vertically-condensed" flex
                                                ng-if="vm.permission_field_herrequired_view && vm.newRecord && vm.minHouseEnergyRating.description=='Other'">
                                <div layout="row" layout-wrap>
                                    <div flex="90">
                                        <label>Required House Energy Rating</label>
                                        <input type="number" name="minHouseEnergyRatingOther"
                                               ng-model="vm.job.currentAssessment.allComplianceOptions[0].requiredHouseEnergyRating"
                                               ng-disabled="!vm.permission_field_herrequired_edit"
                                               pattern="^\d{0,2}(?:\.\d)?$"
                                               string-to-number
                                               step="0.1"
                                               max="10"
                                               min="0"
                                               ng-required="vm.permission_field_herrequired_view && vm.newRecord && vm.minHouseEnergyRating.description=='Other'" />
                                    </div>
                                    <div flex="5">
                                        <md-button class="md-icon-button" ng-click="vm.clearMinHouseEnergyRating()"><i class="material-icons">clear</i></md-button>
                                    </div>
                                </div>
                            </md-input-container>

                            <!-- ******** Assessment Software ******** -->
                            <md-input-container ng-if="vm.newRecord && vm.permission_field_assessmentsoftware_view && vm.job.currentAssessment.allComplianceOptions[0].assessmentSoftwareCode != 'Other'"
                                                class="md-block vertically-condensed"
                                                flex-gt-sm>
                                <label>Assessment Software</label>
                                <md-select name="assessmentSoftwareCode"
                                           ng-disabled="!vm.permission_field_assessmentsoftware_edit"
                                           ng-model="vm.job.currentAssessment.allComplianceOptions[0].assessmentSoftware"
                                           ng-model-options="{trackBy: '$value.assessmentSoftwareCode'}">
                                    <md-option ng-value="item"
                                               ng-disabled="!item.isAvailable"
                                               ng-repeat="item in vm.assessmentSoftwareList track by item.assessmentSoftwareCode">
                                        {{item.description}}
                                    </md-option>
                                </md-select>
                            </md-input-container>
                            <md-input-container class="md-block vertically-condensed"
                                                flex-gt-sm
                                                ng-if="vm.newRecord && vm.permission_field_assessmentsoftware_view && vm.job.currentAssessment.allComplianceOptions[0].assessmentSoftwareCode == 'Other'">
                                <div layout="row" layout-wrap">
                                    <div flex="90">
                                        <label>Assessment Software</label>
                                        <input type="text" name="assessmentSoftwareOther"
                                               ng-required="vm.newRecord && vm.permission_field_assessmentsoftware_view && vm.job.currentAssessment.allComplianceOptions[0].assessmentSoftwareCode == 'Other'"
                                               ng-disabled="!vm.permission_field_assessmentsoftware_edit"
                                               ng-change="vm.assessmentSoftwareOtherChanged()"
                                               ng-model="vm.job.currentAssessment.allComplianceOptions[0].assessmentSoftwareOther" />
                                    </div>
                                    <div flex="5">
                                        <md-button class="md-icon-button" ng-click="vm.clearAssessmentSoftwareDescription(vm.job.currentAssessment.allComplianceOptions[0])"><i class="material-icons">clear</i></md-button>
                                    </div>
                                </div>
                            </md-input-container>

                            <!-- ******** Nominated Building Surveyor ******** -->
                            <md-input-container class="md-block vertically-condensed"
                                                flex-gt-sm>
                                <label>Nominated Building Surveyor</label>
                                <md-select name="nominatedBuildingSurveyor"
                                           required
                                           ng-model="vm.job.currentAssessment.assessmentProjectDetail.nominatedBuildingSurveyorId">
                                    <md-option ng-value="item.buildingSurveyorId"
                                               ng-repeat="item in vm.buildingSurveyorList track by item.buildingSurveyorId">
                                        {{item.description}}
                                    </md-option>
                                </md-select>
                                <div ng-messages="jobform.nominatedBuildingSurveyor.$error">
                                    <div ng-message="required">Nominated Building Surveyor is required.</div>
                                </div>
                            </md-input-container>

                            <!-- ******** Search Address ******* -->
                            <search-address ng-if="vm.permission_field_searchaddress_view && !vm.job.currentAssessment.assessmentProjectDetail.isManual"
                                            class="vertically-condensed"
                                            assessment="vm.job.currentAssessment"
                                            address-changed="vm.captureMapImagesAtCurrentAddress()"
                                            on-manual-address-selected="vm.clearAddress()"
                                            is-disabled="!vm.permission_field_searchaddress_edit"
                                            flex>
                            </search-address>

                            <!-- Manual address entry -->
                            <div ng-if="vm.permission_field_address_view && vm.job.currentAssessment.assessmentProjectDetail.isManual"
                                 style="background-color: #FAFAFA; margin-bottom: 15px; padding: 10px; border-radius: 5px 5px;">

                                <div class="clickable"
                                     style="display: grid; justify-items: center;  margin-bottom: 8px;"
                                     ng-click="vm.job.currentAssessment.assessmentProjectDetail.isManual = false; vm.clearAddress();">
                                    <div style="display: flex; align-items: center; justify-items: center; align-self: center;">
                                        <span style="font-size: 12px">Switch back to address search</span>
                                        <button class="feather-icon-button"
                                                style="display: block; margin-left: 5px;">
                                            <img src="/content/feather/search.svg" style="height: 17px;" />
                                        </button>
                                    </div>
                                </div>

                                <!-- Lot Type -->
                                <md-input-container class="md-block vertically-condensed vertically-condensed" flex="100">
                                    <label>Lot Type</label>
                                    <md-select name="lotType"
                                               class="vertically-condensed vertically-condensed"
                                               ng-required="true"
                                               ng-disabled="!vm.permission_field_address_edit"
                                               ng-change="vm.addressChanged()"
                                               ng-model="vm.job.currentAssessment.assessmentProjectDetail.lotType"
                                               ng-model-options="{trackBy: '$value.lotTypeCode'}">
                                        <md-option ng-repeat="lotType in vm.lotTypeList track by $index"
                                                   ng-value="lotType">
                                            {{lotType.description}}
                                        </md-option>
                                    </md-select>
                                </md-input-container>

                                <!-- Lot No -->
                                <md-input-container class="md-block vertically-condensed" flex="100">
                                    <label>{{vm.job.currentAssessment.assessmentProjectDetail.lotType.description || 'Lot' }} No.</label>
                                    <input ng-change="vm.addressChanged()"
                                           ng-required="true"
                                           ng-disabled="!vm.permission_field_address_edit"
                                           type="text"
                                           name="lotNumber"
                                           ng-model="vm.job.currentAssessment.assessmentProjectDetail.lotNumber" />
                                </md-input-container>

                                <!-- Street Number -->
                                <md-input-container class="md-block vertically-condensed" flex-gt-sm="100">
                                    <label>Street Number</label>
                                    <input type="text"
                                           name="houseNumber"
                                           ng-model="vm.job.currentAssessment.assessmentProjectDetail.houseNumber"
                                           ng-disabled="!vm.permission_field_address_edit"
                                           ng-maxlength="20"
                                           ng-change="vm.addressChanged()" />
                                    <div ng-messages="assessmentform.buildingSiteForm.addressForm.houseNumber.$error">
                                        <div ng-message="maxlength">Too many characters entered, max length is 20.</div>
                                    </div>
                                </md-input-container>

                                <!-- Street Name -->
                                <md-input-container class="md-block vertically-condensed" flex-gt-sm="100">
                                    <label>Street Name</label>
                                    <input type="text" name="streetName"
                                           ng-required="true"
                                           ng-disabled="!vm.permission_field_address_edit"
                                           ng-model="vm.job.currentAssessment.assessmentProjectDetail.streetName"
                                           ng-maxlength="400"
                                           ng-change="vm.addressChanged()" />
                                    <div ng-messages="assessmentform.buildingSiteForm.addressForm.streetName.$error">
                                        <div ng-message="maxlength">Too many characters entered, max length is 400.</div>
                                    </div>
                                </md-input-container>

                                <!-- Street Type -->
                                <search-street-type assessment="vm.job.currentAssessment"
                                                    address-changed="vm.addressChanged()"
                                                    condensed="true"
                                                    required="true"
                                                    is-disabled="!vm.permission_field_address_edit">
                                </search-street-type>

                                <!-- Suburb -->
                                <search-suburb initial-suburb-text="vm.job.currentAssessment.assessmentProjectDetail.suburb"
                                               selected-suburb="vm.selectedSuburb"
                                               suburb-changed="vm.suburbChanged()"
                                               condensed="true"
                                               required="true"
                                               is-disabled="!vm.permission_field_address_edit">
                                </search-suburb>

                                <!-- State -->
                                <md-input-container class="md-block vertically-condensed" flex="100">
                                    <label>State</label>
                                    <input ng-disabled="true"
                                           type="text"
                                           name="stateCode"
                                           ng-model="vm.job.currentAssessment.assessmentProjectDetail.stateCode" />
                                </md-input-container>

                                <!-- Postcode -->
                                <md-input-container class="md-block vertically-condensed" flex="100">
                                    <label>Postcode</label>
                                    <input ng-disabled="true"
                                           type="text"
                                           name="postcode"
                                           ng-model="vm.job.currentAssessment.assessmentProjectDetail.postcode" />
                                </md-input-container>

                            </div>

                            <!-- Non-manual Lot No input -->
                            <div layout-gt-sm="row">

                                <!-- Only display this input the lot number if null -->
                                <md-input-container ng-if="vm.showLotNumberInput == true && !vm.job.currentAssessment.assessmentProjectDetail.isManual"
                                                    class="md-block vertically-condensed"
                                                    flex>
                                    <label>Lot No.</label>
                                    <input ng-change="vm.addressChanged()"
                                           ng-disabled="!vm.permission_field_searchaddress_edit"
                                           type="text"
                                           name="lotNumber"
                                           ng-model="vm.job.currentAssessment.assessmentProjectDetail.lotNumber" />
                                </md-input-container>

                            </div>

                            <!-- Display Project Address + Custom Address Checkbox -->
                            <div layout-gt-sm="row" layout-align="start center">

                                <div layout=column flex="65">

                                    <!-- DISPLAY PROJECT ADDRESS -->
                                    <div flex="65" layout layout-wrap>
                                        <div flex="100" layout layout-align="start center">
                                            <span flex="100" style="font-size: 9px; font-weight: 100; color:#999999">Display Project Address</span>
                                        </div>
                                        <div flex="100" layout>
                                            <md-input-container class="md-block vertically-condensed" flex="100">
                                                <textarea ng-model="vm.job.currentAssessment.assessmentProjectDetail.originalDisplayAddress"
                                                          readonly></textarea>
                                            </md-input-container>
                                        </div>
                                    </div>
                                </div>

                                <!-- Custom Address Checkbox -->
                                <md-input-container ng-if="vm.permission_field_customaddress_view"
                                                    layout="column"
                                                    flex="35"
                                                    class="md-block"
                                                    style="margin: 0 0 auto auto;">
                                    <md-checkbox ng-model="vm.job.currentAssessment.assessmentProjectDetail.useCustomAddress"
                                                 ng-change="vm.useCustomAddressChanged()"
                                                 ng-disabled="!vm.permission_field_customaddress_edit"
                                                 name="useCustomAddress">
                                        Custom Project Address
                                    </md-checkbox>
                                </md-input-container>

                            </div>

                            <!-- custom project addresss -->
                            <div flex="100" layout layout-wrap ng-if="vm.permission_field_customaddress_view && vm.job.currentAssessment.assessmentProjectDetail.useCustomAddress">

                                <div flex="100" layout layout-align="start center">
                                    <span flex="100" style="font-size: 9px; font-weight: 100; color:#999999">Custom Project Address</span>
                                </div>
                                <div flex="100" layout>
                                    <md-input-container class="md-block vertically-condensed" flex="100">
                                                <textarea ng-model="vm.job.currentAssessment.assessmentProjectDetail.customDisplayAddress"
                                                          style="overflow: hidden;"
                                                          require
                                                          ng-disabled="!vm.permission_field_customaddress_edit"
                                                          name="customDisplayAddress"
                                                          rows="2"
                                                          maxlength="200"
                                                          max-rows="2" maxLines="2"
                                                          maxlines-prevent-enter="true"></textarea>
                                        <div ng-messages="jobform.customDisplayAddress.$error">
                                            <div ng-message="required">Custom Address is required.</div>
                                            <div ng-message="maxlength">Too many characters entered, max length is 100.</div>
                                            <div ng-message="maxlines">Too many lines. Max 2 lines.</div>
                                        </div>
                                    </md-input-container>
                                </div>
                            </div>

                            <!-- ******** Project Owner ******** -->
                            <md-input-container ng-if="vm.permission_field_projectowner_view"
                                                class="md-block vertically-condensed"
                                                flex-gt-sm
                                                ng-click="vm.removePOHighlight = true;">
                                <label>Project Owner</label>
                                <input type="text" name="projectOwner"
                                       ng-class="{'highlight': !vm.removePOHighlight && vm.type == vm.TYPE_COPY}"
                                       ng-model="vm.job.currentAssessment.assessmentProjectDetail.projectOwner"
                                       ng-disabled="!vm.permission_field_projectowner_edit"
                                       ng-maxlength="1000"
                                       required />
                                <div ng-messages="jobform.projectOwner.$error">
                                    <div ng-message="required">Project Owner is required.</div>
                                    <div ng-message="maxlength">Too many characters entered, max length is 1000.</div>
                                </div>
                            </md-input-container>

                            <!-- ******** Bushfire Prone ******** -->
                            <md-input-container ng-if="vm.job.currentAssessment.bushFireProneUnknown" class="md-block vertically-condensed" flex="100">
                                <label>Bushfire Prone</label>
                                <md-select name="BushfireProne"
                                           ng-model="vm.job.currentAssessment.isBushFireProne"
                                           ng-change="vm.isBushFireProneChange()"
                                           ng-required="true"
                                           required="true"
                                           ng-disabled="(vm.isLocked)">
                                    <md-option ng-value="true">
                                        Yes
                                    </md-option>
                                    <md-option ng-value="false">
                                        No
                                    </md-option>
                                </md-select>
                            </md-input-container>

                            <!-- ******** Bushfire Attack Level ******** -->
                            <md-input-container ng-if="vm.job.currentAssessment.isBushFireProne" class="md-block vertically-condensed" flex="100">
                                <label>Bushfire Attack Level</label>
                                <md-select name="BushfireAttackLevelCode"
                                           ng-model="vm.job.currentAssessment.bushfireAttackLevelCode"
                                           ng-required="true"
                                           required="true"
                                           ng-disabled="(vm.isLocked)">
                                    <md-option ng-if="vm.job.currentAssessment.isBushFireProne"
                                               ng-value="item.bushfireAttackLevelCode"
                                               ng-repeat="item in vm.bushfireAttackLevelList track by item.bushfireAttackLevelCode">
                                        {{item.description}}
                                    </md-option>
                                </md-select>
                            </md-input-container>

                            <!-- Corner Block (THR-249: Removed) -->

                            <!-- Design Template Selection -->
                            <md-input-container ng-if="vm.type == vm.TYPE_NEWJOB"
                                                class="md-block vertically-condensed"
                                                flex-gt-sm>
                                <label>Proposed Design Template</label>
                                <div layout="row">
                                    <md-select name="zoneTemplate"
                                               flex="100"
                                               ng-model="vm.job.currentAssessment.allComplianceOptions[0].proposed.buildingZonesTemplateId">
                                        <md-option ng-value="item.buildingDesignTemplateId"
                                                   ng-repeat="item in vm.buildingDesignTemplates"
                                                   ng-click="vm.applyDesignTemplate(item, vm.job.currentAssessment.allComplianceOptions[0].proposed);">
                                            {{item.templateName}}
                                        </md-option>
                                        <md-option ng-value="vm.backupZonesTemplate"
                                                   ng-if="vm.type != vm.TYPE_NEWJOB"
                                                   ng-click="vm.applyDesignTemplate(vm.backupZonesTemplate, vm.job.currentAssessment.allComplianceOptions[0].proposed);">
                                            Copy from Previous Assessment
                                        </md-option>
                                        <md-option ng-value="'BLANK_TEMPLATE'"
                                                   ng-click="vm.nullifyZoneDefaults(vm.job.currentAssessment.allComplianceOptions[0], vm.job.currentAssessment.allComplianceOptions[0].proposed, true);">
                                            Blank Design Template
                                        </md-option>

                                    </md-select>
                                </div>
                            </md-input-container>

                            <!-- Hide Everything here if a template is selected (Except for 'blank template'). -->
                            <!-- TODO: Confirm Alistair just never wants to show these... ? -->
                            <div ng-if="true == false">

                                <!-- ******** Building Description ******** -->
                                <md-autocomplete ng-if="(vm.job.currentAssessment.allComplianceOptions[0].proposed.projectDescription.projectDescriptionCode!='PDOther')"
                                                 ng-disabled="vm.disableDesignInputs();"
                                                 class="vertically-condensed"
                                                 md-input-name="projectDescriptionCode"
                                                 md-selected-item="vm.job.currentAssessment.allComplianceOptions[0].proposed.projectDescription"
                                                 md-search-text="vm.job.currentAssessment.allComplianceOptions[0].proposed.projectDescriptionSearchText"
                                                 md-items="listItem in vm.getProjectDescriptionList(vm.job.currentAssessment.allComplianceOptions[0].proposed.projectDescriptionSearchText, true)"
                                                 md-search-text-change="vm.projectDescriptionSearchChanged(vm.job.currentAssessment.allComplianceOptions[0].proposed, vm.job.currentAssessment.allComplianceOptions[0].proposed.projectDescriptionSearchText)"
                                                 md-item-text="listItem.description"
                                                 md-min-length="0"
                                                 md-select-on-match="true"
                                                 md-require-match="true"
                                                 md-no-cache="true"
                                                 required
                                                 md-floating-label="Building Description">
                                    <md-item-template>
                                        <div md-highlight-text="vm.job.currentAssessment.allComplianceOptions[0].proposed.projectDescriptionSearchText"
                                             md-highlight-flags="^i"
                                             ng-click="vm.projectDescriptionChanged(vm.job.currentAssessment.allComplianceOptions[0].proposed, listItem);">
                                            {{listItem.description}}
                                        </div>
                                    </md-item-template>
                                </md-autocomplete>
                                <md-input-container ng-if="(vm.job.currentAssessment.allComplianceOptions[0].proposed.projectDescription.projectDescriptionCode=='PDOther')"
                                                    ng-disabled="vm.disableDesignInputs();"
                                                    class="md-block vertically-condensed">
                                    <div layout="row" layout-wrap>
                                        <div flex="90">
                                            <label>Building Description Other</label>
                                            <input type="text" name="projectDescriptionCodeOther"
                                                   ng-model="vm.job.currentAssessment.allComplianceOptions[0].proposed.projectDescriptionOther"
                                                   ng-required="true" />
                                        </div>
                                        <div flex="5">
                                            <md-button class="md-icon-button" ng-click="vm.clearProjectDescription(vm.job.currentAssessment.allComplianceOptions[0].proposed)"><i class="material-icons">clear</i></md-button>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <!-- For new jobs, we only want to show fixed inputs for templates.-->
                            <div ng-if="vm.type == vm.TYPE_NEWJOB">

                                <!-- Proposed Construction Template -->
                                <building-construction-template-selector type="'construction'"
                                                                         building="vm.job.currentAssessment.allComplianceOptions[0].proposed"
                                                                         building-type="'proposed'"
                                                                         option="vm.job.currentAssessment.allComplianceOptions[0]"
                                                                         disabled="false"
                                                                         new-job="true"
                                                                         required="true">
                                </building-construction-template-selector>

                                <!-- Proposed Opening Template -->
                                <building-construction-template-selector type="'opening'"
                                                                         building="vm.job.currentAssessment.allComplianceOptions[0].proposed"
                                                                         building-type="'proposed'"
                                                                         option="vm.job.currentAssessment.allComplianceOptions[0]"
                                                                         disabled="false"
                                                                         new-job="true"
                                                                         required="true">
                                </building-construction-template-selector>

                                <building-services-template-selector building="vm.job.currentAssessment.allComplianceOptions[0].proposed"
                                                                     building-type="'proposed'"
                                                                     option="vm.job.currentAssessment.allComplianceOptions[0]"
                                                                     disabled="false"
                                                                     new-job="true"
                                                                     required="true">
                                </building-services-template-selector>

                                <!-- House Type (Optional) -->
<!--                                <div ng-if="vm.type === vm.TYPE_NEWJOB"-->
<!--                                     flex-gt-sm="100" layout>-->
<!--                                    <md-input-container class="md-block vertically-condensed" flex>-->
<!--                                        <label>Proposed House Type</label>-->
<!--                                        <input ng-change=""-->
<!--                                               class="input-required-highlight"-->
<!--                                               type="text"-->
<!--                                               name="design"-->
<!--                                               ng-model="vm.job.currentAssessment.allComplianceOptions[0].proposed.design" />-->
<!--                                    </md-input-container>-->
<!--                                </div>-->

                                <!-- Also show the reference template selector under certain conditions-->
                                <div ng-if="vm.job.currentAssessment.allComplianceOptions[0].complianceMethod.complianceMethodCode == 'CMPerfSolution' ||
                                            vm.job.currentAssessment.allComplianceOptions[0].complianceMethod.complianceMethodCode == 'CMPerfSolutionDTS'">

                                    <!-- Reference Design Template -->
                                    <md-input-container ng-if="vm.type == vm.TYPE_NEWJOB"
                                                        class="md-block vertically-condensed"
                                                        flex-gt-sm>
                                        <label>{{vm.job.currentAssessment.allComplianceOptions[0].complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference' : 'Deemed-to-Satisfy'}} Design Template</label>
                                        <div layout="row">
                                            <md-select name="zoneTemplate"
                                                       flex="100"
                                                       required
                                                       ng-model="vm.job.currentAssessment.allComplianceOptions[0].reference.buildingZonesTemplateId">
                                                <md-option ng-value="item.buildingDesignTemplateId"
                                                           ng-repeat="item in vm.buildingDesignTemplates"
                                                           ng-click="vm.applyDesignTemplate(item, vm.job.currentAssessment.allComplianceOptions[0].reference);">
                                                    {{item.templateName}}
                                                </md-option>
                                                <md-option ng-value="'BLANK_TEMPLATE'"
                                                           ng-click="vm.nullifyZoneDefaults(vm.job.currentAssessment.allComplianceOptions[0], vm.job.currentAssessment.allComplianceOptions[0].reference);">
                                                    Blank Design Template
                                                </md-option>

                                                <md-option ng-value="null"
                                                           ng-show="false"
                                                           ng-click="vm.nullifyZoneDefaults(vm.job.currentAssessment.allComplianceOptions[0], vm.job.currentAssessment.allComplianceOptions[0].reference);">

                                                </md-option>
                                            </md-select>
                                        </div>
                                    </md-input-container>

                                    <!-- Reference Construction Template -->
                                    <building-construction-template-selector type="'construction'"
                                                                             building="vm.job.currentAssessment.allComplianceOptions[0].reference"
                                                                             building-type="'reference'"
                                                                             option="vm.job.currentAssessment.allComplianceOptions[0]"
                                                                             disabled="false"
                                                                             new-job="true"
                                                                             required="true">
                                    </building-construction-template-selector>

                                    <!-- Reference Opening Template -->
                                    <building-construction-template-selector type="'opening'"
                                                                             building="vm.job.currentAssessment.allComplianceOptions[0].reference"
                                                                             building-type="'reference'"
                                                                             option="vm.job.currentAssessment.allComplianceOptions[0]"
                                                                             disabled="false"
                                                                             new-job="true"
                                                                             required="true">
                                    </building-construction-template-selector>

                                    <building-services-template-selector building="vm.job.currentAssessment.allComplianceOptions[0].reference"
                                                                         building-type="'reference'"
                                                                         option="vm.job.currentAssessment.allComplianceOptions[0]"
                                                                         disabled="false"
                                                                         new-job="true"
                                                                         required="true">
                                    </building-services-template-selector>

                                    <!-- House Type (Optional) -->
<!--                                    <div ng-if="vm.type === vm.TYPE_NEWJOB"-->
<!--                                         flex-gt-sm="100" layout>-->
<!--                                        <md-input-container class="md-block vertically-condensed" flex>-->
<!--                                            <label>{{vm.job.currentAssessment.allComplianceOptions[0].complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference' : 'Deemed-to-Satisfy'}} House Type</label>-->
<!--                                            <input ng-change=""-->
<!--                                                   class="input-required-highlight"-->
<!--                                                   type="text"-->
<!--                                                   name="design"-->
<!--                                                   ng-model="vm.job.currentAssessment.allComplianceOptions[0].reference.design" />-->
<!--                                        </md-input-container>-->
<!--                                    </div>-->

                                </div>
                            </div>

                            <!--
                                For COPY or RECERT windows we show checkboxes (with expanding fields) to
                                select different compliance options for copying.

                                UNLIKE THE ABOVE TEMPLATE SELECTION INPUTS, these do not apply templates immediately.
                                Instead, they simply record the ID (or special values like "COPY" and "BLANK") and
                                at the point of 'saving', the selected options will be applied (still on the front end(?)).
                            -->
                            <div ng-if="vm.type != vm.TYPE_NEWJOB"
                                 style="margin-bottom: 15px;">
                                <div ng-repeat="option in vm.job.currentAssessment.allComplianceOptions"
                                     style="background-color: #FAFAFA; margin-bottom: 10px; padding: 10px; border-radius: 5px 5px;">

                                    <div style="display: grid; grid-template-columns: auto auto; justify-content: space-between; align-items: center;">

                                        <div ng-if="option.isBaselineSimulation === true">
                                            Baseline
                                        </div>

                                        <md-checkbox ng-if="option.isBaselineSimulation === false"
                                                     ng-model="option.doCopy"
                                                     ng-click="vm.optionCopyClicked(option); option.isExpanded = false;"
                                                     style="margin-bottom: 0;">
                                            {{option.optionIndex == 0 ? 'Copy Baseline' : 'Copy Option ' + option.optionIndex}}
                                        </md-checkbox>

                                        <span style="margin: 0 10px; display: inline-block;"
                                              ng-if="option.doCopy == true"
                                              ng-click="option.isExpanded = !option.isExpanded;">
                                            <i ng-if="option.isExpanded"
                                               class="fa fa-caret-up" style="font-size: 18px;" />
                                            <i ng-if="(!option.isExpanded)"
                                               class="fa fa-caret-down" style="font-size: 18px;" />
                                        </span>

                                    </div>

                                    <div ng-if="option.doCopy == true && option.isExpanded">

                                        <!-- Certification -->
                                        <md-input-container ng-if="vm.permission_field_certification_view"
                                                            class="md-block vertically-condensed"
                                                            flex-gt-sm
                                                            style="margin-top: 20px;">
                                            <label>Certification</label>
                                            <md-select name="certification"
                                                       ng-required="vm.permission_field_certification_view"
                                                       ng-disabled="!vm.permission_field_certification_edit"
                                                       ng-model="option.certification"
                                                       ng-model-options="{trackBy: '$value.certificationId'}"
                                                       ng-change="option.sectorDetermination = option.certification.sectorDetermination">
                                                <md-option ng-value="item"
                                                           ng-repeat="item in vm.certificationList track by item.certificationId">
                                                    {{item.title}}
                                                </md-option>
                                            </md-select>
                                            <div ng-messages="jobform.certification.$error">
                                                <div ng-message="required">Certification is required.</div>
                                            </div>
                                        </md-input-container>

                                        <!-- ******** Sector Determination ******** -->
                                        <md-input-container class="md-block vertically-condensed"
                                                            flex="100">
                                            <label>Sector Determination</label>
                                            <md-select name="sectorDetermination"
                                                       ng-required="true"
                                                       ng-model="option.sectorDetermination"
                                                       ng-model-options="{trackBy: '$value.sectorDeterminationCode'}">
                                                <md-option ng-value="sector"
                                                           ng-repeat="sector in vm.sectorDeterminationList track by sector.sectorDeterminationCode">
                                                    {{sector.title}}
                                                </md-option>
                                            </md-select>
                                        </md-input-container>

                                        <!-- Assessment Method -->
                                        <md-input-container ng-if="vm.permission_field_assessmentmethod_view"
                                                            class="md-block vertically-condensed"
                                                            flex-gt-sm>
                                            <label>Assessment Method</label>
                                            <md-select name="preliminaryComplianceMethodCode"
                                                       ng-required="vm.permission_field_assessmentmethod_view"
                                                       ng-disabled="!vm.permission_field_assessmentmethod_edit"
                                                       ng-model="option.complianceMethod"
                                                       ng-model-options="{trackBy: '$value.complianceMethodCode'}"
                                                       ng-change="vm.complianceMethodChanged()">
                                                <md-option ng-value="item"
                                                           ng-repeat="item in vm.availableComplianceMethods() track by item.complianceMethodCode">
                                                    {{item.description}}
                                                </md-option>
                                            </md-select>
                                            <div ng-messages="jobform.preliminaryComplianceMethodCode.$error">
                                                <div ng-message="required">Assessment Method is required.</div>
                                            </div>
                                        </md-input-container>

                                        <!-- Min House Energy Rating -->
                                        <md-autocomplete md-input-name="minHouseEnergyRating"
                                                         class="vertically-condensed"
                                                         ng-required="vm.permission_field_herrequired_view && vm.minHouseEnergyRating.description != 'Other' &&
                                                                     (option.complianceMethod.complianceMethodCode == 'CMHouseEnergyRating' ||
                                                                      option.complianceMethod.complianceMethodCode=='CMPerfSolutionHER' ||
                                                                      option.complianceMethod.complianceMethodCode == 'CMPerfWAProtocolHER')"
                                                         ng-disabled="!vm.permission_field_herrequired_edit"
                                                         md-input-minlength="1"
                                                         md-min-length="0"
                                                         md-select-on-match="true"
                                                         md-selected-item="vm.tempMinHouseEnergyRating"
                                                         md-selected-item-change="vm.formatActualMinHouseEnergyRating()"
                                                         md-search-text="vm.minHouseEnergyRatingSearchText"
                                                         md-items="value in vm.availableHouseEnergyRatings | filter: vm.minHouseEnergyRatingSearchText"
                                                         md-item-text="value"
                                                         md-require-match
                                                         md-no-cache="true"
                                                         md-floating-label="Required House Energy Rating"
                                                         ng-if="vm.permission_field_herrequired_view && vm.minHouseEnergyRating.description != 'Other' &&
                                                               (option.complianceMethod.complianceMethodCode == 'CMHouseEnergyRating' ||
                                                                option.complianceMethod.complianceMethodCode=='CMPerfSolutionHER' ||
                                                                option.complianceMethod.complianceMethodCode == 'CMPerfWAProtocolHER')">
                                            <md-item-template>
                                                <span md-highlight-text="vm.minHouseEnergyRatingSearchText">{{value}}</span>
                                            </md-item-template>
                                            <div ng-messages="jobform.minHouseEnergyRating.$error">
                                                <div ng-message="required">Required house energy rating is required.</div>
                                            </div>
                                        </md-autocomplete>
                                        <md-input-container ng-if="vm.permission_field_herrequired_view && vm.minHouseEnergyRating.description=='Other'"
                                                            class="md-block vertically-condensed"
                                                            flex >
                                            <div layout="row" layout-wrap>
                                                <div flex="90">
                                                    <label>Required House Energy Rating</label>
                                                    <input type="number" name="minHouseEnergyRatingOther"
                                                           ng-model="option.requiredHouseEnergyRating"
                                                           ng-disabled="!vm.permission_field_herrequired_edit"
                                                           pattern="^\d{0,2}(?:\.\d)?$"
                                                           string-to-number
                                                           step="0.1"
                                                           max="10"
                                                           min="0"
                                                           ng-required="vm.permission_field_herrequired_view && vm.minHouseEnergyRating.description=='Other'" />
                                                </div>
                                                <div flex="5">
                                                    <md-button class="md-icon-button" ng-click="vm.clearMinHouseEnergyRating()"><i class="material-icons">clear</i></md-button>
                                                </div>
                                            </div>
                                        </md-input-container>

                                        <!-- Assessment Software -->
                                        <md-input-container ng-if="vm.permission_field_assessmentsoftware_view && option.assessmentSoftwareCode != 'Other'"
                                                            class="md-block vertically-condensed"
                                                            flex-gt-sm>
                                            <label>Assessment Software</label>
                                            <md-select name="assessmentSoftwareCode"
                                                       ng-disabled="!vm.permission_field_assessmentsoftware_edit"
                                                       ng-model="option.assessmentSoftware"
                                                       ng-model-options="{trackBy: '$value.assessmentSoftwareCode'}">
                                                <md-option ng-value="item"
                                                           ng-disabled="!item.isAvailable"
                                                           ng-repeat="item in vm.assessmentSoftwareList track by item.assessmentSoftwareCode">
                                                    {{item.description}}
                                                </md-option>
                                            </md-select>
                                        </md-input-container>
                                        <md-input-container class="md-block vertically-condensed"
                                                            flex-gt-sm
                                                            ng-if="vm.permission_field_assessmentsoftware_view && option.assessmentSoftwareCode == 'Other'">
                                            <div layout="row" layout-wrap">
                                                <div flex="90">
                                                    <label>Assessment Software</label>
                                                    <input type="text"
                                                           name="assessmentSoftwareOther"
                                                           ng-required="vm.permission_field_assessmentsoftware_view && option.assessmentSoftwareCode == 'Other'"
                                                           ng-disabled="!vm.permission_field_assessmentsoftware_edit"
                                                           ng-change="vm.assessmentSoftwareOtherChanged()"
                                                           ng-model="option.assessmentSoftwareOther" />
                                                </div>
                                                <div flex="5">
                                                    <md-button class="md-icon-button"
                                                               ng-click="vm.clearAssessmentSoftwareDescription(option)">
                                                        <i class="material-icons">clear</i>
                                                    </md-button>
                                                </div>
                                            </div>
                                        </md-input-container>

                                        <!-- Description -->
                                        <div>
                                            <md-input-container class="md-block vertically-condensed"
                                                                flex-gt-sm>
                                                <label>Description</label>
                                                <input type="text"
                                                       name="optionDescription"
                                                       ng-required="true"
                                                       maxlength="500"
                                                       ng-model="option.description" />
                                            </md-input-container>
                                        </div>

                                        <!-- Maximum Heating and Cooling Limits Toggle -->
                                        <div ng-if="option.complianceMethod.complianceMethodCode == 'CMHouseEnergyRating' ||
                                                    option.complianceMethod.complianceMethodCode == 'CMPerfSolutionHER' ||
                                                    option.complianceMethod.complianceMethodCode == 'CMPerfWAProtocolHER'"
                                             style="margin-left: 4px;">
                                            <span flex="100" style="font-size: 9px; font-weight: 100; color:#999999"
                                                  class="flex-100">
                                                Load Limits
                                            </span>
                                            <md-switch ng-model="option.heatingAndCoolingRulesetCode"
                                                       style="margin: 4px 0 20px 0"
                                                       ng-true-value="'Enabled'"
                                                       ng-false-value="'Disabled'"
                                                       class="md-warn md-block">
                                            </md-switch>
                                        </div>

                                        <!-- Proposed Copy/Recert Options -->
                                        <div style="border: 1px solid #EAEAEA; border-radius: 4px 4px; padding: 7px 4px; margin: 9px 0px;">

                                            <!-- Proposed Design Template Selection -->
                                            <md-input-container class="md-block vertically-condensed"
                                                                style="margin-top: 15px;"
                                                                flex>
                                                <label>Proposed Building Design Template</label>
                                                <div layout="row">
                                                    <md-select name="zoneTemplate"
                                                               flex="100"
                                                               ng-model="option.proposed.newBuildingZoneTemplateId">
                                                        <md-option ng-value="item.buildingDesignTemplateId"
                                                                   ng-repeat="item in vm.buildingDesignTemplates">
                                                            {{item.templateName}}
                                                        </md-option>
                                                        <md-option ng-value="'COPY'">
                                                            Copy from Previous Assessment
                                                        </md-option>
                                                        <md-option ng-value="'BLANK_TEMPLATE'">
                                                            Blank Design Template
                                                        </md-option>
                                                    </md-select>
                                                </div>
                                            </md-input-container>

                                            <!-- ******** Proposed Building Construction Templates ******** -->
                                            <md-input-container class="md-block vertically-condensed" flex>
                                                <label>Proposed Building Construction Template</label>
                                                <md-select name="proposedConstructionTemplate"
                                                           ng-model="option.proposed.newConstructionTemplateId"
                                                           required>
                                                    <md-option ng-value="item.buildingConstructionTemplateId"
                                                               ng-repeat="item in vm.buildingConstructionTemplates">
                                                        {{item.templateName}}
                                                    </md-option>
                                                    <md-option ng-value="'COPY'">
                                                        Copy from Previous Assessment
                                                    </md-option>
                                                    <md-option ng-value="'BLANK_TEMPLATE'">
                                                        Blank Construction Template
                                                    </md-option>
                                                </md-select>
                                            </md-input-container>

                                            <!-- ******** Proposed Building Opening Templates ******** -->
                                            <md-input-container class="md-block vertically-condensed" flex>
                                                <label>Proposed Building Opening Template</label>
                                                <md-select name="proposedOpeningTemplate"
                                                           ng-model="option.proposed.newOpeningTemplateId"
                                                           required>
                                                    <md-option ng-value="item.buildingConstructionTemplateId"
                                                               ng-repeat="item in vm.buildingOpeningTemplates">
                                                        {{item.templateName}}
                                                    </md-option>
                                                    <md-option ng-value="'COPY'">
                                                        Copy from Previous Assessment
                                                    </md-option>
                                                    <md-option ng-value="'BLANK_TEMPLATE'">
                                                        Blank Opening Template
                                                    </md-option>
                                                </md-select>
                                            </md-input-container>

                                            <!-- ******** Proposed Building Services Templates ******** -->
                                            <md-input-container class="md-block vertically-condensed" flex>
                                                <label>Proposed Building Services Template</label>
                                                <md-select name="proposedServicesTemplate"
                                                           ng-model="option.proposed.newServicesTemplateId"
                                                           required>
                                                    <md-option ng-value="item.buildingServicesTemplateId"
                                                               ng-repeat="item in vm.buildingServicesTemplates">
                                                        {{item.templateName}}
                                                    </md-option>
                                                    <md-option ng-value="'COPY'">
                                                        Copy from Previous Assessment
                                                    </md-option>
                                                    <md-option ng-value="'BLANK_TEMPLATE'">
                                                        Blank Services Template
                                                    </md-option>
                                                </md-select>
                                            </md-input-container>
                                        </div>

                                        <!-- Referenced Copy/Recert Option -->
                                        <div ng-if="option.complianceMethod.complianceMethodCode == 'CMPerfSolution' || option.complianceMethod.complianceMethodCode == 'CMPerfSolutionDTS'"
                                             style="border: 1px solid #EAEAEA; border-radius: 4px 4px; padding: 20px 4px 7px 4px; margin: 9px 0px; ">

                                            <!-- Reference Zone Template Selection (If Required)-->
                                            <md-input-container class="md-block vertically-condensed"
                                                                flex>
                                                <label>{{option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building Design Template' : 'Deemed-to-Satisfy Building Design Template'}}</label>
                                                <div layout="row">
                                                    <md-select name="zoneTemplate"
                                                               flex="100"
                                                               ng-model="option.reference.newBuildingZoneTemplateId">
                                                        <md-option ng-value="item.buildingDesignTemplateId"
                                                                   ng-repeat="item in vm.buildingDesignTemplates">
                                                            {{item.templateName}}
                                                        </md-option>
                                                        <md-option ng-value="'COPY'">
                                                            Copy from Previous Assessment
                                                        </md-option>
                                                        <md-option ng-value="'BLANK_TEMPLATE'">
                                                            Blank Design Template
                                                        </md-option>
                                                    </md-select>
                                                </div>
                                            </md-input-container>

                                            <!-- ******** Reference Building Construction Templates ******** -->
                                            <md-input-container class="md-block vertically-condensed" flex>
                                                <label>{{option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building Construction Template' : 'Deemed-to-Satisfy Building Construction Template'}}</label>
                                                <md-select name="proposedConstructionTemplate"
                                                           ng-model="option.reference.newConstructionTemplateId"
                                                           required>
                                                    <md-option ng-value="item.buildingConstructionTemplateId"
                                                               ng-repeat="item in vm.buildingConstructionTemplates">
                                                        {{item.templateName}}
                                                    </md-option>
                                                    <md-option ng-value="'COPY'">
                                                        Copy from Previous Assessment
                                                    </md-option>
                                                    <md-option ng-value="'BLANK_TEMPLATE'">
                                                        Blank Construction Template
                                                    </md-option>
                                                </md-select>
                                            </md-input-container>

                                            <!-- ******** Reference Building Opening Templates ******** -->
                                            <md-input-container class="md-block vertically-condensed" flex>
                                                <label>{{option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building Opening Template' : 'Deemed-to-Satisfy Building Opening Template'}}</label>
                                                <md-select name="proposedOpeningTemplate"
                                                           ng-model="option.reference.newOpeningTemplateId"
                                                           required>
                                                    <md-option ng-value="item.buildingConstructionTemplateId"
                                                               ng-repeat="item in vm.buildingOpeningTemplates">
                                                        {{item.templateName}}
                                                    </md-option>
                                                    <md-option ng-value="'COPY'">
                                                        Copy from Previous Assessment
                                                    </md-option>
                                                    <md-option ng-value="'BLANK_TEMPLATE'">
                                                        Blank Opening Template
                                                    </md-option>
                                                </md-select>
                                            </md-input-container>

                                            <!-- ******** Reference Building Services Templates ******** -->
                                            <md-input-container class="md-block vertically-condensed" flex>
                                                <label>{{option.complianceMethod.complianceMethodCode == 'CMPerfSolution' ? 'Reference Building Services Template' : 'Deemed-to-Satisfy Building Services Template'}}</label>
                                                <md-select name="referenceServicesTemplate"
                                                           ng-model="option.reference.newServicesTemplateId"
                                                           required>
                                                    <md-option ng-value="item.buildingServicesTemplateId"
                                                               ng-repeat="item in vm.buildingServicesTemplates">
                                                        {{item.templateName}}
                                                    </md-option>
                                                    <md-option ng-value="'COPY'">
                                                        Copy from Previous Assessment
                                                    </md-option>
                                                    <md-option ng-value="'BLANK_TEMPLATE'">
                                                        Blank Services Template
                                                    </md-option>
                                                </md-select>
                                            </md-input-container>

                                        </div>

                                        <!-- Note that unlike the add-compliance-option modal, this will copy proposed AND reference data -->
                                        <!-- Proposed Copy Annual Energy Loads -->
                                        <md-checkbox ng-if="(vm.baseline.complianceMethod.complianceMethodCode !== 'CMElementalProv' || 
                                                             vm.baseline.complianceMethod.complianceMethodCode !== 'CMPerfWAProtocolEP') &&
                                                            (vm.option.complianceMethod.complianceMethodCode !== 'CMElementalProv' ||
                                                             vm.option.complianceMethod.complianceMethodCode !== 'CMPerfWAProtocolEP')"
                                                     ng-model="option.copyAnnualEnergyLoads"
                                                     style="display: block;  margin-bottom: 10px; margin-top: 20px;"
                                                     flex>
                                            Copy Annual Energy Loads
                                        </md-checkbox>

                                        <!-- Proposed Copy Assessment Files -->
                                        <md-checkbox ng-model="option.copyAssessmentFiles"
                                                     style="display: block;  margin-bottom: 10px; margin-top: 20px;"
                                                     flex>
                                            Copy Assessment Files
                                        </md-checkbox>

                                        <!-- Proposed Copy Multi-Sim Data -->
                                        <md-checkbox ng-model="option.copyMultiSim"
                                                     style="display: block;  margin-bottom: 10px; margin-top: 20px;"
                                                     flex>
                                            Copy Multi-Sim Data
                                        </md-checkbox>

                                        <!-- Copy drawings? -->
                                        <md-checkbox ng-model="option.copyDrawings"
                                                     style="display: block;  margin-bottom: 10px; margin-top: 20px;"
                                                     flex>
                                            Copy Drawings
                                        </md-checkbox>

                                    </div>
                                </div>
                            </div>

                            <!-- ******** Priority ******** -->
                            <md-input-container class="md-block vertically-condensed" flex-gt-sm>
                                <label>Priority</label>
                                <md-select name="priority"
                                           ng-model="vm.job.currentAssessment.priorityCode">
                                    <md-option ng-value="item.priorityCode"
                                               ng-repeat="item in vm.priorityList track by item.priorityCode">
                                        {{item.description}}
                                    </md-option>
                                </md-select>
                            </md-input-container>

                            <!-- ******** Assigned To Assessor User ******** -->
                            <div layout-gt-sm="row" ng-if="vm.permission_field_assignedassessor_view">
                                <md-autocomplete md-input-name="assessorUserId"
                                                 class="vertically-condensed"
                                                 md-input-minlength="2"
                                                 md-min-length="0"
                                                 md-selected-item="vm.job.assessorUser"
                                                 md-search-text="vm.assignedToAssessorUserIdSearchText"
                                                 md-items="item in vm.getAssessors(vm.assignedToAssessorUserIdSearchText)"
                                                 md-item-text="item.fullName"
                                                 md-require-match
                                                 ng-disabled="!vm.permission_field_assignedassessor_edit"
                                                 md-floating-label="Assigned Assessor"
                                                 flex-gt-sm>
                                    <md-item-template>
                                        <span md-highlight-text="vm.assignedToAssessorUserIdSearchText">{{item.fullName}}</span>
                                    </md-item-template>
                                </md-autocomplete>
                            </div>

                            <!-- Design Changes -->
                            <md-input-container ng-if="vm.type === 'RECERTIFY'"
                                                ng-click="vm.removeDesignChangesHighlight = true"
                                                class="md-block"
                                                flex="100">
                                <label>Design Changes</label>
                                <md-select name="designChanges"
                                           ng-required="true"
                                           ng-class="{'input-required-highlight': !vm.removeDesignChangesHighlight && 
                                                       (vm.job.currentAssessment.recertificationDesignChanges == null || vm.job.currentAssessment.recertificationDesignChanges.length == 0)}"
                                           ng-model="vm.job.currentAssessment.recertificationDesignChanges"
                                           multiple="true">
                                    <md-option ng-value="item.designChangeId"
                                               ng-repeat="item in vm.designChangeList">
                                        {{item.description}}
                                    </md-option>
                                </md-select>
                            </md-input-container>

                            <!-- ******** Notes ******** -->
                            <md-input-container class="md-block vertically-condensed">
                                <label>Notes For Assessor</label>
                                <textarea type="text" name="notes"
                                          ng-model="vm.job.currentAssessment.notes"
                                          rows="3" />
                                <div ng-messages="jobform.notes.$error">
                                    <div ng-message="required">Notes are required.</div>
                                </div>
                            </md-input-container>

                        </md-card-content>
                    </md-card>

                    <!-- Attach Files -->
                    <div ng-if="vm.job.currentAssessment.allComplianceOptions[0].copyDrawings !== true">
                        <md-card>
                            <table style="border-collapse: collapse; margin: -10px 20px 5px 20px;">
                                <tbody>

                                <tr ng-repeat="item in vm.attachedDrawings track by $index"
                                    lr-drag-src="attachedFiles"
                                    lr-drop-target="attachedFiles"
                                    lr-drag-data="vm.attachedDrawings"
                                    lr-drop-success="vm.handleAttachmentReorder()"
                                    lr-match-property="id"
                                    lr-index="vm.attachedDrawings.indexOf(item)"
                                    lr-match-value="{{item.id}}">

                                    <!-- File Row -->
                                    <td class="file-row" ng-show="item.attachmentUploadProgress != null || item.attachment != null">
                                        <!-- Document Icon -->
                                        <img src="../../../content/images/file.png"
                                             style="opacity: 0.35; margin-right: 3px; width: 20px; height: auto; cursor: pointer;"
                                             ng-show="item.attachment!=null" 
                                             ng-click="vm.downloadFile(item.attachment)"/>
                                        <!-- Name -->
                                        <button class="file-name-button" 
                                                ng-show="(item.attachmentUploadProgress != null || item.attachment != null) && item.attachment!=null"
                                                ng-click="vm.downloadFile(item.attachment)">
                                            {{item.attachment.fileName}}
                                        </button>
                                        <!-- Menu -->
                                        <md-menu>
                                            <img ng-show="(item.attachmentUploadProgress != null || item.attachment != null) && item.attachment!=null"
                                                 class="file-arrow-button"
                                                 md-menu-origin
                                                 ng-click="$mdOpenMenu()"
                                                 src="/content/images/arrow-up-skinny.png"/>
                                            <md-menu-content>
                                                <!-- Download Attachment -->
                                                <md-menu-item><md-button ng-click="vm.downloadFile(item.attachment)">
                                                    Download Attachment
                                                </md-button></md-menu-item>
                                                <md-menu-divider></md-menu-divider>
                                                <!-- Delete button -->
                                                <md-menu-item><md-button ng-click="vm.deleteFile(item)">
                                                    <span style="color: orangered">Delete</span>
                                                </md-button></md-menu-item>
                                            </md-menu-content>
                                        </md-menu>
                                        <!-- Progress Indicator -->
                                        <md-progress-circular style="display:inline-block;"
                                                              md-diameter="20"
                                                              md-mode="{{vm.progressMode}}"
                                                              value="{{item.attachmentUploadProgress}}">
                                        </md-progress-circular>
                                    </td>
                                </tr>
                                </tbody>

                            </table>
                        </md-card>
                        <md-card style="outline: 1px dashed lightgrey; outline-offset: -10px; background-color: white;"
                                 data-title="Attachment" class="text-left"
                                 ngf-drop="vm.uploadFile($files, 'attachment')"
                                 ngf-multiple="true"
                                 ngf-drag-over-class="'file-dragover'">
                            <label style="text-align: center; padding-top: 20px;">
                                Attach Files
                            </label>
                            <md-card-content layout="row" layout-align="center">
                                <div>
                                    <md-button class="md-raised"
                                               ngf-select="vm.uploadFile($files, 'attachment')"
                                               ngf-accept="'.pdf'"
                                               ngf-multiple="true"><i class="material-icons" style="text-align: center; vertical-align: middle">file_upload</i>Upload</md-button>
                                    <label style="display:inline-block; color: darkgrey; font-family: Roboto, 'Helvetica Neue', sans-serif; font-size: 1.6rem;">or drag here.</label>
                                </div>
                            </md-card-content>
                        </md-card>
                    </div>

                </div>
            </div>

            <div data-cc-widget-button-bar
                 data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy"
                     data-cc-spinner="vm.spinnerOptions"
                     style="margin-left: auto; margin-right: auto;"></div>

                <md-button class="md-raised md-primary"
                           ng-disabled="vm.isBusy || jobform.$invalid || vm.uploadBusy || !vm.clientOptionsConfigured || (vm.permission_field_searchaddress_view && vm.job.currentAssessment.assessmentProjectDetail.streetName == null)"
                           ng-show="vm.job.deleted!=true"
                           ng-click="vm.save()">
                    {{vm.type == vm.TYPE_RECERTIFY ? "Recertify" : vm.type == vm.TYPE_COPY ? "Copy" : "Save"}}
                </md-button>

                <md-button class="md-raised md-primary"
                           ng-disabled="vm.isBusy || jobform.$invalid || vm.uploadBusy || !vm.clientOptionsConfigured || (vm.permission_field_searchaddress_view && vm.job.currentAssessment.assessmentProjectDetail.streetName == null)"
                           ng-show="vm.job.deleted !=true && vm.newRecord"
                           ng-click="vm.save(true)">
                    Save and Add
                </md-button>
                <md-button class="md-raised"
                           ng-disabled="vm.isBusy || vm.uploadBusy"
                           ng-click="vm.cancel()">
                    Cancel
                </md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>

</form>

<!-- This is a div only shown for milliseconds when we need to auto-render a map image on confirmed address change! #Aerial Image -->
<!-- 
    NOTE!!!!! THESE DIVS WILL ACTUALLY ONLY BE USED IF THIS NEW JOB WINDOW 
    IS SOMEHOW LAUNCHED WITH THE 'topnavremake.html' FILE ALSO BEING PRESENT IN THE DOM! 
    REFER TO THAT FILE FOR MORE INFO WHY
-->
<div id="remote_map" 
     style="width: 1600px; height: 1200px; display: none; visibility: hidden; overflow: hidden; position: absolute; left: 0; top: 0; margin: 0; padding: 0;"></div>
<div id="remote_site_map" 
     style="width: 1600px; height: 1200px; display: none; visibility: hidden; overflow: hidden; position: absolute; left: 0; top: 0; margin: 0; padding: 0;"></div>

<style>

    .important-file-upload > file-upload > div > div > md-input-container > md-select {
        background-color: #e8f3de;
        border: none;
        outline: none;
    }

    .file-row {
        margin-bottom: 5px;
        display: flex;
        align-items: center;
    }

        .file-row:hover .file-arrow-button {
            visibility: visible;
        }
        .file-row:hover .file-name-button {
            text-decoration: underline
        }

        .file-name-button {
            background: none;
            border: none;
            text-decoration: none;
            font-size: 16px;
            color: var(--thermarate-green);
        }

        .file-arrow-button {
            visibility: hidden;
            opacity: 0.8;
            margin-left: 4px;
            margin-bottom: -4px;
            width: 12px;
            height: auto;
            padding: 7px 4px;
            border-radius: 4px;
            cursor: pointer;
            transform: rotate(180deg);
        }
        .file-arrow-button:hover {
            background-color: #d1d1d1;
        }

</style>