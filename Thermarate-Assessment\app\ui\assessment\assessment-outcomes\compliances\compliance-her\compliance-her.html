<div ng-form="complianceHerForm">

    <!-- General Info Table-->
    <table flex="100"
           style="margin-bottom: 10px;">
        <thead>
            <tr>
                <th style="width: 160px;"></th>
                <th></th>
            </tr>
        </thead>
        <tbody>

            <!-- Certification -->
            <tr ng-if="vm.permission_field_certification_view">
                <td style="font-weight:bold;">Certification</td>
                <td>
                    <md-select name="certification"
                               ng-required="true"
                               class="vertically-condensed"
                               style="width: 530px;"
                               ng-disabled="vm.isLocked || vm.permission_field_certification_edit == false"
                               ng-model="vm.complianceData.certification"
                               ng-model-options="{trackBy: '$value.certificationId'}"
                               ng-change="vm.updateDataLinkedToCertification()">
                    <md-option ng-value="item"
                               ng-repeat="item in vm.certificationList track by item.certificationId">
                        {{item.title}}
                    </md-option>
                </md-select></td>
            </tr>

            <!-- Sector Determination -->
            <tr>
                <td style="font-weight:bold;">Sector Determination</td>
                <td>
                    <md-select name="sectorDetermination"
                               class="vertically-condensed"
                               style="width: 530px;"
                               ng-required="true"
                               ng-disabled="vm.isLocked"
                               ng-model="vm.complianceData.sectorDetermination"
                               ng-model-options="{trackBy: '$value.sectorDeterminationCode'}">
                        <md-option ng-value="sector"
                                   ng-repeat="sector in vm.sectorDeterminationList track by sector.sectorDeterminationCode">
                            {{sector.title}}
                        </md-option>
                    </md-select>
                </td>
            </tr>

            <!-- Assessment Method -->
            <tr ng-if="vm.permission_field_assessmentmethod_view">
                <td style="font-weight:bold;">
                    Assessment Method
                </td>
                <td>
                    <md-select name="preliminaryComplianceMethodCode"
                               class="vertically-condensed"
                               style="width: 530px;"
                               flex="100"
                               ng-disabled="vm.isLocked || vm.permission_field_assessmentmethod_edit == false"
                               ng-model="vm.complianceData.complianceMethod"
                               ng-model-options="{trackBy: '$value.complianceMethodCode'}"
                               placeholder="'Select a Method'"
                               ng-change="vm.complianceTypeChanged(vm.complianceData);">
                        <md-option ng-repeat="item in vm.availableComplianceMethods() track by item.complianceMethodCode"
                                   ng-value="item">
                            {{item.description}}
                        </md-option>
                    </md-select>
                </td>
            </tr>

            <!-- Assessment Software -->
            <tr ng-if="vm.permission_field_assessmentsoftware_view">
                <td style="font-weight:bold;">
                    Assessment Software
                </td>
                <td>
                    <md-select name="assessmentSoftwareCode"
                               ng-if="vm.assessmentSoftwareList !== null"
                               ng-disabled="vm.isLocked || vm.permission_field_assessmentsoftware_edit == false"
                               class="vertically-condensed"
                               style="width: 530px;"
                               flex="100"
                               ng-model="vm.complianceData.assessmentSoftware"
                               ng-model-options="{trackBy: '$value.assessmentSoftwareCode'}">
                        <md-option ng-value="item"
                                   ng-disabled="!vm.isSoftwareAvailable(item.assessmentSoftwareCode)"
                                   ng-repeat="item in vm.assessmentSoftwareList track by item.assessmentSoftwareCode">
                            {{item.description}}
                        </md-option>
                    </md-select>
                </td>
            </tr>

            <!-- Description -->
            <tr>
                <td style="font-weight:bold;">
                    Description
                </td>
                <td class="vertically-condensed">
                    <md-input-container class="md-block vertically-condensed kindly-remove-error-spacer"
                                        style="min-height: 30px;" flex="100">
                        <textarea maxlength="500"
                                  class="vertically-condensed"
                                  style="width: 530px; white-space: pre-wrap;"
                                  ng-model="vm.complianceData.description"
                                  ng-required="true && !vm.complianceData.description"
                                  ng-focus="vm.hidePlaceholder = true;"
                                  ng-blur="vm.hidePlaceholder = false;"
                                  ng-disabled="vm.isLocked"
                                  placeholder="{{((vm.complianceData.description == null || vm.complianceData.description == '')) && !vm.hidePlaceholder ?  !vm.isOption ? 'Baseline specifications' : 'Option ' + vm.determineUINumber() + ' specifications' : ''}}"
                                  name="Description"></textarea>
                        <div ng-if="complianceHerForm.description.$error"
                             ng-messages="complianceHerForm.description.$error">
                            <div ng-message="required">Field is required.</div>
                        </div>
                    </md-input-container>
                </td>
            </tr>

            <!-- Update Drawings -->
            <tr ng-if="vm.isOption">
                <td style="font-weight:bold;">Drawings</td>
                <td>
                    <md-input-container class="md-block vertically-condensed vertically-condensed-ex">
                        <md-select name="UpdatedDrawingsRequired"
                                   style="margin-top: auto;
                                          margin-bottom: auto;
                                          width: 530px;"
                                   ng-model="vm.complianceData.updatedDrawingsRequired"
                                   ng-disabled="vm.isLocked"
                                   ng-required="true">
                            <md-option ng-value="true">Updated drawings required</md-option>
                            <md-option ng-value="false">Copy from Baseline</md-option>
                        </md-select>
                    </md-input-container>
                </td>
            </tr>

            <!-- New Purchase Order -->
            <tr ng-if="vm.isOption && vm.assessment.job.client.clientDefault.purchaseOrderCode != 'NotRequired'">
                <td style="font-weight:bold;">
                    <span class="full-text">New Purchase Order</span>
                    <span class="short-text">
                        New PO
                        <md-tooltip>
                            New Purchase Order
                        </md-tooltip>
                    </span>
                </td>
                <td>
                    <md-input-container class="md-block vertically-condensed">
                        <md-checkbox ng-model="vm.complianceData.newPurchaseOrderRequired"
                                     style="margin-bottom: 1px; margin-top: 1px;"
                                     ng-disabled="vm.isLocked">
                        </md-checkbox>
                    </md-input-container>
                </td>
            </tr>

            <!-- Markup Required? -->
            <tr ng-if="vm.isOption">
                <td style="font-weight:bold;">Mark-Up</td>
                <td>
                    <file-upload class="vertically-condensed-ex"
                                 is-locked="vm.isLocked"
                                 job-files="vm.jobFiles"
                                 file-object="vm.complianceData.proposed"
                                 prop-name="markupFile"
                                 form-name="ProposedMarkupFile"
                                 is-required="false"
                                 required-message="''"
                                 assessment="vm.assessment"
                                 job="vm.assessment.job"
                                 category="'Building Data'"
                                 classification="'Markup'">
                    </file-upload>
                </td>
            </tr>

        </tbody>
    </table>

    <!-- Assessment Data Table-->
    <table class="table-striped table-hover table-condensed"
           ng-class="{'shadow-z-1': true }"
           style="margin-bottom: 2rem;">

        <thead>

            <tr>

                <!-- Building Floor Areas -->
                <th style="text-align: center;">
                    <span class="full-text">Building Floor Areas (m<sup>2</sup>)</span>
                    <span class="short-text">
                        Floor Areas (m<sup>2</sup>)
                        <md-tooltip>
                            Building Floor Areas (m<sup>2</sup>)
                        </md-tooltip>
                    </span>
                </th>

                <!-- Load Limits -->
                <th ng-if="vm.complianceData.complianceMethod.complianceMethodCode !== 'CMPerfELL'"
                    style="text-align: center;">
                    Load Limits
                </th>

                <!-- Annual Energy Loads -->
                <th style="text-align: center;">
                    <span class="full-text">Annual Energy Loads ({{vm.complianceData.assessmentSoftware.energyLoadUnits}}/m<sup>2</sup>)</span>
                    <span class="short-text">
                        Loads ({{vm.complianceData.assessmentSoftware.energyLoadUnits}}/m<sup>2</sup>)
                        <md-tooltip>
                            Annual Energy Loads ({{vm.complianceData.assessmentSoftware.energyLoadUnits}}/m<sup>2</sup>)
                        </md-tooltip>
                    </span>
                </th>

                <!-- HER -->
                <th ng-if="vm.complianceData.complianceMethod.complianceMethodCode !== 'CMPerfELL'"
                    style="text-align: center;">
                    <span class="full-text" style=" text-align: center;">House Energy Rating</span>
                    <span class="short-text" style=" text-align: center;">
                        HER
                        <md-tooltip>
                            House Energy Rating
                        </md-tooltip>
                    </span>
                </th>

                <!-- Valid (Override) -->
                <th style="text-align: center;" ng-if="vm.permission_field_valid_view">
                    <span class="full-text">Valid</span>
                    <span class="short-text">
                        Valid
                    </span>
                </th>

                <!-- Show to Client -->
                <th style="text-align: center;" ng-if="!vm.complianceData.isBaselineSimulation && vm.permission_field_showtoclient_view">
                    <span class="full-text">Show to Client</span>
                    <span class="short-text">
                        Show to Client
                    </span>
                </th>
            </tr>

        </thead>
        <tbody>
            <tr>

                <!-- Building Floor Areas -->
                <td style="vertical-align: top;">
                    <table>
                        <thead>
                            <tr>
                                <th>Space</th>
                                <th style="text-align:center;">(m<sup>2</sup>)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Conditioned Area -->
                            <tr>
                                <td>Conditioned</td>

                                <!-- Conditioned Area -->
                                <td style="text-align: center;">
                                    <md-input-container class="md-block vertically-condensed"
                                                        style="display: flex;">
                                        <input type="text" name="ProposedHeating"
                                               ng-value="vm.complianceData.proposed.conditionedFloorArea.toFixed(1)"
                                               class="compliance-row-table-input-m"
                                               ng-disabled="true"/>
                                    </md-input-container>
                                </td>

                            </tr>

                            <!-- Unconditioned Area -->
                            <tr>
                                <td>Unconditioned</td>

                                <!-- Unconditioned Area -->
                                <td style="text-align: center;">
                                    <md-input-container class="md-block vertically-condensed"
                                                        style="display: flex;">
                                        <input type="text" name="ProposedHeating"
                                               ng-value="vm.complianceData.proposed.unconditionedFloorArea.toFixed(1)"
                                               class="compliance-row-table-input-m"
                                               ng-disabled="true"/>
                                    </md-input-container>
                                </td>
                            </tr>

                            <!-- Garage -->
                            <tr>
                                <td>Garage</td>

                                <!-- Garage Area -->
                                <td style="text-align: center;">
                                    <md-input-container class="md-block vertically-condensed"
                                                        style="display: flex;">
                                        <input type="text" name="ProposedGarage"
                                               ng-value="vm.complianceData.proposed.attachedGarageFloorArea.toFixed(1)"
                                               class="compliance-row-table-input-m"
                                               ng-disabled="true"/>
                                    </md-input-container>
                            </td>

                            </tr>

                            <!-- Total -->
                            <tr>
                                <td>Total</td>

                                <!-- Total Area -->
                                <td  style="text-align: center;">
                                    <md-input-container class="md-block vertically-condensed"
                                                        style="display: flex;">
                                        <input type="text" name="ProposedHeating"
                                               ng-value="(vm.complianceData.proposed.conditionedFloorArea + vm.complianceData.proposed.unconditionedFloorArea) .toFixed(1)"
                                               class="compliance-row-table-input-m"
                                               ng-disabled="true"/>
                                    </md-input-container>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                </td>

                <!-- Load Limits -->
                <td ng-if="vm.complianceData.complianceMethod.complianceMethodCode !== 'CMPerfELL'">
                    <!-- Maximum Heating and Cooling Limits Toggle -->
                    <md-switch ng-model="vm.complianceData.heatingAndCoolingRulesetCode"
                               style="display: grid; justify-content: center;"
                               ng-true-value="'Enabled'"
                               ng-false-value="'Disabled'"
                               class="md-warn md-block"
                               ng-disabled="vm.isLocked"
                               ng-change="vm.calculateCompliance();">
                    </md-switch>
                </td>

                <!-- Annual Energy Loads -->
                <td style="vertical-align: top;">
                    <table>
                        <thead>
                            <tr>
                                <th>Type</th>
                                <th style="text-align:center;">
                                    Calculated
                                    <i ng-if="vm.permission_action_overrideenergyloads"
                                       class="fa fa-wrench clickable"
                                       style="margin-left: 10px;"
                                       ng-click="vm.showLoadsUpdateModal(vm.complianceData.proposed, 'proposed', 'Calculated')"/>
                                </th>
                                <th ng-if="vm.complianceData.heatingAndCoolingRulesetCode !== 'Disabled' || vm.complianceData.complianceMethod.complianceMethodCode === 'CMPerfELL'"
                                    style="text-align:center;">
                                    {{ vm.complianceData.complianceMethod.complianceMethodCode === 'CMPerfELL' ? 'Load Limit' : 'Maximum' }}

                                    <!-- I don't think it makes sense for this to exist, since these values are ALWAYS calculated ... -->
<!--                                    <i ng-if="vm.complianceData.complianceMethod.complianceMethodCode !== 'CMPerfELL'"-->
<!--                                       class="fa fa-wrench clickable"-->
<!--                                       style="margin-left: 20px;"-->
<!--                                       ng-click="vm.showLoadsUpdateModal(vm.complianceData.reference, 'reference')"/>-->
                                </th>
                            </tr>
                        </thead>
                        <tbody>

                            <!-- Heating -->
                            <tr>
                                <td>Heating </td>

                                <!-- Proposed Heating -->
                                <td>
                                    <md-input-container class="md-block vertically-condensed"
                                                        style="display: flex;">
                                        <input type="text"
                                               ng-value="vm.complianceData.proposed.heating.toFixed(1)"
                                               ng-style="{'color': vm.complianceData.proposed.heating > vm.complianceData.reference.heating && vm.complianceData.reference.heating != -1 ? 'red' : ''}"
                                               class="compliance-row-table-input-m"
                                               ng-disabled="true" />
                                    </md-input-container>
                                </td>

                                <!-- Reference Heating -->
                                <td ng-if="vm.complianceData.heatingAndCoolingRulesetCode !== 'Disabled' || vm.complianceData.complianceMethod.complianceMethodCode === 'CMPerfELL'">
                                    <md-input-container ng-if="vm.complianceData.reference.heating != -1"
                                                        class="md-block vertically-condensed"
                                                        style="display: flex;">
                                        <input disabled
                                               class="compliance-row-table-input-m"
                                               ng-value="vm.complianceData.reference.heating.toFixed(1)" />
                                    </md-input-container>
                                    <md-input-container ng-if="vm.complianceData.reference.heating == -1"
                                                        class="md-block vertically-condensed"
                                                        style="display: flex;">
                                        <input disabled
                                               class="compliance-row-table-input-m"
                                               ng-value="'N/A'"/>
                                    </md-input-container>
                                </td>
                            </tr>

                            <!-- Cooling -->
                            <tr>
                                <td>Cooling</td>

                                <!-- Proposed Cooling -->
                                <td>
                                    <md-input-container class="md-block vertically-condensed"
                                                        style="display: flex;">
                                        <input type="text"
                                               class="compliance-row-table-input-m"
                                               ng-value="vm.complianceData.proposed.cooling.toFixed(1)"
                                               ng-style="{'color': vm.complianceData.proposed.cooling > vm.complianceData.reference.cooling && vm.complianceData.reference.cooling != -1 ? 'red' : ''}"
                                               ng-disabled="true" />
                                        <div ng-messages="complianceHerForm.proposed.cooling.$error">
                                            <div ng-message="required"></div>
                                        </div>
                                    </md-input-container>
                                </td>

                                <!-- Reference Cooling Limit -->
                                <td ng-if="vm.complianceData.heatingAndCoolingRulesetCode !== 'Disabled' || vm.complianceData.complianceMethod.complianceMethodCode === 'CMPerfELL'">
                                    <md-input-container ng-if="vm.complianceData.reference.cooling != -1"
                                                        class="md-block vertically-condensed"
                                                        style="display: flex;">
                                        <input disabled
                                               class="compliance-row-table-input-m"
                                               ng-value="vm.complianceData.reference.cooling.toFixed(1)" />
                                    </md-input-container>
                                    <div ng-if="vm.complianceData.reference.cooling == -1">

                                    </div>
                                    <md-input-container ng-if="vm.complianceData.reference.cooling == -1"
                                                        class="md-block vertically-condensed"
                                                        style="display: flex;">
                                        <input disabled
                                               class="compliance-row-table-input-m"
                                               ng-value="'N/A'"/>
                                    </md-input-container>
                                </td>
                            </tr>

                            <!-- Total -->
                            <tr>
                                <td>Total</td>

                                <!-- Proposed Calculated Energy -->
                                <td>
                                    <md-input-container class="md-block vertically-condensed"
                                                        style="display: flex;">
                                        <input ng-value="vm.complianceData.proposed.totalEnergyLoad.toFixed(1)"
                                               ng-disabled="true"
                                               class="compliance-row-table-input-m"
                                               ng-style="{'color': vm.complianceData.proposed.totalEnergyLoad > vm.complianceData.reference.totalEnergyLoad && vm.complianceData.reference.totalEnergyLoad != -1 ? 'red' : ''}"
                                               />
                                    </md-input-container>
                                </td>

                                <!-- Reference Calculated Maximum Energy -->
                                <td ng-if="vm.complianceData.heatingAndCoolingRulesetCode !== 'Disabled' || vm.complianceData.complianceMethod.complianceMethodCode === 'CMPerfELL'">
                                    <md-input-container ng-if="vm.complianceData.reference.totalEnergyLoad > -1"
                                                        class="md-block vertically-condensed"
                                                        style="display: flex;">
                                        <input ng-disabled="true"
                                               class="compliance-row-table-input-m"
                                               ng-value="vm.complianceData.reference.totalEnergyLoad | number: '1.0'" />
                                    </md-input-container>
                                    <md-input-container ng-if="vm.complianceData.reference.totalEnergyLoad <= -1"
                                                        class="md-block vertically-condensed"
                                                        style="display: flex;">
                                        <input disabled
                                               class="compliance-row-table-input-m"
                                               ng-value="'N/A'"/>
                                    </md-input-container>
                                </td>
                            </tr>

                            <!-- 'Equivalent' Reference rows to other compliance options -->
                            <tr ng-if="vm.complianceData.complianceMethod.complianceMethodCode === 'CMPerfELL' &&
                                       vm.complianceData.assessmentSoftware.simulationEngine === 'CHENATH'">
                                <td>Equivalent HER</td>

                                <!-- Equivalent Proposed Calculated Energy -->
                                <td>
                                    <md-input-container ng-if="vm.complianceData.proposed.overrideEnergyLoads === true"
                                                        class="md-block vertically-condensed"
                                                        style="display: flex;">

                                        <input class="compliance-row-table-input-m"
                                               ng-disabled="true"
                                               ng-value="vm.complianceData.proposed.houseEnergyRatingOverride | number: '1.0'"/>

                                    </md-input-container>

                                    <md-input-container ng-if="vm.complianceData.proposed.overrideEnergyLoads === false  || vm.complianceData.reference.overrideEnergyLoads == null"
                                                        class="md-block vertically-condensed"
                                                        style="display: flex;">
                                        <input class="compliance-row-table-input-m"
                                               ng-disabled="true"
                                               ng-value="vm.complianceData.proposed.houseEnergyRating | number: '1.0'"/>

                                    </md-input-container>
                                </td>

                                <!-- Equivalent Reference Calculated Maximum Energy -->
                                <td>

                                    <div ng-if="vm.complianceData.reference.overrideEnergyLoads === true">
                                        <md-input-container class="md-block vertically-condensed"
                                                            style="display: flex;">
                                            <input class="compliance-row-table-input-m"
                                                   ng-disabled="true"
                                                   ng-value="vm.complianceData.reference.houseEnergyRatingOverride | number: '1.0'"/>
                                        </md-input-container>
                                    </div>

                                    <div ng-if="vm.complianceData.reference.overrideEnergyLoads === false || vm.complianceData.reference.overrideEnergyLoads == null">
                                        <md-input-container ng-if="vm.complianceData.reference.houseEnergyRating > -1 || vm.complianceData.reference.houseEnergyRating == null"
                                                            class="md-block vertically-condensed"
                                                            style="display: flex;">
                                            <input class="compliance-row-table-input-m"
                                                   ng-disabled="true"
                                                   ng-value="vm.complianceData.reference.houseEnergyRating | number: '1.0'"/>

                                        </md-input-container>

                                        <md-input-container ng-if="vm.complianceData.reference.houseEnergyRating == -1"
                                                            class="md-block vertically-condensed"
                                                            style="display: flex;">
                                            <input disabled
                                                   class="compliance-row-table-input-m"
                                                   ng-value="'N/A'"/>
                                        </md-input-container>
                                    </div>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                </td>

                <!-- House Energy Rating-->
                <td ng-if="vm.complianceData.complianceMethod.complianceMethodCode !== 'CMPerfELL'">
                    <table>
                        <tbody>

                            <!-- Required -->
                            <tr>
                                <td>Required</td>
                                <td>
                                    <md-select ng-if="vm.permission_field_herrequired_view"
                                               ng-disabled="vm.isLocked || vm.permission_field_herrequired_edit == false"
                                               name="RequiredHouseEnergyRating"
                                               class="vertically-condensed compliance-row-table-input-m"
                                               flex="100"
                                               ng-model="vm.tempRequiredHouseEnergyRating"
                                               ng-change="vm.handleReqHERFormat()"
                                               placeholder="'Select an Energy Rating'">
                                        <md-option ng-repeat="value in vm.availableHouseEnergyRatings"
                                                   ng-value="value">
                                            <span style="text-align: center; padding-left: 25px;">{{value}}</span>
                                        </md-option>
                                    </md-select>
                                </td>
                            </tr>

                            <!-- Calculated -->
                            <tr>
                                <td>Calculated</td>
                                <td>
                                    <span type="text"
                                          name="ProposedHouseEnergyRatingOther"
                                          style="text-align:center; display: block; margin: 3px 3px;"
                                          ng-style="{'color': vm.complianceData.proposed.overrideEnergyLoads == false
                                                ? vm.complianceData.proposed.houseEnergyRating < vm.complianceData.requiredHouseEnergyRating
                                                    ? 'red'
                                                    : ''
                                                : vm.complianceData.proposed.houseEnergyRatingOverride < vm.complianceData.requiredHouseEnergyRating
                                                    ? 'red'
                                                    : '' }">
                                        {{ vm.complianceData.proposed.overrideEnergyLoads === true
                                            ? vm.complianceData.proposed.houseEnergyRatingOverride.toFixed(1)
                                            : vm.complianceData.proposed.houseEnergyRating.toFixed(1) }}
                                    </span>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                </td>

                <!-- Is Compliance Valid (Override) -->
                <td ng-if="vm.permission_field_valid_view">
                    <md-switch ng-model="vm.complianceData.isComplianceValid"
                               ng-change="vm.calculateCompliance()"
                               ng-disabled="vm.isLocked || !vm.permission_field_valid_edit"
                               style="margin-left: 8px;"></md-switch>
                </td>

                <!-- Show to Client -->
                <td ng-if="!vm.complianceData.isBaselineSimulation && vm.permission_field_showtoclient_view">
                    <md-switch ng-model="vm.complianceData.isShownToClient"
                               ng-disabled="vm.isLocked || !vm.permission_field_showtoclient_edit">
                        <style>
                            md-switch .md-container { margin: auto; }
                        </style>
                    </md-switch>
                </td>

            </tr>
        </tbody>
    </table>

    <!-- File Uploads -->
    <software-file-uploads option="vm.complianceData"
                           disabled="vm.isLocked"
                           assessment="vm.assessment"
                           job-files="vm.jobFiles">
    </software-file-uploads>

</div>
