SELECT TOP (1000) [ID]
      ,[AUTHOR]
      ,[FILENAME]
      ,[DATEEXECUTED]
      ,[ORDEREXECUTED]
      ,[EXECTYPE]
      ,[MD5SUM]
      ,[DESCRIPTION]
      ,[COMMENTS]
      ,[TAG]
      ,[LIQUIBASE]
      ,[CONTEXTS]
      ,[<PERSON>BE<PERSON>]
  FROM [dbo].[DATABASECHANGELOG]
  WHERE 1=1
    -- AND [ID] = '050625'
  ORDER BY [DATEEXECUTED] DESC

-- Forget record
-- UPDATE [dbo].[DATABASECHANGELOG] SET [MD5SUM] = NULL WHERE [ID] = '050625';