(function () {
    // The StandardModelUpdateCtrl supports a list page.
    'use strict';
    var controllerId = 'StandardModelUpdateCtrl';
    angular.module('app')
        .controller(controllerId, ['$rootScope', 'common', '$scope', '$mdDialog', '$stateParams', '$timeout',
            '$state', '$http', 'bootstrap.dialog', 'standardmodelservice', 'uuid4', 'projectservice', 'security', 'servicetemplateservice', 'wadesigncodeservice', 'wholeofhomeservice',
            standardModelUpdateController]);
    function standardModelUpdateController($rootScope, common, $scope, $mdDialog, $stateParams, $timeout,
        $state, $http, modalDialog, standardmodelservice, uuid4, projectservice, securityservice, servicetemplateservice, wadesigncodeservice, wholeofhomeservice) {

        // - --------- - //
        // - VARIABLES - //
        // - --------- - //

        var vm = this;

        // General
        vm.isBusy = true;
        vm.spinnerOptions = {};
        vm.isModal = $scope.modalInstance != null;
        vm.newRecord = vm.isModal && $scope.newRecord != undefined && $scope.newRecord == true;
        vm.title = vm.newRecord ? "New Home Design" : 'Edit Home Design';
        vm.viewMode = $scope.viewMode;
        vm.hideActionBar = false;

        // Permissions
        vm.editPermission = securityservice.immediateCheckRoles('settings__settings__edit');

        // Main
        vm.projectId = $scope.projectId;
        vm.project = null;
        vm.standardModel = {
            standardHomeModelId: uuid4.generate(),
            title: "New Home Design",
            description: "",
            isActive: true,
            projectId: vm.projectId,
            standardHomeModelFiles: [],
            variableOptions: {},
            variationOptionsSettings: {
                floorplanIsActive: true,
                designOptionIsActive: true,
                facadeIsActive: true,
                specificationIsActive: true,
                configurationIsActive: true
            },
            variationOptionsList: [],
            variationList: [],
            view3dFloorPlans: false,
            costEstimateEnabled: false,
            variableMetadata: {
                designInsightsEnabled: false,
                wholeOfHomeDefaultData: {}
            }
        };

        // TESTING: Dummy Standard Model
        //vm.standardModel = {
        //    ...vm.standardModel,
        //    ...TestDummyData.NewModelVariation,
        //};

        vm.resetChangeDetection = function () {
            vm.standardModelOriginal = angular.copy(vm.standardModel);
        }

        vm.hasChanges = function () {
            return angular.toJson(vm.standardModel) != angular.toJson(vm.standardModelOriginal);
        }

        // Variation
        vm.variationCategories = StandardModelConstants.variationCategories;

        // Services Defaults
        servicetemplateservice.getServiceTypes().then(data => {
            vm.serviceTypes = data;
            vm.serviceTypesGrouped = servicetemplateservice.serviceTypesGrouped(
                ['SpaceHeatingSystem', 'SpaceCoolingSystem', 'HotWaterSystem'],
                'title',
                vm.serviceTypes
            );
        });

        // Bulk
        vm.bulkStatus = {};
        vm.variationCategories.forEach(category => {
            vm.bulkStatus[category] = new MasterBulkStatus();
            vm.bulkStatus[category].checkboxId = "sm-allCheckbox";
            vm.bulkStatus[category].isIndeterminate = false;
            vm.bulkStatus[category].selectAllCheckboxState = false;
        });

        // Utils
        vm.wohConstants = WholeOfHomeConstants;
        vm.toSplitTitleCase = function (inString) {
            if (inString.toLowerCase() == "floorplan") {
                return "Floor Plan";
            }
            return common.toSplitTitleCase(inString);
        }
        vm.roundUpInt = common.roundUpInt;
        vm.featureName = standardmodelservice.featureName;
        vm.keyToName = standardmodelservice.keyToName;
        vm.firstCharLowerCase = common.firstCharLowerCase;

        // - ---------- - //
        // - INITIALISE - //
        // - ---------- - //

        function initialise() {
            // IF modal
            if (vm.isModal) {
                if (vm.newRecord === false) {
                    vm.standardModel.standardHomeModelId = $scope.standardHomeModelId;
                }
                vm.hideActionBar = true;
            // ELSE
            } else {
                vm.standardModel.standardHomeModelId = $stateParams.standardHomeModelId;
            }

            // IF new record
            if (vm.newRecord) {
                // Check project access and redirect if no permission (following EnergyLabs pattern)
                projectservice.getProject(vm.projectId).then(data => {
                    if (data == null || data == "" || (data.clientId && !data.projectName)) {
                        // Project not found or no permission - redirect to client update page
                        $state.go('client-updateform', { clientId: vm.standardModel.clientId || 'new', tabIndex: 7 });
                        return;
                    }
                    vm.project = data;
                    vm.standardModel.project = data;
                    vm.standardModel.clientId = vm.project.clientId;
                    // Set Variation Category settings based on Project's settings
                    vm.standardModel.variationOptionsSettings = {
                        floorplanIsActive: vm.project.energyLabsSettings.varCategoryFloorplanActive,
                        designOptionIsActive: vm.project.energyLabsSettings.varCategoryDesignOptionActive,
                        facadeIsActive: vm.project.energyLabsSettings.varCategoryFacadeActive,
                        specificationIsActive: vm.project.energyLabsSettings.varCategorySpecificationActive,
                        configurationIsActive: vm.project.energyLabsSettings.varCategoryConfigurationActive
                    };
                    // Set 3D Model and Cost Estimate values based on Project settings
                    vm.standardModel.view3dFloorPlans = vm.project.energyLabsSettings.view3dFloorPlans;
                    vm.standardModel.costEstimateEnabled = vm.project.energyLabsSettings.costEstimateEnabledDefault;

                    // Inherit variation options from project if available
                    if (vm.project.variationOptions && vm.project.variationOptions.length > 0) {
                        // Create copies of the project variation options with new IDs
                        vm.standardModel.variationOptionsList = vm.project.variationOptions
                            .filter(o => !o.deleted)
                            .map(o => ({
                                standardHomeModelVariationOptionId: uuid4.generate(),
                                standardHomeModelId: vm.standardModel.standardHomeModelId,
                                variationCategoryCode: o.variationCategoryCode,
                                optionName: o.optionName,
                                sortOrder: o.sortOrder
                            }));
                    }
                    vm.isBusy = false;
                }).catch(error => {
                    // Error accessing project - redirect to client update page
                    $state.go('client-updateform', { clientId: vm.standardModel.clientId || 'new', tabIndex: 7 });
                });
            // ELSE
            } else {
                standardmodelservice.getStandardModel(vm.standardModel.standardHomeModelId).then(data => {
                    if (data != null) {
                        vm.standardModel = {
                            ...vm.standardModel,
                            ...data
                        };
                        vm.standardModel.variationList = vm.standardModel.variationList ?? [];
                        vm.projectId = vm.standardModel.projectId;
                        vm.drawingsCount = vm.standardModel.standardHomeModelFiles.filter(x => !x.deleted).length;
                        vm.setImageSortOrder(data.standardHomeModelFiles);
                        // Check project access and redirect if no permission (following EnergyLabs pattern)
                        projectservice.getProject(vm.standardModel.projectId).then(data => {
                            if (data == null || data == "" || (data.clientId && !data.projectName)) {
                                // Project not found or no permission - redirect to client update page
                                $state.go('client-updateform', { clientId: vm.standardModel.clientId || 'new', tabIndex: 7 });
                                return;
                            }
                            vm.project = data;
                            vm.standardModel.clientId = vm.project.clientId;
                            vm.updateServDefsVisibility();
                            vm.resetChangeDetection();
                            vm.isBusy = false;
                        }).catch(error => {
                            // Error accessing project - redirect to client update page
                            $state.go('client-updateform', { clientId: vm.standardModel.clientId || 'new', tabIndex: 7 });
                        });
                    }
                });
            }
        }

        initialise();

        // - ------- - //
        // - HANDLES - //
        // - ------- - //

        // Get Variation Options for Category
        vm.getVariationOptions = function (category) {
            return vm.standardModel.variationOptionsList.filter(o => o.variationCategoryCode == category);
        }

        // URL is valid
        async function urlIsValid(url) {
            try {
                return fetch(url)
                      .then(response => response.ok)
                      .catch(error => false);
            } catch (error) {
                return false;
            }
        }

        // Floorplanner
        vm.floorplannerLinkBlur = async function () {
            if (!vm.standardModel.floorplannerLink.startsWith("https://floorplanner.com")) {
                vm.floorplannerLinkWrongSite = true;
                vm.floorplannerLinkInvalid = false;
            } else if (!(await urlIsValid(vm.standardModel.floorplannerLink))) {
                vm.floorplannerLinkWrongSite = false;
                vm.floorplannerLinkInvalid = true;
                $scope.$apply();
            } else {
                vm.floorplannerLinkEditing = false;
                vm.floorplannerLinkWrongSite = false;
                vm.floorplannerLinkInvalid = false;
                $scope.$apply();
            }
        }

        // Project Search
        vm.getProjects = function(searchTerm) {
            var filter = [{ field: "projectName", operator: "startswith", value: searchTerm }];
            var sort = [{ field: "projectName", dir: "asc" }];
            return projectservice.getList(null, null, null, null, null, sort, filter).then(data => {
                var list = data.data;
                return list;
            });
        }

        // After Project Selected
        vm.projectChanged = function() {
            if (vm.standardModel.project != null) {
                vm.standardModel.projectId = vm.standardModel.project.projectId;
            }
        }

        // Add Plan Image
        vm.addPlanImage = function() {
            if (vm.standardModel.standardHomeModelFiles == null) {
                vm.standardModel.standardhomeModelFiles = [];
            }
            vm.standardModel.standardHomeModelFiles.push({
                standardHomeModelFileId: uuid4.generate(),
                standardHomeModelId: vm.standardModel.standardHomeModelId,
                deleted: false,
                createdOn: new Date().toUTCString(),
                sortOrder: vm.standardModel.standardHomeModelFiles.filter(x => !x.deleted).length
            });
            vm.drawingsCount = vm.standardModel.standardHomeModelFiles.filter(x => !x.deleted).length;
        }

        // Delete Plan Image
        vm.deletePlanImage = function(homeModelFile) {
            homeModelFile.deleted = true;
            vm.setImageSortOrder(vm.standardModel.standardHomeModelFiles);
            vm.drawingsCount = vm.standardModel.standardHomeModelFiles.filter(x => !x.deleted).length;
        }

        /**
         * Sets sort order of standard home model files when no 'sort order' is already supplied. This is only
         * required to fix up old data which was created WITHOUT the sort order in the first place.
         * */
        vm.setImageSortOrder = function (standardHomeModelFiles) {
            let order = 0;
            standardHomeModelFiles.forEach(x => {
                if (x.deleted)
                    return;
                x.sortOrder = order;
                order++;
            });
        }

        // Initialize toggle settings flags
        vm.toggleSettings = {
            isActive: { applyToAllLevels: false },
            view3dFloorPlans: { applyToAllLevels: false },
            costEstimateEnabled: { applyToAllLevels: false },
            designInsightsEnabled: { applyToAllLevels: false }
        };

        // Toggle settings on all variations
        vm.toggleVariationsIsActive = function () {
            if (!vm.standardModel.isActive) {
                // When toggling OFF, set flag to apply to all levels
                // Backend will handle turning off child values
                vm.standardModel.toggleChildrenIsActive = true;
                vm.toggleSettings.isActive.applyToAllLevels = true; // For backward compatibility

                // Immediately update variations in the UI
                if (vm.standardModel.variationList && vm.standardModel.variationList.length > 0) {
                    vm.standardModel.variationList.forEach(variation => {
                        variation.isActive = false;
                    });
                }
            } else {
                // When toggling ON, show modal with options
                let modalScope = $rootScope.$new();
                modalScope.settingName = "Active Setting";
                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/energy-labs/modals/toggle-setting-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: false,
                }).then(function (response) {
                    // Store the user's choice to apply to all levels
                    vm.standardModel.toggleChildrenIsActive = response.applyToAllLevels;
                    vm.toggleSettings.isActive.applyToAllLevels = response.applyToAllLevels; // For backward compatibility

                    // If user chose to apply to all levels, immediately update variations in the UI
                    if (response.applyToAllLevels && vm.standardModel.variationList && vm.standardModel.variationList.length > 0) {
                        vm.standardModel.variationList.forEach(variation => {
                            variation.isActive = true;
                        });
                    }
                    // Backend will handle applying to child levels if needed
                }, function() {
                    // If modal is canceled, revert the toggle
                    vm.standardModel.isActive = false;
                    vm.standardModel.toggleChildrenIsActive = false;
                    vm.toggleSettings.isActive.applyToAllLevels = false;
                });
            }
        }
        vm.toggleVariations3dModel = function () {
            if (!vm.standardModel.view3dFloorPlans) {
                // When toggling OFF, set flag to apply to all levels
                // Backend will handle turning off child values
                vm.standardModel.toggleChildrenView3dFloorPlans = true;
                vm.toggleSettings.view3dFloorPlans.applyToAllLevels = true; // For backward compatibility

                // Immediately update variations in the UI
                if (vm.standardModel.variationList && vm.standardModel.variationList.length > 0) {
                    vm.standardModel.variationList.forEach(variation => {
                        variation.view3dFloorPlans = false;
                    });
                }
            } else {
                // When toggling ON, show modal with options
                let modalScope = $rootScope.$new();
                modalScope.settingName = "3D Model Setting";
                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/energy-labs/modals/toggle-setting-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: false,
                }).then(function (response) {
                    // Store the user's choice to apply to all levels
                    vm.standardModel.toggleChildrenView3dFloorPlans = response.applyToAllLevels;
                    vm.toggleSettings.view3dFloorPlans.applyToAllLevels = response.applyToAllLevels; // For backward compatibility

                    // If user chose to apply to all levels, immediately update variations in the UI
                    if (response.applyToAllLevels && vm.standardModel.variationList && vm.standardModel.variationList.length > 0) {
                        vm.standardModel.variationList.forEach(variation => {
                            variation.view3dFloorPlans = true;
                        });
                    }
                    // Backend will handle applying to child levels if needed
                }, function() {
                    // If modal is canceled, revert the toggle
                    vm.standardModel.view3dFloorPlans = false;
                    vm.standardModel.toggleChildrenView3dFloorPlans = false;
                    vm.toggleSettings.view3dFloorPlans.applyToAllLevels = false;
                });
            }
        }
        vm.toggleVariationsCostEstimate = function () {
            if (!vm.standardModel.costEstimateEnabled) {
                // When toggling OFF, set flag to apply to all levels
                // Backend will handle turning off child values
                vm.standardModel.toggleChildrenCostEstimateEnabled = true;
                vm.toggleSettings.costEstimateEnabled.applyToAllLevels = true; // For backward compatibility

                // Immediately update variations in the UI
                if (vm.standardModel.variationList && vm.standardModel.variationList.length > 0) {
                    vm.standardModel.variationList.forEach(variation => {
                        variation.costEstimateEnabled = false;
                    });
                }
            } else {
                // When toggling ON, show modal with options
                let modalScope = $rootScope.$new();
                modalScope.settingName = "Cost Estimate Setting";
                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/energy-labs/modals/toggle-setting-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: false,
                }).then(function (response) {
                    // Store the user's choice to apply to all levels
                    vm.standardModel.toggleChildrenCostEstimateEnabled = response.applyToAllLevels;
                    vm.toggleSettings.costEstimateEnabled.applyToAllLevels = response.applyToAllLevels; // For backward compatibility

                    // If user chose to apply to all levels, immediately update variations in the UI
                    if (response.applyToAllLevels && vm.standardModel.variationList && vm.standardModel.variationList.length > 0) {
                        vm.standardModel.variationList.forEach(variation => {
                            variation.costEstimateEnabled = true;
                        });
                    }
                    // Backend will handle applying to child levels if needed
                }, function() {
                    // If modal is canceled, revert the toggle
                    vm.standardModel.costEstimateEnabled = false;
                    vm.standardModel.toggleChildrenCostEstimateEnabled = false;
                    vm.toggleSettings.costEstimateEnabled.applyToAllLevels = false;
                });
            }
        }
        vm.toggleVariationsDesignInsights = function () {
            if (!vm.standardModel.variableMetadata.designInsightsEnabled) {
                // When toggling OFF, set flag to apply to all levels
                // Backend will handle turning off child values
                vm.standardModel.toggleChildrenDesignInsightsEnabled = true;
                vm.toggleSettings.designInsightsEnabled.applyToAllLevels = true; // For backward compatibility

                // Immediately update variations in the UI
                if (vm.standardModel.variationList && vm.standardModel.variationList.length > 0) {
                    vm.standardModel.variationList.forEach(variation => {
                        if (variation.variableMetadata) {
                            variation.variableMetadata.designInsightsEnabled = false;
                        }
                    });
                }
            } else {
                // When toggling ON, show modal with options
                let modalScope = $rootScope.$new();
                modalScope.settingName = "Design Insights Setting";
                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/energy-labs/modals/toggle-setting-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: false,
                }).then(function (response) {
                    // Store the user's choice to apply to all levels
                    vm.standardModel.toggleChildrenDesignInsightsEnabled = response.applyToAllLevels;
                    vm.toggleSettings.designInsightsEnabled.applyToAllLevels = response.applyToAllLevels; // For backward compatibility

                    // If user chose to apply to all levels, immediately update variations in the UI
                    if (response.applyToAllLevels && vm.standardModel.variationList && vm.standardModel.variationList.length > 0) {
                        vm.standardModel.variationList.forEach(variation => {
                            if (variation.variableMetadata) {
                                variation.variableMetadata.designInsightsEnabled = true;
                            }
                        });
                    }
                    // Backend will handle applying to child levels if needed
                }, function() {
                    // If modal is canceled, revert the toggle
                    vm.standardModel.variableMetadata.designInsightsEnabled = false;
                    vm.standardModel.toggleChildrenDesignInsightsEnabled = false;
                    vm.toggleSettings.designInsightsEnabled.applyToAllLevels = false;
                });
            }
        }

        // Variation Options
        vm.addVariationOption = function (category) {
            let newOption = {
                standardHomeModelVariationOptionId: uuid4.generate(),
                standardHomeModelId: vm.standardModel.standardHomeModelId,
                variationCategoryCode: category,
                optionName: "",
                sortOrder: Math.max(...vm.getVariationOptions(category).map(o => o.sortOrder), 0)+1
            };
            vm.standardModel.variationOptionsList.push(newOption);
            setTimeout(() => {
                console.log(`option-input-${newOption.standardHomeModelVariationOptionId}`);
                console.log(document.getElementById(`option-input-${newOption.standardHomeModelVariationOptionId}`));
                document.getElementById(`option-input-${newOption.standardHomeModelVariationOptionId}`)?.focus();
            }, 10);
        }
        vm.copyVariationOption = function (category, option) {
            let newOptionName = `${option.optionName} (copy)`;
            while (vm.standardModel.variationOptionsList.filter(o => o.variationCategoryCode == option.variationCategoryCode).find(o => o.optionName == newOptionName) != null) {
                newOptionName += ' (copy)';
            }
            vm.standardModel.variationOptionsList.push({
                standardHomeModelVariationOptionId: uuid4.generate(),
                standardHomeModelId: option.standardHomeModelId,
                variationCategoryCode: category,
                optionName: newOptionName,
                sortOrder: Math.max(...vm.getVariationOptions(category).map(o => o.sortOrder), 0)+1
            });
        }
        vm.moveVarOptionUp = function (category, option) {
            // Find option before
            let optionBefore = vm.getVariationOptions(option.variationCategoryCode).sort((a,b) => a.sortOrder < b.sortOrder ? 1 : -1).find(o => o.sortOrder < option.sortOrder);
            // Swap sort orders
            let tempSortOrder = optionBefore.sortOrder;
            optionBefore.sortOrder = option.sortOrder;
            option.sortOrder = tempSortOrder;
            // Sort new list
            vm.standardModel.variationOptionsList.sort((a, b) => (a.sortOrder > b.sortOrder) ? 1 : -1);
        }
        vm.moveVarOptionDown = function (category, option) {
            // Find option after
            let optionAfter = vm.getVariationOptions(option.variationCategoryCode).sort((a,b) => a.sortOrder > b.sortOrder ? 1 : -1).find(o => o.sortOrder > option.sortOrder);
            // Swap sort orders
            let tempSortOrder = optionAfter.sortOrder;
            optionAfter.sortOrder = option.sortOrder;
            option.sortOrder = tempSortOrder;
            // Sort new list
            vm.standardModel.variationOptionsList.sort((a, b) => (a.sortOrder > b.sortOrder) ? 1 : -1);
        }
        vm.deleteVariationOption = function (option) {
            modalDialog.confirmationDialog(
                "Delete Variation Option",
                `Are you sure you want to delete the option "${option.optionName}"?`,
                "Delete",
                "Cancel",
                null
            ).then(() => {
                vm.standardModel.variationOptionsList.splice(
                    vm.standardModel.variationOptionsList.findIndex(o => o.standardHomeModelVariationOptionId == option.standardHomeModelVariationOptionId),
                    1
                );
            });
        }
        vm.deleteAllVariationOptions = function () {
            modalDialog.confirmationDialog(
                "Delete All Variation Options",
                `Are you sure you want to delete all options?`,
                "Confirm",
                "Cancel",
                null
            ).then(() => {
                vm.standardModel.variationOptionsList = [];
            });
        }
        vm.copyVariationOptionsToModel = function (copyFrom = false) {
            // If copying from project, use a different flow
            if (copyFrom === 'project') {
                // Confirm with the user
                let modalScope = $rootScope.$new();
                modalScope.confirmationHeader = "Copy From Project";
                modalScope.confirmationText = "Are you sure you want to copy variation options from the project? This will replace all existing variation options.";

                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/data/generic-confirmation-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: false,
                }).then(function (confirmed) {
                    if (confirmed) {
                        // Get the project data
                        projectservice.getProject(vm.standardModel.projectId).then(data => {
                            if (data && data.variationOptions && data.variationOptions.length > 0) {
                                // Create copies of the project variation options with new IDs
                                const newOptions = data.variationOptions
                                    .filter(o => !o.deleted)
                                    .map(o => ({
                                        standardHomeModelVariationOptionId: uuid4.generate(),
                                        standardHomeModelId: vm.standardModel.standardHomeModelId,
                                        variationCategoryCode: o.variationCategoryCode,
                                        optionName: o.optionName,
                                        sortOrder: o.sortOrder
                                    }));

                                vm.standardModel.variationOptionsList = newOptions;
                            } else {
                                common.logger.warning("No variation options found in the project.");
                            }
                        });
                    }
                });
                return;
            }

            // Regular flow for copying from/to another home design
            let modalScope = $rootScope.$new();
            modalScope.modalTitle = `Copy Variation Options ${copyFrom ? 'From' : 'To'} Home Design`;
            modalScope.thisHomeModelId = vm.standardModel.standardHomeModelId;
            modalScope.projectId = vm.standardModel.projectId;

            // Show the selector modal with skipHide: true to keep the parent modal open
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/home-model-selector-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
                skipHide: true // Always keep the parent modal open
            }).then(async function (selectedModelId) {
                if (copyFrom) {
                    // If it's a new record, we can't save to the backend yet, so just update the frontend model
                    if (vm.newRecord) {
                        // Get the variation options directly from the selected model
                        standardmodelservice.getStandardModel(selectedModelId).then(selectedModel => {
                            if (selectedModel && selectedModel.variationOptionsList) {
                                // Create new copies of the variation options with new IDs
                                const newList = selectedModel.variationOptionsList.map(option => ({
                                    ...option,
                                    standardHomeModelVariationOptionId: uuid4.generate(),
                                    standardHomeModelId: vm.standardModel.standardHomeModelId
                                }));
                                vm.standardModel.variationOptionsList = newList;
                            }
                        });
                    } else {
                        // Existing record, use the backend service
                        standardmodelservice.copyVariationOptionsToModel(selectedModelId, vm.standardModel.standardHomeModelId).then(newList => {
                            vm.standardModel.variationOptionsList = newList;
                        });
                    }
                }
                else {
                    await standardmodelservice.copyVariationOptionsToModel(vm.standardModel.standardHomeModelId, selectedModelId);
                }
            });
        }

        // Services Defaults
        vm.restoreServiceDefaults = function () {
            const defaults = vm.project.energyLabsSettings.variableOptionDefaults;
            vm.standardModel.variableMetadata.wholeOfHomeDefaultData = angular.copy(defaults.wholeOfHomeDefaultData);
        }
        vm.clearAllServiceDefaults = function () {
            vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceHeating = null;
            vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceCooling = null;
            vm.standardModel.variableMetadata.wholeOfHomeDefaultData.waterHeating = null;
            vm.standardModel.variableMetadata.wholeOfHomeDefaultData.swimmingPool = null;
            vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spa = null;
            vm.standardModel.variableMetadata.wholeOfHomeDefaultData.photovoltaic = null;
        }
        vm.updateServDefSelectAllState = function () { $timeout(() => {
            if (vm.spaceHeatingTypeSelected && (vm.spaceHeatingRatingVisible && vm.spaceHeatingRatingSelected) && vm.spaceCoolingTypeSelected && (vm.spaceCoolingRatingVisible && vm.spaceCoolingRatingSelected)
                && vm.waterHeaterTypeSelected && vm.swimmingPoolExistsSelected && (vm.swimmingPoolVolumeVisible && vm.swimmingPoolVolumeSelected)
                && (vm.swimmingPoolRatingVisible && vm.swimmingPoolRatingSelected) && vm.spaExistsSelected && (vm.spaVolumeVisible && vm.spaVolumeSelected) && (vm.spaRatingVisible && vm.spaRatingSelected)
                && vm.photovoltaicExistsSelected && (vm.photovoltaicCapacityVisible && vm.photovoltaicCapacitySelected)) {

                vm.allServDefsSelected = true;
                vm.anyServDefsSelected = true;

            } else if (vm.spaceHeatingTypeSelected || (vm.spaceHeatingRatingVisible && vm.spaceHeatingRatingSelected) || vm.spaceCoolingTypeSelected || (vm.spaceCoolingRatingVisible && vm.spaceCoolingRatingSelected)
                || vm.waterHeaterTypeSelected || vm.swimmingPoolExistsSelected || (vm.swimmingPoolVolumeVisible && vm.swimmingPoolVolumeSelected)
                || (vm.swimmingPoolRatingVisible && vm.swimmingPoolRatingSelected) || vm.spaExistsSelected || (vm.spaVolumeVisible && vm.spaVolumeSelected) || (vm.spaRatingVisible && vm.spaRatingSelected)
                || vm.photovoltaicExistsSelected || (vm.photovoltaicCapacityVisible && vm.photovoltaicCapacitySelected)) {

                vm.allServDefsSelected = false;
                vm.anyServDefsSelected = true;

            } else {
                vm.allServDefsSelected = false;
                vm.anyServDefsSelected = false;
            }
        });}
        vm.updateServDefsVisibility = function () { $timeout(() => {
            vm.spaceHeatingRatingVisible = vm.showEnergyRatingForCode(vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceHeating?.serviceTypeCode, vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceHeating);
            vm.spaceCoolingRatingVisible = vm.showEnergyRatingForCode(vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceCooling?.serviceTypeCode, vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spaceCooling);
            vm.swimmingPoolVolumeVisible = vm.standardModel.variableMetadata.wholeOfHomeDefaultData.swimmingPool?.exists === true;
            vm.swimmingPoolRatingVisible = vm.standardModel.variableMetadata.wholeOfHomeDefaultData.swimmingPool?.exists === true;
            vm.spaVolumeVisible = vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spa?.exists === true;
            vm.spaRatingVisible = vm.standardModel.variableMetadata.wholeOfHomeDefaultData.spa?.exists === true;
            vm.photovoltaicCapacityVisible = vm.standardModel.variableMetadata.wholeOfHomeDefaultData.photovoltaic?.exists === true;
            vm.updateServDefSelectAllState();
        });}
        vm.selectAllServiceDefaultCheckboxes = function () {
            let toggle = !vm.allServDefsSelected;
            vm.anyServDefsSelected = toggle;
            vm.spaceHeatingTypeSelected = toggle;
            vm.spaceHeatingRatingSelected = toggle;
            vm.spaceCoolingTypeSelected = toggle;
            vm.spaceCoolingRatingSelected = toggle;
            vm.waterHeaterTypeSelected = toggle;
            vm.swimmingPoolExistsSelected = toggle;
            vm.swimmingPoolVolumeSelected = toggle;
            vm.swimmingPoolRatingSelected = toggle;
            vm.spaExistsSelected = toggle;
            vm.spaVolumeSelected = toggle;
            vm.spaRatingSelected = toggle;
            vm.photovoltaicExistsSelected = toggle;
            vm.photovoltaicCapacitySelected = toggle;
        }
        vm.launchServDefsUpdateAllConfrimation = function () {
            let modalScope = $rootScope.$new();
            modalScope.confirmationHeader = "Confirm Update All";
            modalScope.confirmationText = "Updating the Services Defaults Settings will overwrite the existing data for all Variations. Are you sure you want to proceed?";
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/data/generic-confirmation-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(async function (confirmed) {
                let fieldsToCopy = [];
                if (confirmed) {
                    if (vm.spaceHeatingTypeSelected)
                        fieldsToCopy.push("SpaceHeating.ServiceTypeCode");
                    if (vm.spaceHeatingRatingVisible && vm.spaceHeatingRatingSelected)
                        fieldsToCopy.push("SpaceHeating.Gems2019Rating");
                    if (vm.spaceCoolingTypeSelected)
                        fieldsToCopy.push("SpaceCooling.ServiceTypeCode");
                    if (vm.spaceCoolingRatingVisible && vm.spaceCoolingRatingSelected)
                        fieldsToCopy.push("SpaceCooling.Gems2019Rating");
                    if (vm.waterHeaterTypeSelected)
                        fieldsToCopy.push("WaterHeating.ServiceTypeCode");
                    if (vm.swimmingPoolExistsSelected)
                        fieldsToCopy.push("SwimmingPool.Exists");
                    if (vm.swimmingPoolVolumeVisible && vm.swimmingPoolVolumeSelected)
                        fieldsToCopy.push("SwimmingPool.Volume");
                    if (vm.swimmingPoolRatingVisible && vm.swimmingPoolRatingSelected)
                        fieldsToCopy.push("SwimmingPool.Gems2019Rating");
                    if (vm.spaExistsSelected)
                        fieldsToCopy.push("Spa.Exists");
                    if (vm.spaVolumeVisible && vm.spaVolumeSelected)
                        fieldsToCopy.push("Spa.Volume");
                    if (vm.spaRatingVisible && vm.spaRatingSelected)
                        fieldsToCopy.push("Spa.Gems2019Rating");
                    if (vm.photovoltaicExistsSelected)
                        fieldsToCopy.push("Photovoltaic.Exists");
                    if (vm.photovoltaicCapacityVisible && vm.photovoltaicCapacitySelected)
                        fieldsToCopy.push("Photovoltaic.Capacity");
                    standardmodelservice.updateServicesDefaultsInAllModels(vm.projectId, vm.standardModel.standardHomeModelId, fieldsToCopy, vm.standardModel.variableMetadata.wholeOfHomeDefaultData);
                    // Deselect all checkboxes
                    vm.allServDefsSelected = true;
                    vm.selectAllServiceDefaultCheckboxes();
                    vm.allServDefsSelected = false;
                }
            });
        }
        vm.showEnergyRatingForCode = wholeofhomeservice.showEnergyRatingForCode;

        // Click on Variation row
        vm.variationRowClick = function (variationId) {
            vm.navigateAttempt('standard-model-variation-updateform', { standardHomeModelId: variationId });
        }

        // Attempt to navigate
        vm.navigateAttempt = async function (page, params) {
            if (vm.hasChanges()) {
                let modalScope = $rootScope.$new();
                modalScope.title = vm.standardModel.title;
                modalScope.bodyText = `You have unsaved changes for "${vm.standardModel.title}". Would you like to save before navigating?`;
                modalScope.buttons = [{
                    isPrimary: true,
                    text: "Save",
                    onClick: () => vm.save(() => $state.go(page, params))
                },{
                    text: "Don't Save",
                    onClick: () => $state.go(page, params)
                },{
                    isCancel: true,
                    text: "Cancel"
                }];
                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/data/generic-modal.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: false,
                });
            } else {
                $state.go(page, params);
            }
        }

        // UI apply
        function safeApply() {
            const phase = $rootScope.$$phase;
            if (!phase) {
                $rootScope.$apply();
            }
        }

        // - ---- - //
        // - BULK - //
        // - ---- - //

        // Checkboxes
        vm.selectAllCheckboxes = (a, b, c) => {
            setTimeout(() => {
                selectAllCheckboxes(a, b, c);
                safeApply();
            }, 25);
        }

        // Open Modal
        vm.launchOptionsBulkEditModal = async function(category) {
            let modalScope = $rootScope.$new();
            modalScope.thisModelId = vm.standardModel.standardHomeModelId;
            modalScope.projectId = vm.projectId;
            modalScope.categoryName = vm.toSplitTitleCase(category);
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/energy-labs/modals/bulk-edit-standard-model-option-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: false,
            }).then(async function (response) {

                if (response.bulkEditAction === "DELETE") {

                    let modalScope = $rootScope.$new();
                    modalScope.confirmationHeader = "Confirm Delete";
                    modalScope.confirmationText = "Are you sure you want to delete these Variation options?";
                    $mdDialog.show({
                        scope: modalScope,
                        templateUrl: 'app/ui/data/generic-confirmation-modal.html',
                        parent: angular.element(document.body),
                        clickOutsideToClose: false,
                    }).then(async function (confirmed) {

                        if (confirmed) {
                            vm.standardModel.variationOptionsList = vm.standardModel.variationOptionsList.filter(o => !(o.variationCategoryCode == category && o.checkboxSelected));
                        }

                    });

                } else if (response.bulkEditAction === "COPYTOMODEL") {

                        let modalScope = $rootScope.$new();
                        modalScope.confirmationHeader = "Confirm Copy";
                        modalScope.confirmationText = "Are you sure you want to override this Home Design's Variation options?";
                        $mdDialog.show({
                            scope: modalScope,
                            templateUrl: 'app/ui/data/generic-confirmation-modal.html',
                            parent: angular.element(document.body),
                            clickOutsideToClose: false,
                        }).then(async function (confirmed) {
                            if (confirmed) {
                                let newVarOptions = vm.standardModel.variationOptionsList.filter(o => o.variationCategoryCode == category && o.checkboxSelected);
                                newVarOptions.forEach(o => o.standardHomeModelVariationOptionId = uuid4.generate());
                                standardmodelservice.addVariationOptions(response.selectedModelId, newVarOptions);
                                vm.unselectAllOptions(category);
                            }
                        });

                } else if (response.bulkEditAction === "COPY") {

                    vm.standardModel.variationOptionsList.filter(o => o.variationCategoryCode == category && o.checkboxSelected).forEach(o => {
                        vm.copyVariationOption(category, o);
                    });
                    vm.unselectAllOptions(category);

                }

                vm.bulkStatus[category].isIndeterminate = false;
                vm.bulkStatus[category].selectAllCheckboxState = false;

            });
        }

        vm.unselectAllOptions = function (category) {
            vm.standardModel.variationOptionsList.filter(o => o.variationCategoryCode == category).forEach(o => o.checkboxSelected = false)
        }

        vm.updateBulkSelectStatus = updateBulkSelectStatus;

        // Upload Spreadsheet
        vm.uploadSpreadsheetForBulkCreate = function (file) {
            if (file != null) {
                standardmodelservice.processSpreadsheet(file, vm.standardModel.standardHomeModelId).then(() => setTimeout(refreshStandardModel, 250));
            }
        }

        // - ---------------------- - //
        // - CANCEL / SAVE / DELETE - //
        // - ---------------------- - //

        // Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            if ($scope.modalInstance) {
                $mdDialog.hide();
            } else {
                $state.go("project-updateform", { projectId: vm.standardModel.projectId });
            }
        }

        // Save
        vm.save = function (finishedCallback) {
            vm.isBusy = true;
            if (vm.newRecord == true) {
                standardmodelservice.createStandardModel(vm.standardModel).then(data => {
                    vm.isBusy = false;
                    vm.cancel();
                });
            } else {
                // Add toggle settings to the standard model object
                vm.standardModel.toggleSettings = vm.toggleSettings;

                // Update this model
                standardmodelservice.updateStandardModel(vm.standardModel).then(() => {
                    // Update it's list of variations with their new variations selections
                    standardmodelservice.updateVariationsList(
                        vm.standardModel.variationList.map(m => { return {
                            standardHomeModelId: m.standardHomeModelId,
                            title: m.title,
                            variationFloorplanId: !vm.standardModel.variationOptionsSettings.floorplanIsActive ? null : m.variationFloorplanId,
                            variationDesignOptionId: !vm.standardModel.variationOptionsSettings.designOptionIsActive ? null : m.variationDesignOptionId,
                            variationFacadeId: !vm.standardModel.variationOptionsSettings.facadeIsActive ? null : m.variationFacadeId,
                            variationSpecificationId: !vm.standardModel.variationOptionsSettings.specificationIsActive ? null : m.variationSpecificationId,
                            variationConfigurationId: !vm.standardModel.variationOptionsSettings.configurationIsActive ? null : m.variationConfigurationId,
                            isActive: m.isActive,
                            view3dFloorPlans: m.view3dFloorPlans ?? false,
                            costEstimateEnabled: m.costEstimateEnabled ?? false,
                            designInsightsEnabled: m.variableMetadata.designInsightsEnabled ?? false,
                            isDefaultVariation: m.isDefaultVariation,
                            sortOrder: m.sortOrder
                        }})
                    ).then(() => {
                        // Reset toggle settings after save
                        vm.toggleSettings = {
                            isActive: { applyToAllLevels: false },
                            view3dFloorPlans: { applyToAllLevels: false },
                            costEstimateEnabled: { applyToAllLevels: false },
                            designInsightsEnabled: { applyToAllLevels: false }
                        };

                        vm.resetChangeDetection();
                        if (finishedCallback != null) {
                            finishedCallback();
                        }
                        vm.isBusy = false;
                    });
                });
            }
        }

        // Delete
        vm.delete = function () {
            vm.isBusy = true;
            standardmodelservice.deleteStandardModel(vm.standardModel.standardHomeModelId).then(() => {
                vm.isBusy = false;
                vm.cancel();
            });
        }

        // Undo Delete
        vm.undoDelete = function () {
            vm.isBusy = true;
            standardmodelservice.undoDeleteStandardModel(vm.standardModel.standardHomeModelId).then(() => {
                vm.isBusy = false;
                vm.cancel();
            });
        }

    }
})();