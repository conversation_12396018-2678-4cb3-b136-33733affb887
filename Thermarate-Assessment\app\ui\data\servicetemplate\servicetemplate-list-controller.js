(function () {
    // The ServiceTemplateListCtrl supports a list page.
    'use strict';
    var controllerId = 'ServiceTemplateListCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', 'common', 'servicetemplateservice', 'manufacturerservice', 'daterangehelper', '$state', '$timeout', serviceTemplateListController]);
    function serviceTemplateListController($rootScope, $scope, $mdDialog, common, servicetemplateservice, manufacturerservice, daterangehelper, $state, $timeout) {
        // The model for this form
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.title = 'Services Database';
        vm.serviceTemplateList = [];
        vm.listFilter = "";
        vm.actionButtons = [];
        vm.filterOptions = {};
        vm.currentFilter = "All";
        vm.totalRecords = 0;
        vm.totalFilteredRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.showMoreBusy = false;

        // Pagination variables
        vm.resultsPageSize = 100;

        // Multi-filter variables - following Construction Database pattern exactly
        vm.searchString = null;
        vm.searchStringOld = null;
        vm.searchFields = ['description', 'ServiceCategoryTitle', 'ManufacturerDescription'];
        vm.filters = [
            { field: 'serviceCategoryTitle', name: 'category', section: 1 },
            { field: 'manufacturerDescription', name: 'manufacturer', section: 1 }
        ];
        vm.filteredFilters = [];
        vm.appliedFilters = {};
        vm.appliedFiltersOld = {};
        vm.filterCountData = {};
        vm.filtersApplied = false;
        vm.initialiseComplete = false;
        vm.showDisplayDesc = false;

        // Cache variables for optimized filter count performance - following Construction Database pattern exactly
        vm.cachedFilterCounts = {};
        vm.cachedGroupCounts = {};
        vm.cachedGroupOptions = {};
        vm.filterCountLookup = null;
        vm.queryModel = {
            canSave: false,
            fields: [
                {
                    name: 'serviceCategoryCode',
                    description: 'Category',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'description',
                    description: 'Description',
                    dataType: 'string',
                    operators: []
                },
            ],
        };

        const FILTER_NAME = "serviceTemplateListCtrl-customFilter";

        // Initialize filters - following Construction Database pattern exactly
        vm.filters.forEach(filter => {
            vm.appliedFilters[filter.field] = ['Any'];
        });
        vm.appliedFiltersOld = angular.copy(vm.appliedFilters);

        // Filter the filters to only include those that have a name (not blank or button)
        vm.filteredFilters = vm.filters.filter(filter => filter.name != null && filter.name != '[blank]' && filter.name != '[moreLessButton]');

        // - WATCH - //
        $scope.$watch('vm.searchString', function (newVal, oldVal) {
            if (newVal !== oldVal && vm.initialiseComplete) {
                vm.refreshList();
            }
        });

        // Common service functions - following Construction Database pattern exactly
        vm.anyFiltersApplied = common.anyFiltersApplied;
        vm.anyOptionsSelectedOnField = common.anyOptionsSelectedOnField;

        vm.filteredAppliedFilters = function () {
            let result = {};
            vm.filteredFilters.forEach(filter => {
                result[filter.field] = vm.appliedFilters[filter.field];
            });
            return result;
        };

        vm.getFilterSelectedText = common.getMultiFilterSelectedText;
        vm.optionName = common.getOptionName;

        vm.keyToName = function (key) {
            if (key === 'category') return 'Category';
            if (key === 'manufacturer') return 'Manufacturer';
            return common.toSplitTitleCase(key);
        };

        // Create a lookup map for faster filter count lookups - following Construction Database pattern exactly
        vm.updateFilterCountLookup = function() {
            vm.filterCountLookup = {};

            if (!vm.filterCountData) return;

            Object.keys(vm.filterCountData).forEach(fieldName => {
                vm.filterCountLookup[fieldName] = {};

                if (vm.filterCountData[fieldName]) {
                    Object.keys(vm.filterCountData[fieldName]).forEach(key => {
                        vm.filterCountLookup[fieldName][key.toLowerCase()] = vm.filterCountData[fieldName][key];
                    });
                }
            });
        }

        // Optimized function to get filter count - uses caching and lookup map - following Construction Database pattern exactly
        vm.getFilterCountItem = function (filter, item) {
            if (!item) {
                return 0;
            }

            let fieldName = filter.field;
            let itemValue = item.value?.toString().toLowerCase();

            if (!itemValue) {
                return 0;
            }

            // If initialization is not complete, return a default value to show all options
            if (!vm.initialiseComplete) {
                return 1;
            }

            // Use cache if available
            let cacheKey = `${fieldName}_${itemValue}`;
            if (vm.cachedFilterCounts[cacheKey] !== undefined) {
                return vm.cachedFilterCounts[cacheKey];
            }

            // Use lookup map for faster access
            if (vm.filterCountLookup && vm.filterCountLookup[fieldName]) {
                const count = vm.filterCountLookup[fieldName][itemValue];
                vm.cachedFilterCounts[cacheKey] = count || 0;
                return count || 0;
            }

            // Fallback to original method if lookup not available
            if (!vm.filterCountData[fieldName]) {
                vm.cachedFilterCounts[cacheKey] = 0;
                return 0;
            }

            let valueKey = Object.keys(vm.filterCountData[fieldName]).find(option =>
                option.toLowerCase() === itemValue);

            const count = valueKey ? vm.filterCountData[fieldName][valueKey] : 0;
            vm.cachedFilterCounts[cacheKey] = count;
            return count;
        }

        // Optimized function to get group options - uses caching - following Construction Database pattern exactly
        vm.getGroupOptions = function (filter, group) {
            let fieldName = filter.field;
            let groupKey = `${fieldName}_${group.value}`;

            // Return cached result if available
            if (vm.cachedGroupOptions[groupKey]) {
                return vm.cachedGroupOptions[groupKey];
            }

            let filteredFilterOptions = vm.filterOptions[fieldName]?.filter(f => group.startRange > group.endRange
                                                                                 ? +f.value > group.startRange || +f.value <= group.endRange
                                                                                 : +f.value > group.startRange && +f.value <= group.endRange);

            // Create a lookup map for faster filtering
            if (!vm.filterCountLookup) {
                vm.updateFilterCountLookup();
            }

            filteredFilterOptions = filteredFilterOptions?.filter(o => {
                const countItem = vm.getFilterCountItem(filter, o);
                return countItem > 0;
            });

            // Cache the result
            vm.cachedGroupOptions[groupKey] = filteredFilterOptions;
            return filteredFilterOptions;
        }

        // Optimized function to get group count - uses caching - following Construction Database pattern exactly
        vm.getGroupCount = function (filter, group) {
            let cacheKey = `${filter.field}_${group.value}`;

            // Return cached result if available
            if (vm.cachedGroupCounts[cacheKey] !== undefined) {
                return vm.cachedGroupCounts[cacheKey];
            }

            let optionsForThisGroup = vm.getGroupOptions(filter, group);
            if (!optionsForThisGroup || optionsForThisGroup.length === 0) {
                vm.cachedGroupCounts[cacheKey] = 0;
                return 0;
            }

            // Calculate the total once and cache it
            let thisGroupTotal = 0;
            for (let i = 0; i < optionsForThisGroup.length; i++) {
                const count = vm.getFilterCountItem(filter, optionsForThisGroup[i]);
                thisGroupTotal += count || 0;
            }

            vm.cachedGroupCounts[cacheKey] = thisGroupTotal;
            return thisGroupTotal;
        }

        // Clear caches when filter data changes - following Construction Database pattern exactly
        vm.clearFilterCaches = function() {
            vm.cachedFilterCounts = {};
            vm.cachedGroupCounts = {};
            vm.cachedGroupOptions = {};
            vm.filterCountLookup = null;
        }

        // Add missing group-related functions from Construction Database controller
        vm.selectOptionForGroup = function (filter, selected, group) {
            let fieldName = filter.field;
            let allOptionsForThisGroup = [...vm.getGroupOptions(filter, group).map(o => o.value), group.value];
            // function selectAllLogic(allOptions, listBefore, listChildIdKey, selected, selectAllCode)
            let newSelectedForThisGroup = common.selectAllLogic(
                [...allOptionsForThisGroup, group.value],
                vm.appliedFilters[fieldName].filter(o => allOptionsForThisGroup.includes(o)), // Only looking at this group's selected options
                null,
                selected.value,
                group.value
            )
            // Now that we have new selected options for this group, add this group's selections to full list of selections by removing this group's options from full selected list then adding this group's new selected
            vm.appliedFilters[fieldName] = vm.appliedFilters[fieldName].filter(o => !allOptionsForThisGroup.includes(o)).concat(newSelectedForThisGroup);
        }

        // Clear filter functions - following Construction Database pattern exactly
        vm.clearFilter = function (filter) {
            vm.appliedFilters[filter.field] = ['Any'];
            vm.filtersApplied = vm.anyFiltersApplied(vm.searchString, vm.filteredFilters, vm.appliedFilters);
            vm.refreshList();
        };

        vm.clearFilters = function () {
            vm.appliedFilters = {};
            vm.appliedFiltersOld = {};
            vm.filterCountData = {};
            vm.searchString = null;
            vm.searchStringOld = null;
            vm.filters.forEach(filter => {
                vm.appliedFilters[filter.field] = ['Any'];
            });
            vm.filtersApplied = false;
            vm.resultsPageSize = 100; // Reset pagination
            vm.refreshList();
        };

        vm.serviceTemplateCategories = [];
        servicetemplateservice.getServiceCategories()
            .then((data) => { vm.serviceTemplateCategories = data; });

        vm.manufacturers = [];
        manufacturerservice.getAllManufacturersList()
            .then((data) => { vm.manufacturers = data; });

        var persistRangeName = "serviceTemplateList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('All Time', persistRangeName);
        vm.ranges = daterangehelper.getRanges('Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month',
                                                'This Quarter', 'Last Quarter', 'Current Year', 'Current Financial Year', 'Last Financial Year',
                                                'Last Year', '12 Months', 'All Time');

        // Main refresh method - matches Construction Database pattern exactly
        vm.refreshList = function () {
            // Store date range in localStorage
            localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));

            common.adjustFiltersForSelection(vm.filteredFilters, vm.appliedFilters, vm.appliedFiltersOld);
            if ( vm.initialiseComplete && (!angular.equals(vm.appliedFilters, vm.appliedFiltersOld) || !angular.equals(vm.sortBy, vm.sortByOld)) || vm.searchString != vm.searchStringOld ) {
                vm.appliedFiltersOld = angular.copy(vm.appliedFilters);
                vm.sortByOld = angular.copy(vm.sortBy);
                vm.searchStringOld = vm.searchString;
                vm.isBusy = true;

                // Create search filter
                let searchFilter = [];
                for (let i = 0; i < vm.searchFields.length; i++) {
                    if (i == vm.searchFields.length-1) {
                        searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString })
                    } else {
                        searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString, logic: "or" })
                    }
                }

                // Get filtered services using multi-filter method
                servicetemplateservice.getListMultiFiltered(vm.resultsPageSize, 1, vm.sortBy, vm.filteredFilters, vm.filterOptions, vm.filteredAppliedFilters(), searchFilter).then(function (result) {
                    if (result == undefined || result == null)
                        return; // Its been cancelled so get out of here.

                    vm.serviceTemplateList = result.data;
                    vm.totalFilteredRecords = result.total;
                    vm.showingFromCnt = vm.serviceTemplateList.length > 0 ? 1 : 0;
                    vm.showingToCnt = vm.serviceTemplateList.length;

                    // Sort favorited items to the top
                    if (vm.serviceTemplateList && vm.serviceTemplateList.length > 0) {
                        vm.serviceTemplateList.sort((a, b) => {
                            if (a.isFavourite && !b.isFavourite) return -1;
                            if (!a.isFavourite && b.isFavourite) return 1;
                            return 0;
                        });
                    }

                    vm.filtersApplied = vm.anyFiltersApplied(vm.searchString, vm.filteredFilters, vm.appliedFilters);

                    // Get filter count data and update caches
                    let filteredAppliedFilters = vm.filteredAppliedFilters();
                    servicetemplateservice.getFilterCountData(vm.filteredFilters, vm.filterOptions, filteredAppliedFilters, searchFilter).then(data => {
                        vm.filterCountData = data;
                        vm.clearFilterCaches();
                        vm.updateFilterCountLookup();
                        vm.isBusy = false;
                        vm.showMoreBusy = false;
                    });
                }).catch(function (error) {
                    vm.isBusy = false;
                    vm.showMoreBusy = false;
                });
            }
        };

        vm.createServiceTemplate = function () {
            var modalScope = $rootScope.$new();
            modalScope.viewMode = "New";
            modalScope.newRecord = true;
            var modalOptions = {
                templateUrl: 'app/ui/data/servicetemplate/servicetemplate-update.html',
                scope: modalScope,
                resolve: {
                    viewMode: function () {
                        return 'New';
                    }
                }
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(function (data) {
                // Returned from modal, so refresh list.
                vm.refreshList(null);
            }, function () {
                vm.refreshList(null);
                // Cancelled.
            })['finally'](function () {
                modalScope.modalInstance = undefined  // <--- This fixes
            });
        }



        vm.delete = async function (row, showSuccessMessage = true) {

            let index = vm.serviceTemplateList.indexOf(row);

            await servicetemplateservice.deleteServiceTemplate(row.serviceTemplateId, showSuccessMessage);
            row.deleted = true;

            vm.serviceTemplateList.splice(index, 1);

        }

        vm.clone = function (row) {

            servicetemplateservice
                .copyServiceTemplate(row.serviceTemplateId, true) // Show success message for individual clones
                .then((id) => {

                    servicetemplateservice
                        .getServiceTemplate(id)
                        .then(template => {

                            // Transform the returned construction
                            // so it matches our view data.
                            let transform = {
                                ...template,
                                serviceCategoryTitle: template.serviceCategory.title
                            };

                            // Add returned template to list.
                            vm.serviceTemplateList.push(transform);

                            // Ideally sort based on what we're actually sorting by, but since
                            // we basically only have the Template Name to go off...
                            vm.serviceTemplateList.sort((a, b) => (a.description > b.description)
                                ? 1
                                : ((b.templateName > a.templateName)
                                    ? 1
                                    : -1));
                        });

                });
        }

        vm.bulkSelect = function (state) {
            vm.serviceTemplateList.forEach(x => x.isBulkSelected = state);
        }

        vm.bulkSelectionsExist = function () {
            return vm.serviceTemplateList.some(x => x.isBulkSelected);
        }

        vm.bulkDelete = async function() {
            let toDelete = vm.serviceTemplateList.filter(x => x.isBulkSelected);
            const deletePromises = [];

            for (let i = 0; i < toDelete.length; i++) {
                deletePromises.push(servicetemplateservice.deleteServiceTemplate(toDelete[i].serviceTemplateId, false));
            }

            await Promise.all(deletePromises);

            toDelete.forEach(item => {
                item.deleted = true;
                let index = vm.serviceTemplateList.indexOf(item);
                if (index !== -1) {
                    vm.serviceTemplateList.splice(index, 1);
                }
            });

            vm.serviceTemplateList.forEach(item => item.isBulkSelected = false);
            vm.bulkSelected = false;

            common.logger.logSuccess(`${toDelete.length} Service Templates deleted successfully`);
        }

        vm.addCustomFilter = function () {

            let categoriesTransformed = vm.serviceTemplateCategories.map(x => ({ title: x.title, value: x.serviceCategoryCode }));
            // Just match on description otherwise the search filter will display the id...
            let manufacturersTransformed = vm.manufacturers.map(x => ({ title: x.description, value: x.description }));

            let options = [
                {
                    title: "Service Category",
                    field: 'serviceCategoryCode',
                    operator: 'eq',
                    elementType: "select",
                    values: categoriesTransformed
                },
                {
                    title: "Manufacturer",
                    field: 'ManufacturerDescription',
                    operator: 'eq',
                    elementType: "select",
                    values: manufacturersTransformed
                }
            ];

            var modalScope = $rootScope.$new(true);
            modalScope.options = options;

            $mdDialog.show({
                templateUrl: 'app/ui/data/generic-filter-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
                scope: modalScope,
                skipHide: true, // DON'T HIDE THE MODAL
            })
                .then(function (response) {
                    vm.customFilter = response;
                    vm.refreshList(vm.customFilter);
                    localStorage.setItem(FILTER_NAME, JSON.stringify(vm.customFilter));

                }, function () { });
        };

        vm.removeCustomFilter = function (f) {

            vm.customFilter = vm.customFilter.filter(x => x.field != f.field);

            if (vm.customFilter.length == 0) {
                vm.customFilter = null;
            }

            localStorage.setItem(FILTER_NAME, JSON.stringify(vm.customFilter));
            vm.refreshList(vm.customFilter);
        };

        vm.getPreviousCustomFilters = function () {
            var savedState = JSON.parse(localStorage.getItem(FILTER_NAME));

            if (savedState) {
                vm.customFilter = savedState;
                vm.refreshList(vm.customFilter);
            }
        }

        setTimeout(() => vm.getPreviousCustomFilters(), 200);

        // Initialize multi-filters - following Construction Database pattern exactly
        vm.initializeMultiFilters = function() {
            vm.isBusy = true;

            // Use Promise.all to make concurrent API calls
            Promise.all([
                servicetemplateservice.getList(null, vm.rptDateRange.startDate.toISOString(), vm.rptDateRange.endDate.toISOString(), vm.resultsPageSize, 1, null, null),
                servicetemplateservice.getMultiFilterOptions(vm.filteredFilters)
            ]).then(results => {
                // Process service templates data (first promise result)
                const serviceTemplatesResult = results[0];
                vm.serviceTemplateList = serviceTemplatesResult.data;
                vm.totalRecords = serviceTemplatesResult.total;
                vm.totalFilteredRecords = vm.totalRecords;

                // Sort favorited items to the top
                if (vm.serviceTemplateList && vm.serviceTemplateList.length > 0) {
                    vm.serviceTemplateList.sort((a, b) => {
                        if (a.isFavourite && !b.isFavourite) return -1;
                        if (!a.isFavourite && b.isFavourite) return 1;
                        return 0;
                    });
                }

                // Process filter options (second promise result)
                const filterOptions = results[1];
                console.log('Services Database - Filter Options received:', filterOptions);
                vm.filterOptions = filterOptions;
                console.log('Services Database - vm.filterOptions set to:', vm.filterOptions);
                console.log('Services Database - vm.filteredFilters:', vm.filteredFilters);

                // Debug: Check if filter options are properly structured
                if (vm.filterOptions) {
                    Object.keys(vm.filterOptions).forEach(fieldName => {
                        console.log(`Services Database - Field ${fieldName} has ${vm.filterOptions[fieldName]?.length || 0} options:`, vm.filterOptions[fieldName]);
                    });
                }

                common.orderFilterOptions(vm.filterOptions);
                common.adjustFiltersForSelection(vm.filteredFilters, vm.appliedFilters, vm.appliedFiltersOld);
                vm.appliedFiltersOld = angular.copy(vm.appliedFilters);

                // Create search filter
                let searchFilter = [];
                for (let i = 0; i < vm.searchFields.length; i++) {
                    if (i == vm.searchFields.length-1) {
                        searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString })
                    } else {
                        searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString, logic: "or" })
                    }
                }

                // Make additional API call for filter count data after both initial calls are complete
                let filteredAppliedFilters = vm.filteredAppliedFilters();
                servicetemplateservice.getFilterCountData(vm.filteredFilters, vm.filterOptions, filteredAppliedFilters, searchFilter).then(data => {
                    // Process filter count data
                    console.log('Services Database - Filter Count Data received:', data);
                    vm.filterCountData = data;
                    console.log('Services Database - vm.filterCountData set to:', vm.filterCountData);
                    vm.clearFilterCaches();
                    vm.updateFilterCountLookup();

                    // Update UI
                    vm.showingFromCnt = vm.totalRecords > 0 ? 1 : 0;
                    vm.showingToCnt = vm.totalRecords < vm.resultsPageSize ? vm.totalRecords : vm.resultsPageSize;
                    vm.isBusy = false;
                    $timeout(() => {
                        console.log('Services Database - Setting initialiseComplete to true');
                        console.log('Services Database - Final vm.filterOptions:', vm.filterOptions);
                        console.log('Services Database - Final vm.filterCountData:', vm.filterCountData);
                        vm.initialiseComplete = true;
                    }, 1000);
                }).catch(error => {
                    console.error('Error loading filter count data:', error);
                    vm.initialiseComplete = true;
                    vm.isBusy = false;
                });
            }).catch(error => {
                console.error('Error initializing multi-filters:', error);
                vm.isBusy = false;
            });
        };

        // Initialize on load
        vm.initializeMultiFilters();

        vm.createCustomFilterLabel = function(field) {
            let overriddenField = field;

            // Override for UI
            if (field === 'ManufacturerDescription')
                overriddenField = 'Manufacturer';
            else if (field === 'serviceCategoryCode')
                overriddenField = 'Service Category';
            else
                overriddenField = vm.toSplitTitleCase(overriddenField);

            return overriddenField;
        }

        vm.toSplitTitleCase = (s) => common.toSplitTitleCase(s);

        function setActionButtons() {
            vm.actionButtons = [];
            vm.actionButtons.push({
                onclick: vm.createServiceTemplate,
                name: 'Add New',
                desc: 'Add New',
                roles: ['settings__settings__create'],
            });
        }

        setActionButtons();

        /**
         * Attempts to process the given excel files. Will show a dialog with
         * further actions required depending on how the processing went
         * (i.e. if any warnings or errors were encountered)
         *
         * @param {any} file Excel file to upload
         */
        vm.uploadFile = function (file) {

            if (file == null)
                return;

            // TODO: Get this working! Will need to have a new template to
            // replace the construction-import below I *THINK*. Maybe if we get
            // the data format the same it will 'just work' (or we can modify it
            // to make it more generic).
            servicetemplateservice.uploadTemplateDatabase(file, false)
                .then((data) => {
                    var modalScope = $rootScope.$new();
                    modalScope.excelData = file;
                    modalScope.response = data;
                    modalScope.isInitialProcess = true;
                    modalScope.extractor = 'services';

                    modalScope.modalInstance = $mdDialog.show({
                        templateUrl: 'app/ui/data/construction/construction-import.html',
                        scope: modalScope,
                    });
                });
        }

        /**
         * Exports the database to an Excel file in the same format as expected for imports
         */
        vm.exportDatabase = function () {
            vm.isBusy = true;
            servicetemplateservice.exportTemplateDatabase()
                .then(() => {
                    vm.isBusy = false;
                })
                .catch(() => {
                    vm.isBusy = false;
                });
        }

        vm.createServiceTemplate = function() {
            $state.go("servicetemplate-updateform", { serviceTemplateId: null });
        }

        vm.goToServiceTemplate = function(serviceTemplateId) {
            $state.go("servicetemplate-updateform", { serviceTemplateId: serviceTemplateId });
        }

        vm.setFavouriteStatus = function(serviceTemplateId, isFavourite) {
            servicetemplateservice.setIsFavourite(serviceTemplateId, isFavourite)
                .then(function() {
                    // Update the local list item
                    const item = vm.serviceTemplateList.find(x => x.serviceTemplateId === serviceTemplateId);
                    if (item) {
                        item.isFavourite = isFavourite;
                    }
                });
        }

        vm.showBulkEditModal = function() {
            let selectedItems = vm.serviceTemplateList.filter(x => x.isBulkSelected);

            if (selectedItems.length === 0) {
                return;
            }

            var modalScope = $rootScope.$new();
            modalScope.title = "Bulk Edit";

            $mdDialog.show({
                templateUrl: 'app/ui/data/servicetemplate/bulk-edit-servicetemplate-modal.html',
                scope: modalScope,
                clickOutsideToClose: false
            }).then(function(action) {
                if (action === 'DELETE') {
                    vm.bulkDelete();
                } else if (action === 'COPY') {
                    vm.bulkCopy(selectedItems);
                } else if (action === 'EXPORT') {
                    vm.bulkExport();
                }
            });
        }

        vm.bulkCopy = function(items) {
            const promises = [];

            // Clone each item without showing individual success messages
            items.forEach(item => {
                promises.push(servicetemplateservice
                    .copyServiceTemplate(item.serviceTemplateId, false)
                    .then((id) => {
                        return servicetemplateservice
                            .getServiceTemplate(id)
                            .then(template => {
                                // Transform the returned construction
                                // so it matches our view data.
                                let transform = {
                                    ...template,
                                    serviceCategoryTitle: template.serviceCategory.title
                                };

                                // Add returned template to list.
                                vm.serviceTemplateList.push(transform);
                            });
                    }));
            });

            // Show a single success message after all items are copied
            Promise.all(promises).then(() => {
                // Sort the list
                vm.serviceTemplateList.sort((a, b) => (a.description > b.description)
                    ? 1
                    : ((b.templateName > a.templateName)
                        ? 1
                        : -1));

                // Clear all selections
                items.forEach(item => item.isBulkSelected = false);
                vm.bulkSelected = false;

                // Show a single success message
                common.logger.logSuccess(`${items.length} Service Templates copied successfully`);
            });
        }

        vm.bulkExport = function () {
            vm.isBusy = true;
            let selectedItems = vm.serviceTemplateList.filter(x => x.isBulkSelected);
            let selectedIds = selectedItems.map(item => item.serviceTemplateId);

            servicetemplateservice.exportTemplateDatabase(selectedIds)
                .then(() => {
                    vm.isBusy = false;

                    // Clear all selections
                    selectedItems.forEach(item => item.isBulkSelected = false);
                    vm.bulkSelected = false;

                    // Show a success message
                    common.logger.logSuccess(`${selectedItems.length} Service Templates exported successfully`);
                })
                .catch(() => {
                    vm.isBusy = false;
                });
        }

        // Show more results function - matches Home page pattern
        vm.getMoreResults = function () {
            vm.showMoreBusy = true;
            vm.resultsPageSize += 100;
            // Force refresh by marking filters as changed
            vm.appliedFiltersOld = {};
            vm.refreshList();
        }
    }
})();