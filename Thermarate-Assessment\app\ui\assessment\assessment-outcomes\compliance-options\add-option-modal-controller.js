(function () {

    'use strict';

    let controllerId = 'addOptionModalController';

    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state', '$q',
            'bootstrap.dialog', 'uuid4',
            'compliancemethodservice',
            'assessmentsoftwareservice',
            'buildingconstructiontemplateservice',
            'buildingdesigntemplateservice',
            'constructionservice',
            'buildingservicestemplateservice',
            'certificationservice',
            addOptionModalController]);

    function addOptionModalController($rootScope, $scope, $mdDialog, $stateParams, $state, $q,
                                      modalDialog, uuid4,
                                      compliancemethodservice,
                                      assessmentsoftwareservice,
                                      buildingconstructiontemplateservice,
                                      buildingdesigntemplateservice,
                                      constructionservice,
                                      buildingservicestemplateservice,
                                      certificationservice) {

        // The model for this form 
        const vm = this;

        vm.assessment = $scope.assessment;
        vm.baseline = $scope.baseline;
        vm.option = $scope.option;
        vm.complianceMethodList = $scope.complianceMethodList;

        vm.sectorDeterminationList = [];
        let sectorsPromise = certificationservice.getSectorDeterminations()
            .then(data => {
                vm.sectorDeterminationList = data;
            });

        vm.certificationList = [];
        var certificationPromise = certificationservice.getList()
            .then(function (data) {
                vm.allCertificationList = data.data;
                vm.certificationList = vm.allCertificationList;
            });

        // All of these calls are cached, so no HTTP request should be required.
        vm.allAssessmentSoftwareList = [];

        vm.buildingConstructionTemplates = [];
        vm.buildingOpeningTemplates = [];
        vm.buildingServicesTemplates = [];
        vm.buildingDesignTemplates = [];

        vm.tempRequiredHouseEnergyRating = vm.option.requiredHouseEnergyRating.toFixed(1);

        // Get client available values.
        vm.availableHouseEnergyRatings = vm.assessment.job.client.clientOptions.availableHouseEnergyRatings;
        vm.availableHouseEnergyRatings = vm.availableHouseEnergyRatings?.sort((a, b) => a - b);

        // Convert to and from set to remove duplicates if present.
        vm.availableHouseEnergyRatings = [...new Set(vm.availableHouseEnergyRatings)];

        vm.blankConstructionTemplate = {

            categoriesNotRequired: {},
            zoneTypesNotApplicable: {},
            openings: [],
            surfaces: [],
            categoriesWithExternalData: {},
            classification: null,
            lowestLivingAreaFloorType: null,
            masonryWalls: null,
        };

        vm.blankDesignTemplate = {
            buildingDesignTemplateId: null,

            projectDescription: null,
            projectDescriptionOther: null,
            worksDescription: null,
            projectClassification: null,
            designFeatures: {},
            design: null,
            storeys: null,
            zones: null,
            buildingOrientation: null
        };

        vm.blankServicesTemplate = {
            categoriesNotRequired: {},
            zoneTypesNotApplicable: {},
            services: [],
        };

        function initialize() {

            assessmentsoftwareservice.getAll()
                .then(data => {
                    vm.allAssessmentSoftwareList = data;
                    determineAvailableSoftware(vm.option.complianceMethod.complianceMethodCode);
                });

            buildingconstructiontemplateservice.getAll('construction')
                .then((data) => vm.buildingConstructionTemplates = data);

            buildingconstructiontemplateservice.getAll('opening')
                .then((list) => vm.buildingOpeningTemplates = list);

            buildingservicestemplateservice.getAll()
                .then((list) => vm.buildingServicesTemplates = list);

            buildingdesigntemplateservice
                .getAll()
                .then((list) => vm.buildingDesignTemplates = list);

            //  Assign client template defaults...
            vm.option.proposed.newBuildingZoneTemplateId = vm.assessment.job.client.clientDefault.buildingZonesTemplateId;
            vm.option.proposed.newConstructionTemplateId = vm.assessment.job.client.clientDefault.proposedConstructionTemplateId;
            vm.option.proposed.newOpeningTemplateId = vm.assessment.job.client.clientDefault.proposedOpeningTemplateId;
            vm.option.proposed.newServicesTemplateId = vm.assessment.job.client.clientDefault.proposedServicesTemplateId;

            vm.option.reference.newBuildingZoneTemplateId = vm.assessment.job.client.clientDefault.referenceBuildingZonesTemplateId;
            vm.option.reference.newConstructionTemplateId = vm.assessment.job.client.clientDefault.referenceConstructionTemplateId;
            vm.option.reference.newOpeningTemplateId = vm.assessment.job.client.clientDefault.referenceOpeningTemplateId;
            vm.option.reference.newServicesTemplateId = vm.assessment.job.client.clientDefault.referenceServicesTemplateId;

            vm.option.isComplianceValid = true;
            vm.option.isShownToClient = false;

        }

        initialize();

        vm.title = () => `Compliance Option ${vm.option.optionIndex}`;

        function determineAvailableSoftware(complianceMethodCode) {
            vm.availableAssessmentSoftwareList = compliancemethodservice
                .determineAvailableSoftware(vm.allAssessmentSoftwareList, complianceMethodCode);
        }
        vm.determineAvailableSoftware = determineAvailableSoftware;

        vm.availableComplianceMethods = function() {
            return compliancemethodservice.determineAvailableComplianceMethods(vm.complianceMethodList, vm.assessment.worksDescription.worksDescriptionCode);
        }

        //Cancel - Close Modal Or Route back to last page.
        vm.cancel = function () {
            $mdDialog.cancel();
        }

        vm.save = async function () {

            // TODO: ...
            // Special Functionality Relating to the General Tab:
            // when Assessment Method for the Baseline is the SAME as for the Compliance Option:
            //     the General Tab should ALWAYS be copied from the Baseline when a New Compliance Option is added
            // when Assessment Method for the Baseline is DIFFERENT to the Compliance Option:
            //     Only the Proposed Building sub-tab is copied, and the Reference/DTS Building sub-tab of the Compliance Option is blank (including the Not Applicable boxes = CHECKED)
            // Keeping in mind that the 'copy x' functions below should now no longer touch anything in the general tab.

            // when Assessment Method for the Baseline is the SAME as for the Compliance Option:
            //     the General Tab should ALWAYS be copied from the Baseline when a New Compliance Option is added
            // Note: I assume the above means that both the proposed AND reference building get the data from the baseline...
            if(vm.option.complianceMethod.complianceMethodCode === vm.baseline.complianceMethod.complianceMethodCode) {

                // Proposed can be left as-is. Copy reference across.
                // TODO: Confirm Alistair wants to copy the baseline reference building into the new option reference
                //  and NOT the baseline proposed building into the new option reference building.
                // vm.option.reference.projectDescription = vm.baseline.proposed.projectDescription;
                // vm.option.reference.projectDescriptionOther = vm.baseline.proposed.projectDescriptionOther;
                // vm.option.reference.projectClassification = vm.baseline.proposed.projectClassification;
                // vm.option.reference.design = vm.baseline.proposed.design;
                // vm.option.reference.buildingOrientation = vm.baseline.proposed.buildingOrientation;
                // vm.option.reference.lowestLivingAreaFloorType = vm.baseline.proposed.lowestLivingAreaFloorType;
                // vm.option.reference.masonryWalls = vm.baseline.proposed.masonryWalls;
                // vm.option.reference.buildingWidth = vm.baseline.proposed.buildingWidth;
                // vm.option.reference.buildingLength = vm.baseline.proposed.buildingLength;
                //
                // vm.option.reference.spaces = vm.baseline.proposed.spaces;
                // vm.option.reference.roofs = vm.baseline.proposed.roofs;
                // vm.option.reference.zoneTypesNotApplicable.floorPlanSpaces = vm.baseline.proposed.zoneTypesNotApplicable.floorPlanSpaces;
                // vm.option.reference.zoneTypesNotApplicable.generalroofs = vm.baseline.proposed.zoneTypesNotApplicable.generalroofs;

            } else if(vm.option.complianceMethod.complianceMethodCode !== vm.baseline.complianceMethod.complianceMethodCode) {
                // when Assessment Method for the Baseline is DIFFERENT to the Compliance Option:
                //     Only the Proposed Building sub-tab is copied, and the Reference/DTS Building sub-tab of the Compliance Option is blank (including the Not Applicable boxes = CHECKED)

                vm.option.reference.projectDescription = null;
                vm.option.reference.projectDescriptionOther = null;
                vm.option.reference.projectClassification = null;
                vm.option.reference.design = null;
                vm.option.reference.buildingOrientation = null;
                vm.option.reference.lowestLivingAreaFloorType = null;
                vm.option.reference.masonryWalls = null;
                vm.option.reference.buildingWidth = null;
                vm.option.reference.buildingLength = null;

                vm.option.reference.spaces = null;
                vm.option.reference.roofs = null;
                vm.option.reference.zoneTypesNotApplicable.floorPlanSpaces = true;
                vm.option.reference.zoneTypesNotApplicable.generalroofs = true;
            }

            applyCopyOptionZoneTemplates(vm.option, vm.option.proposed, 'proposed');
            applyCopyOptionZoneTemplates(vm.option, vm.option.reference, 'reference');

            await applyCopyOptionConstructionTemplate(vm.option, 'proposed', 'construction');
            await applyCopyOptionConstructionTemplate(vm.option, 'reference', 'construction');

            await applyCopyOptionConstructionTemplate(vm.option, 'proposed', 'opening');
            await applyCopyOptionConstructionTemplate(vm.option, 'reference', 'opening');

            await applyCopyOptionServicesTemplate(vm.option, 'proposed');
            await applyCopyOptionServicesTemplate(vm.option, 'reference');

            // Copy energy values if required.
            if(vm.option.proposed.copyAnnualEnergyLoads === true) {

                vm.option.proposed.heating = vm.baseline.proposed.heating;
                vm.option.proposed.cooling = vm.baseline.proposed.cooling;
                vm.option.proposed.totalEnergyLoad = vm.baseline.proposed.totalEnergyLoad;
                vm.option.proposed.houseEnergyRating = vm.baseline.proposed.houseEnergyRating;
                vm.option.proposed.energyUsageSummary = angular.copy(vm.baseline.proposed.energyUsageSummary);

                vm.option.proposed.heatingOriginal = vm.baseline.proposed.heatingOriginal;
                vm.option.proposed.coolingOriginal = vm.baseline.proposed.coolingOriginal;
                vm.option.proposed.totalEnergyLoadOriginal = vm.baseline.proposed.totalEnergyLoadOriginal;
                vm.option.proposed.houseEnergyRatingOverride = vm.baseline.proposed.houseEnergyRatingOverride;
                vm.option.proposed.overrideEnergyLoads = vm.baseline.proposed.overrideEnergyLoads || false;

                // If we are copying a compliance method which does NOT "require" a reference building, we actually _DO_ 
                // copy across the reference building energy values since they are used for 'calculated' values.
                // It sounds counter-intuitive but trust me.
                if(vm.baseline.complianceMethod.requiresReferenceBuilding === false) {

                    vm.option.reference.heating = vm.baseline.reference.heating;
                    vm.option.reference.cooling = vm.baseline.reference.cooling;
                    vm.option.reference.totalEnergyLoad = vm.baseline.reference.totalEnergyLoad;
                    vm.option.reference.houseEnergyRating = vm.baseline.reference.houseEnergyRating;
                    vm.option.reference.energyUsageSummary = angular.copy(vm.baseline.reference.energyUsageSummary);

                    vm.option.reference.heatingOriginal = vm.baseline.reference.heatingOriginal;
                    vm.option.reference.coolingOriginal = vm.baseline.reference.coolingOriginal;
                    vm.option.reference.totalEnergyLoadOriginal = vm.baseline.reference.totalEnergyLoadOriginal;
                    vm.option.reference.houseEnergyRatingOverride = vm.baseline.reference.houseEnergyRatingOverride;
                    vm.option.reference.overrideEnergyLoads = vm.baseline.reference.overrideEnergyLoads || false;

                }

            } else {
                vm.option.proposed.heating = null;
                vm.option.proposed.cooling = null;
                vm.option.proposed.totalEnergyLoad = null;
                vm.option.proposed.houseEnergyRating = null;
                vm.option.proposed.energyUsageSummary = null;

                vm.option.proposed.heatingOriginal = null;
                vm.option.proposed.coolingOriginal = null;
                vm.option.proposed.totalEnergyLoadOriginal = null;
                vm.option.proposed.houseEnergyRatingOverride = null;
                vm.option.proposed.overrideEnergyLoads = false;
            }

            if(vm.option.reference.copyAnnualEnergyLoads === true) {

                if(vm.copyProposedIntoReference()) {
                    // Copy whatever values have already been applied to THIS new options proposed building.
                    vm.option.reference.heating = vm.option.proposed.heating
                    vm.option.reference.cooling = vm.option.proposed.cooling;
                    vm.option.reference.totalEnergyLoad = vm.option.proposed.totalEnergyLoad;
                    vm.option.reference.houseEnergyRating = vm.option.proposed.houseEnergyRating;
                    vm.option.reference.energyUsageSummary = angular.copy(vm.option.proposed.energyUsageSummary);

                    vm.option.reference.heatingOriginal = vm.baseline.proposed.heatingOriginal;
                    vm.option.reference.coolingOriginal = vm.baseline.proposed.coolingOriginal;
                    vm.option.reference.totalEnergyLoadOriginal = vm.baseline.proposed.totalEnergyLoadOriginal;
                    vm.option.reference.houseEnergyRatingOverride = vm.baseline.proposed.houseEnergyRatingOverride;
                    vm.option.reference.overrideEnergyLoads = vm.baseline.proposed.overrideEnergyLoads || false;

                } else {
                    vm.option.reference.heating = vm.baseline.reference.heating;
                    vm.option.reference.cooling = vm.baseline.reference.cooling;
                    vm.option.reference.totalEnergyLoad = vm.baseline.reference.totalEnergyLoad;
                    vm.option.reference.houseEnergyRating = vm.baseline.reference.houseEnergyRating;
                    vm.option.reference.energyUsageSummary = angular.copy(vm.baseline.reference.energyUsageSummary);

                    vm.option.reference.heatingOriginal = vm.baseline.reference.heatingOriginal;
                    vm.option.reference.coolingOriginal = vm.baseline.reference.coolingOriginal;
                    vm.option.reference.totalEnergyLoadOriginal = vm.baseline.reference.totalEnergyLoadOriginal;
                    vm.option.reference.houseEnergyRatingOverride = vm.baseline.reference.houseEnergyRatingOverride;
                    vm.option.reference.overrideEnergyLoads = vm.baseline.reference.overrideEnergyLoads || false;
                }

            } else {

                vm.option.reference.heating = null;
                vm.option.reference.cooling = null;
                vm.option.reference.totalEnergyLoad = null;
                vm.option.reference.houseEnergyRating = null;
                vm.option.reference.energyUsageSummary = null;

                vm.option.reference.heatingOriginal = null;
                vm.option.reference.coolingOriginal = null;
                vm.option.reference.totalEnergyLoadOriginal = null;
                vm.option.reference.houseEnergyRatingOverride = null;
                vm.option.reference.overrideEnergyLoads = false;
            }

            // Copy assessment files if required.
            if(vm.option.proposed.copyAssessmentFiles === true) {
                vm.option.proposed.fileA = vm.baseline.proposed.fileA;
                vm.option.proposed.fileB = vm.baseline.proposed.fileB;
                vm.option.proposed.fileC = vm.baseline.proposed.fileC;
                vm.option.proposed.fileD = vm.baseline.proposed.fileD;
                vm.option.proposed.fileE = vm.baseline.proposed.fileE;
                vm.option.proposed.markupFile = vm.baseline.proposed.markupFile;
            }

            if(vm.option.reference.copyAssessmentFiles === true) {

                if(vm.copyProposedIntoReference()) {
                    // Copy whatever values have already been applied to THIS new options proposed building.
                    vm.option.reference.fileA = vm.option.proposed.fileA;
                    vm.option.reference.fileB = vm.option.proposed.fileB;
                    vm.option.reference.fileC = vm.option.proposed.fileC;
                    vm.option.reference.fileD = vm.option.proposed.fileD;
                    vm.option.reference.fileE = vm.option.proposed.fileE;
                    vm.option.reference.markupFile = vm.option.proposed.markupFile;
                } else {
                    vm.option.reference.fileA = vm.baseline.reference.fileA;
                    vm.option.reference.fileB = vm.baseline.reference.fileB;
                    vm.option.reference.fileC = vm.baseline.reference.fileC;
                    vm.option.reference.fileD = vm.baseline.reference.fileD;
                    vm.option.reference.fileE = vm.baseline.reference.fileE;
                    vm.option.reference.markupFile = vm.baseline.reference.markupFile;
                }
            }

            vm.option.proposed.zones?.forEach(x => x.zoneId = uuid4.generate());
            vm.option.reference.zones?.forEach(x => x.zoneId = uuid4.generate());

            // console.log("Returning: ", vm.option);

            $mdDialog.hide(vm.option);
        }

        function applyCopyOptionZoneTemplates(option, building, templateType) {

            const templateId = building.newBuildingZoneTemplateId;

            // If copy, do nothing! Ok as-is - UNLESS we are copying from a method which has NO reference into a method 
            // WITH a reference. In which case, we wish to copy the baseline into the new reference.
            if (templateId === "COPY" || templateId === null) {

                if(vm.copyProposedIntoReference() && templateType === "reference") 
                    vm.applyDesignTemplate(option.proposed, option.reference);

            } else if (templateId === "BLANK_TEMPLATE") {

                building.buildingZonesTemplateId = null;
                building.zones = [];

                building.worksDescription = null;
                building.designFeatures = null;
                building.storeys = null;

            } else {

                // Assume we have a guid here.
                const template = vm.buildingDesignTemplates
                    .find(x => x.buildingDesignTemplateId === templateId);

                vm.applyDesignTemplate(template, building);
            }
        }

        async function applyCopyOptionConstructionTemplate(option, building = '', templateType) {

            let templateId = null;

            if (templateType === 'construction')
                templateId = option[building].newConstructionTemplateId;
            else if (templateType === 'opening')
                templateId = option[building].newOpeningTemplateId;

            // If copy, do nothing! Ok as-is - UNLESS we are copying from a method which has NO reference into a method 
            // WITH a reference. In which case, we wish the copy whatever the final value of the 'proposed' building is
            // for THIS new compliance option, into the reference (NOT the proposed building from the baseline!)
            if (templateId === null || templateId === "COPY") {

                if(vm.copyProposedIntoReference() && building === "reference")
                    await applyConstructionTemplate(
                        option,
                        option[building],
                        { ...option.proposed, templateType });

            }
            else if (templateId === "BLANK_TEMPLATE")
                await applyConstructionTemplate(
                    option,
                    option[building],
                    {...vm.blankConstructionTemplate, templateType},
                    building
                );
            else {

                // Assume we have a guid here.
                let template = null;

                if (templateType === 'construction')
                    template = vm.buildingConstructionTemplates.find(x => x.buildingConstructionTemplateId === templateId);
                else if (templateType === 'opening')
                    template = vm.buildingOpeningTemplates.find(x => x.buildingConstructionTemplateId === templateId);

                // sigh FFS. Have to upate this to apply both the construction
                // and opening templates here depending on selections...
                await applyConstructionTemplate(
                    option,
                    option[building],
                    template,
                    building);
            }
        }

        async function applyCopyOptionServicesTemplate(option, building = '') {

            let templateId = option[building].newServicesTemplateId;

            // If copy, do nothing! Ok as-is - UNLESS we are copying from a method which has NO reference into a method 
            // WITH a reference. In which case, we wish the copy whatever the final value of the 'proposed' building is
            // for THIS new compliance option, into the reference (NOT the proposed building from the baseline!)
            if (templateId === null || templateId === "COPY") {

                if(vm.copyProposedIntoReference() && building === "reference")
                    await buildingservicestemplateservice.applyTemplate(
                        option,
                        option.reference,
                        option.proposed
                    );
            }
            else if (templateId === "BLANK_TEMPLATE") {

                await buildingservicestemplateservice.applyTemplate(
                    option,
                    option[building],
                    {...vm.blankServicesTemplate}
                );

            } else {

                // Assume we have a guid here.
                let template = null;
                template = vm.buildingServicesTemplates.find(x => x.buildingServicesTemplateId === templateId);

                await buildingservicestemplateservice.applyTemplate(
                    option,
                    option[building],
                    template
                );

            }
        }

        async function applyConstructionTemplate(option, building, template) {

            if (template === null)
                return;

            await constructionservice.applyTemplate(option, building, template);
        }

        vm.applyConstructionTemplate = applyConstructionTemplate;

        vm.clearConstructionTemplate = function (building) {

            if (vm.job.currentAssessment.allComplianceOptions[0][building] === null)
                vm.job.currentAssessment.allComplianceOptions[0][building] = {};

            let bld = vm.job.currentAssessment.allComplianceOptions[0][building];

            vm.job.currentAssessment.allComplianceOptions[0][building].constructionTemplateId = null;
            vm.job.currentAssessment.allComplianceOptions[0][building].constructionTemplateTitle = null;
            bld.surfaces = null;
            bld.openings = null;
            bld.services = null;
        }

        /** Applies the given Zone Template. */
        vm.applyDesignTemplate = function (template, building) {

            // This may be hit if e.g. client default is set to a now-deleted template.
            if (template === null)
                return;

            if (building === null) {
                throw "No, you need to set this now!!!";
            }

            building.buildingZonesTemplateId = template.buildingDesignTemplateId;
            building.zones = template.zones;
            building.worksDescription = template.worksDescription;

            building.designFeatures = template.designFeatures;
            building.designWasBlankFromTemplate = template.design === null;
            building.storeys = template.storeys;
            building.garageLocation = template.garageLocation;

            // Re-assign GUIDs
            building.zones.forEach(x => {
                x.zoneId = uuid4.generate();
                x.createdOn = new Date().toUTCString();
                x.assessmentComplianceBuildingId = building.assessmentComplianceBuildingId
            });
        }

        /** We have to take our temp (string) value and convert it to the value which will be saved (decimal) */
        vm.handleReqHERFormat = function () {
            vm.option.requiredHouseEnergyRating = parseFloat(vm.tempRequiredHouseEnergyRating);
        }

        /** 
         * Indicates that the baseline option has no reference building, but the new option DOES, so therefore when
         * copying data for the new reference building, it needs to come from the baselines proposed building
         */
        vm.copyProposedIntoReference = function() {
            return vm.baseline.complianceMethod.requiresReferenceBuilding === false &&
                   vm.option.complianceMethod.requiresReferenceBuilding === true;
        }
    }
})();