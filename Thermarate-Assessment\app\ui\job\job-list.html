<section id="job-list-view" class="main-content-wrapper" data-ng-controller="JobListCtrl as vm">

    <div class="widget">

        <div data-cc-widget-header title="{{vm.title}}"></div>

        <md-toolbar class="widget-actionbar-wrapper header-container {{vm.filtersExpanded ? 'header-container-expanded' : null}}" flex>
            <div class="md-toolbar-tools widget-actionbar header-inner-container">

                <div class="filters-container">

                    <!-- Search -->
                    <div class="filter">
                        <div class="filter-label">Search</div>
                        <div class="search-input-container">
                            <input class="search-input"
                                   type="text"
                                   placeholder="Quick Filter"
                                   ng-model="vm.searchString"
                                   ng-keydown="$event.keyCode == 13 ? vm.refreshList() : null"
                                   ng-blur="vm.refreshList()">
                            <img src="/content/images/cross.png"
                                 class="search-clear-button"
                                 ng-show="vm.searchString"
                                 ng-click="vm.searchString = null; vm.refreshList(); $event.stopPropagation()">
                        </div>
                    </div>

                    <!-- Multi-Filters -->
                    <div ng-repeat="filter in vm.filters track by filter.field"
                         ng-if="filter.name != null"
                         ng-show="filter.section == 1 || vm.filtersExpanded"
                         class="filter {{vm.anyOptionsSelectedOnField(filter, vm.appliedFilters) ? 'options-selected' : ''}}">

                        <!-- Label -->
                        <div ng-if="filter.name != '[blank]' && filter.name != '[moreLessButton]'" class="filter-label">{{vm.keyToName(filter.name)}}</div>

                        <!-- Clear Button -->
                        <img src="/content/images/cross.png"
                             class="filter-clear-button"
                             ng-click="vm.clearFilter(filter);$event.stopPropagation()"
                        />

                        <!-- Normal Field -->
                        <md-select ng-if="filter.name != '[blank]' && filter.name != '[moreLessButton]' && !filter.isDate"
                                   class="filter-dropdown"
                                   multiple="true"
                                   md-selected-text="vm.getFilterSelectedText(filter, vm.filterOptions, vm.appliedFilters)"
                                   ng-model="vm.appliedFilters[filter.field]"
                                   ng-change="vm.refreshList()">

                            <!-- Grouped Options -->
                            <md-option ng-if="filter.groups != null"
                                       ng-show="vm.getFilterCountItem(filter, {value: 'Any'}) > 0"
                                       ng-value="'Any'">
                                Any ({{vm.getFilterCountItem(filter, {value: 'Any'})}})
                            </md-option>
                            <md-option ng-if="filter.groups != null"
                                       ng-show="vm.getFilterCountItem(filter, {value: 'Not Specified'}) > 0"
                                       ng-value="'Not Specified'">
                                Not Specified ({{vm.getFilterCountItem(filter, {value: 'Not Specified'})}})
                            </md-option>
                            <div ng-if="filter.groups != null"
                                 ng-show="vm.getGroupCount(filter, group) > 0"
                                 ng-repeat="group in filter.groups track by group.value"
                                 ng-value="item.value">
                                <div style="position:relative">
                                    <div class="group-arrow" ng-click="group.expanded = !group.expanded;$event.stopPropagation()">
                                        <div class="{{group.expanded ? 'expanded' : ''}}"/>
                                    </div>
                                    <md-option ng-value="group.value"
                                               ng-click="vm.selectOptionForGroup(filter, group, group)"
                                               class="group-option">
                                        {{group.name}} ({{vm.getGroupCount(filter, group)}})
                                    </md-option>
                                </div>
                                <md-option ng-show="group.expanded && vm.getFilterCountItem(filter, item) > 0"
                                           ng-repeat="item in vm.getGroupOptions(filter, group) track by item.value"
                                           ng-value="item.value"
                                           ng-click="vm.selectOptionForGroup(filter, item, group)"
                                           class="group-child-option">
                                    {{vm.optionName(item, filter.isBool, filter.isDecimal)}} ({{vm.getFilterCountItem(filter, item)}})
                                </md-option>
                            </div>

                            <!-- Normal Option -->
                            <md-option ng-if="filter.groups == null"
                                       ng-show="vm.getFilterCountItem(filter, item) > 0"
                                       ng-repeat="item in vm.filterOptions[filter.field] track by item.value"
                                       ng-value="item.value">
                                {{vm.optionName(item, filter.isBool, filter.isDecimal)}} ({{vm.getFilterCountItem(filter, item)}})
                            </md-option>

                        </md-select>

                        <!-- Date Field -->
                        <md-select ng-if="filter.name != '[blank]' && filter.name != '[moreLessButton]' && filter.isDate"
                                    class="filter-dropdown"
                                    multiple="true"
                                    md-selected-text="vm.appliedFilters[filter.field].label"
                                    ng-model="vm.appliedFilters[filter.field]">
                        </md-select>
                        <div ng-if="filter.isDate"
                                rd-date-range-picker
                                class="date-range-picker"
                                ng-model="vm.appliedFilters[filter.field]"
                                format="MMMM D, YYYY"
                                ranges="vm.ranges"
                                on-change="vm.refreshList">
                        </div>

                        <!-- More/Less Button -->
                        <div ng-if="filter.name == '[moreLessButton]'"
                             class="more-filters-button {{vm.filtersExpanded ? 'adjust-padding' : null}}"
                             ng-click="vm.filtersExpanded = !vm.filtersExpanded"
                        >
                            <img class="more-filters-icon" src="../../../content/images/settings.png" /> {{vm.filtersExpanded ? 'Less Filters' : 'More Filters'}}
                        </div>

                        <!-- Blank -->
                        <div ng-if="filter.name == '[blank]'"></div>

                    </div>

                </div>

                <!-- Settings -->
                <md-menu class="settings-button">
                    <md-button class="md-icon-button" ng-click="$mdOpenMenu()"><i class="material-icons">settings</i></md-button>
                    <md-menu-content>
                        <div class="columnFilter" ng-repeat="item in vm.columnOptions.columnList track by item.sortOrder">
                            <md-checkbox ng-model="vm.columnOptions[item.reference]" ng-change="vm.columnSelectionsChanged()">
                                {{item.description}}
                            </md-checkbox>
                        </div>
                    </md-menu-content>
                </md-menu>

            </div>

            <!-- Number of Items -->
            <div class="current-filter-description {{vm.filtersExpanded ? null : 'add-margin'}}" ng-show="!vm.filtersApplied">
                Showing {{vm.totalJobs}} jobs <img ng-if="vm.availableComplianceMethods != null" src="../../../content/images/flash.png" class="filter-description-lightning" ng-click="vm.showAnalytics()" />
            </div>
            <div class="current-filter-description {{vm.filtersExpanded ? null : 'add-margin'}}" ng-show="vm.filtersApplied">
                {{vm.totalFilteredJobs}} of {{vm.totalJobs}} jobs match your filters
                (<u ng-click="vm.clearFilters()" style="text-decoration: underline; cursor:pointer;">clear filters</u>)
                <img ng-if="vm.availableComplianceMethods != null" src="../../../content/images/flash.png" class="filter-description-lightning" ng-click="vm.showAnalytics()" />
            </div>

        </md-toolbar>

        <div class="table-responsive-vertical shadow-z-1" style="background-color: var(--background-grey);">

            <!-- For some reason, putting margin-top on our table below does not have the same effect -->
            <div style="height: 3px"></div>

            <table class="table table-striped table-hover table-condensed job-list-table"
                   st-table="vm.jobsList"
                   st-table-filtered-list="exportList"
                   st-global-search="vm.listFilter"
                   st-persist="jobList"
                   st-pipe="vm.refreshList"
                   st-sticky-header>
                <thead>
                    <tr>
                        <th st-sort="jobReference"                  style="display:none" class="can-sort text-left  column-jobReference"                 >Ref</th>
                        <th st-sort="clientName"                    style="display:none" class="can-sort text-left  column-clientName"                   >Client</th>
                        <th st-sort="clientJobNumber"               style="display:none" class="can-sort text-left  column-clientJobNumber"              >Client Ref</th>
                        <th st-sort="address"                       style="display:none" class="can-sort text-left  column-address"                      >Project Address</th>
                        <th st-sort="projectOwner"                  style="display:none" class="can-sort text-left  column-projectOwner"                 >Owner</th>
                        <th st-sort="projectDescriptionDescription" style="display:none" class="can-sort text-left  column-projectDescriptionDescription">Building Description</th>
                        <th st-sort="assessmentDesign"              style="display:none" class="can-sort text-left  column-assessmentDesign"             >House Type</th>
                        <th st-sort="assessmentVersion"             style="display:none" class="can-sort text-right column-assessmentVersion"            >Version</th>
                        <th st-sort="creatorFullName"               style="display:none" class="can-sort text-left  column-creatorFullName"              redi-allow-roles="['assessment_page_(tabs/sub-tabs)__creator__edit']">Creator</th>
                        <th st-sort="clientAssigneeFullName"        style="display:none" class="can-sort text-left  column-clientAssigneeFullName"       >Assignee</th>
                        <th st-sort="assessorFullName"              style="display:none" class="can-sort text-left  column-assessorFullName"             >Assessor</th>
                        <th st-sort="assessmentPriorityCode"        style="display:none" class="can-sort text-left  column-assessmentPriorityDescription">Priority</th>
                        <th st-sort="jobStatusDescription"          style="display:none" class="can-sort text-left  column-status"                       >Status</th>
                        <th st-sort="orderDate"                     style="display:none" class="can-sort text-left  column-orderDate"                    >Created</th>
                        <th st-sort="assessmentCerficateDate"       style="display:none" class="can-sort text-left  column-assessmentCerficateDate"      >Certified</th>
                        <th st-sort="jobModifiedOn"                 style="display:none" class="can-sort text-left  column-jobModifiedOn"                >Updated</th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="row in vm.jobsList"
                        class="{{row.priorityColour}} {{row.canEditJob ? 'clickable' : 'not-clickable'}}"
                        ng-click="vm.goToJob(row)"
                        ng-mousedown="vm.jobRowClick($event, row)">

                        <td data-title="Job Reference"        style="display:none" class="text-left  column-jobReference"                 >{{::row.jobReference }}</td>
                        <td data-title="Client"               style="display:none" class="text-left  column-clientName"                   >{{::row.clientName }}</td>
                        <td data-title="Client Job Ref"       style="display:none" class="text-left  column-clientJobNumber"              >{{::row.clientJobNumber }}</td>
                        <td data-title="Project Address"      style="display:none" class="text-left  column-address"                      >{{::row.address }}</td>
                        <td data-title="Project Owner"        style="display:none" class="text-left  column-projectOwner"                 >{{::row.projectOwner }}</td>
                        <td data-title="Building Description" style="display:none" class="text-left  column-projectDescriptionDescription">{{::row.projectDescriptionDescription }}</td>
                        <td data-title="House Type"           style="display:none" class="text-left  column-assessmentDesign"             >{{::row.assessmentDesign }}</td>
                        <td data-title="Version"              style="display:none" class="text-right column-assessmentVersion"            >{{::row.assessmentVersion | number }}</td>
                        <td data-title="Creator"              style="display:none" class="text-left  column-creatorFullName" redi-allow-roles="['assessment_page_(tabs/sub-tabs)__creator__edit']" >{{::row.creatorFullName }}</td>
                        <td data-title="Assignee"             style="display:none" class="text-right column-clientAssigneeFullName"       >{{::row.clientAssigneeFullName }}</td>
                        <td data-title="Assessor"             style="display:none" class="text-right column-assessorFullName"             >{{::row.assessorFullName }}</td>
                        <td data-title="Priority"             style="display:none" class="text-left  column-assessmentPriorityDescription" ng-class="{'label-red label-bold' : row.assessmentPriorityCode=='URGENT', 'label-orange label-bold' : row.assessmentPriorityCode=='HIGH'}">{{::row.assessmentPriorityDescription }}</td>
                        <td data-title="Status"               style="display:none" class="column-status" ng-init="statusCode = row.statusCode">
                            <div class="job-list-status-column">
                                <span ng-class="{
                                      'label-red' : statusCode=='JCancelled',
                                      'label-green' : statusCode=='JIssued' || statusCode=='JComplete',
                                      'label-orange' : statusCode=='JDraft' || statusCode=='JInProgress' || statusCode=='JPreliminaryReview' || statusCode=='JCompliance' || statusCode=='JRecertification'
                                }">
                                    {{::row.jobStatusDescription }}
                                </span>
                            </div>
                        </td>
                        <td data-title="Created"              style="display:none" class="text-left  column-orderDate"              >{{::row.orderDate | date: 'dd/MM/yyyy HH:mm:ss' }}</td>
                        <td data-title="Certified"            style="display:none" class="text-left  column-assessmentCerficateDate">{{::row.assessmentCerficateDate | date: 'dd/MM/yyyy HH:mm:ss' }}</td>
                        <td data-title="Updated"              style="display:none" class="text-left  column-jobModifiedOn"          >{{::row.jobModifiedOn | date: 'dd/MM/yyyy HH:mm:ss' }}</td>
                    </tr>
                </tbody>
            </table>
            <div ng-show="vm.showingToCnt < vm.totalFilteredJobs"
                 style="width:max-content; margin:auto; margin-top: -10px; margin-bottom: 20px;"
                 ng-class="{'clickable': !vm.showMoreLoading, 'loading-disabled': vm.showMoreLoading}"
                 ng-click="!vm.showMoreLoading && vm.getMoreResults()">
                <span ng-show="!vm.showMoreLoading">Show more</span>
                <i ng-show="vm.showMoreLoading" class="fa fa-spinner fa-spin" style="font-size: 16px;"></i>
            </div>
            <div class="widget-pager">
                <span ng-if="vm.totalFilteredJobs != null && vm.totalFilteredJobs > 0">Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalFilteredJobs}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>

<style>
    .not-clickable {
        cursor: default;
    }
    .job-list-status-column {
        display: grid;
        grid-template-columns: auto 36px;
        align-items: center;
        justify-content: space-between;
        justify-items: flex-end;
    }

    .header-container {
        min-height: 150px !important;
        justify-content: center;
    }

    .header-container-expanded {
        min-height: 400px !important;
    }

    .header-inner-container {
        gap: 1%;
        max-height: max-content;
        height: max-content;
        align-items: flex-start;
    }

    .filters-container {
        flex: 10;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
        column-gap: 2%;
        row-gap: 15px;
    }

    .filter {
        position: relative;
        margin-top: 32px; /* To account for heading above that is positioned absolute */
        font-size: 1.2rem !important;
    }

    .search-input-container {
        position: relative;
        width: 100%;
    }

    .search-input {
        width: 100%;
        height: 45px;
        margin-top: -6px;
        padding: 7px 10px 6px 10px;
        box-sizing: border-box;
        border: none;
        background-color: #e2e2e2;
        color: black !important;
    }

        .search-input:focus-visible {
            border: none !important;
        }

        .search-input::placeholder {
            color: black !important;
        }

        .search-input:-ms-input-placeholder {
            color: black !important;
        }

    .search-clear-button {
        position: absolute;
        right: 6px;
        top: 46%;
        transform: translateY(-50%);
        z-index: 50;
        width: 18px;
        height: auto;
        padding: 4px;
        border-radius: 50%;
        cursor: pointer;
    }

    .search-clear-button:hover {
        background-color: #eeeeee;
    }

    .filter-label {
        position: absolute;
        top: -32px;
        left: 15px;
        font-size: 12px;
        color: #8e888e;
    }

    .filter-dropdown {
        margin: -6px 0 0 2px;
        width: 100%;
        height: 45px;
        padding: 7px 10px 6px 10px;
        box-sizing: border-box;
        background-color: #e2e2e2;
        color: black !important;
    }

        .filter-dropdown span {
            margin-top: -2px;
        }

        .filter-dropdown .md-select-placeholder {
            color: black !important;
        }

            .filter-clear-button {
                display: none;
                position: absolute;
                right: 3px;
                bottom: 13px;
                z-index: 50;
                width: 18px;
                height: auto;
                padding: 4px;
                border-radius: 50%;
                cursor: pointer;
            }
            .filter-clear-button:hover {
                background-color: #eeeeee;
            }
            .filter.options-selected > .filter-clear-button {
                display: inherit !important;
            }
            .filter.options-selected .md-select-icon {
                margin-left: -36px;
            }

            .group-option {
                margin-left: 20px;
            }

                .group-arrow {
                    position: absolute;
                    left: 5px;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 18px;
                    height: 18px;
                    border-radius: 50%;
                    cursor: pointer;
                }
                .group-arrow:hover {
                    background-color: #eeeeee;
                }

                    .group-arrow > div {
                        position: absolute;
                        top: 3px;
                        left: 6px;
                        border-top: 5px solid transparent;
                        border-bottom: 5px solid transparent;
                        border-left: 6px solid black;
                    }
                    .group-arrow > div.expanded {
                        top: 7px;
                        left: 4px;
                        border-left: 5px solid transparent;
                        border-right: 5px solid transparent;
                        border-top: 6px solid black;
                    }

                    .group-child-option {
                        margin-left: 30px;
                    }

    .filter-dropdown .md-select-value {
        border-bottom: none !important;
    }

    .more-filters-button {
        margin-top: -6px;
        position: relative;
        height: 45px;
        padding-top: 17px;
        padding-left: 70px;
        background-color: white;
        color: #8e888e;
        cursor: pointer;
        user-select: none;
    }

        .more-filters-button.adjust-padding {
            padding-top: 15px;
        }

    .more-filters-icon {
        position: absolute;
        left: 25px;
        top: 11px;
        width: 24px;
        height: 24px;
        margin-right: 10px;
        opacity: 0.7;
    }

    .date-range-picker {
        height: 125%;
        width: 104% !important;
        opacity: 0;
        position: absolute;
        top: -9px;
        left: -12px;
    }

    .settings-button {
        margin-top: 27px !important;
    }

    .filter-desciption-container {
        display: flex;
        justify-content: space-between;
    }

    .current-filter-description {
        margin-left: 20px;
        margin-top: 20px;
        color: #7b7b7b;
        font-size: 12px;
    }

    .current-filter-description.add-margin {
        margin-top: 30px;
    }

        .filter-description-lightning {
            height: 16px;
            width: auto;
            margin: 0 0 -4px 2px;
            cursor: pointer;
            opacity: 0.6;
        }

    .clear-filters-text {
        margin-left: 4px;
    }

    .sort-container {
        margin-top: 44px;
        margin-right: 20px;
        color: black;
    }

    /* Table */
    .job-list-table.show-jobReference                  tr > .column-jobReference                  { display: table-cell !important; }
    .job-list-table.show-clientName                    tr > .column-clientName                    { display: table-cell !important; }
    .job-list-table.show-clientJobNumber               tr > .column-clientJobNumber               { display: table-cell !important; }
    .job-list-table.show-address                       tr > .column-address                       { display: table-cell !important; }
    .job-list-table.show-projectOwner                  tr > .column-projectOwner                  { display: table-cell !important; }
    .job-list-table.show-projectDescriptionDescription tr > .column-projectDescriptionDescription { display: table-cell !important; }
    .job-list-table.show-assessmentDesign              tr > .column-assessmentDesign              { display: table-cell !important; }
    .job-list-table.show-assessmentVersion             tr > .column-assessmentVersion             { display: table-cell !important; }
    .job-list-table.show-creatorFullName               tr > .column-creatorFullName               { display: table-cell !important; }
    .job-list-table.show-clientAssigneeFullName        tr > .column-clientAssigneeFullName        { display: table-cell !important; }
    .job-list-table.show-assessorFullName              tr > .column-assessorFullName              { display: table-cell !important; }
    .job-list-table.show-assessmentPriorityDescription tr > .column-assessmentPriorityDescription { display: table-cell !important; }
    .job-list-table.show-status                        tr > .column-status                        { display: table-cell !important; }
    .job-list-table.show-orderDate                     tr > .column-orderDate                     { display: table-cell !important; }
    .job-list-table.show-assessmentCerficateDate       tr > .column-assessmentCerficateDate       { display: table-cell !important; }
    .job-list-table.show-jobModifiedOn                 tr > .column-jobModifiedOn                 { display: table-cell !important; }

    .loading-disabled {
        cursor: default !important;
        opacity: 0.6;
    }

</style>