USE [thermarate];

SELECT [variationOption].[StandardHomeModelVariationOptionId]
      ,[variationOption].[StandardHomeModelId]
      ,[homeModel].[Title] [__HomeModel]
      ,[variationOption].[VariationCategoryCode]
      ,[variationOption].[OptionName]
      ,[variationOption].[SortOrder]
      ,[variationOption].[Deleted]
  FROM [dbo].[RSS_StandardHomeModelVariationOption] [variationOption]
  INNER JOIN [dbo].[RSS_StandardHomeModel] [homeModel] ON [variationOption].[StandardHomeModelId] = [homeModel].[StandardHomeModelId]
  WHERE 1=1
    --AND [variationOption].[Deleted] = 0
    --AND [homeModel].[StandardHomeModelId] = '00000000-0000-0000-0000-000000000000'
  ORDER BY [variationOption].[SortOrder]


SELECT COUNT(*) FROM [dbo].[RSS_StandardHomeModelOption];