USE [thermarate];

SELECT [default].[ClientDefaultId]
      ,[default].[SpaceHeatingServiceTypeCode]
      ,[default].[SpaceHeatingGems2019Rating]
      ,[default].[PreliminaryComplianceMethodCode]
      ,[default].[PerformanceSolutionComplianceOption]
      ,[default].[AssessmentSoftwareCode]
      ,[default].[MinHouseEnergyRating]
      ,[default].[BuildingExposureCode]
      ,[default].[CreatedOn]
      ,[default].[CreatedByName]
      ,[default].[ModifiedOn]
      ,[default].[ModifiedByName]
      ,[default].[Deleted]

      ,[default].[EmailAssessorNewJob]
      ,[default].[EmailAssessorRecertification]
      ,[default].[SummaryReport]
      ,[default].[EmailAssessorCopyAssessment]
      ,[default].[PurchaseOrderCode]
      ,[default].[NominatedBuildingSurveyorId]
      ,[default].[ProposedConstructionTemplateId]
      ,[default].[ReferenceConstructionTemplateId]
      ,[default].[BuildingZonesTemplateId]
      ,[default].[AssessorUserId]
      ,[default].[CertificationId]
      ,[default].[ProposedOpeningTemplateId]
      ,[default].[ReferenceOpeningTemplateId]
      ,[default].[ReferenceBuildingZonesTemplateId]
      ,[default].[ProposedServicesTemplateId]
      ,[default].[ReferenceServicesTemplateId]
      ,[default].[PriorityCode]
      ,[default].[WorksDescriptionCode]
      ,[default].[AssessorNotesNotApplicable]
      ,[default].[AssessorNotes]
      ,[default].[SpaceHeatingServiceTypeCode]
      ,[default].[SpaceHeatingGems2019Rating]
      ,[default].[SpaceCoolingServiceTypeCode]
      ,[default].[SpaceCoolingGems2019Rating]
      ,[default].[WaterHeatingServiceTypeCode]
      ,[default].[WaterHeatingGems2019Rating]
      ,[default].[SwimmingPoolExists]
      ,[default].[SwimmingPoolVolume]
      ,[default].[SwimmingPoolGems2019Rating]
      ,[default].[SpaExists]
      ,[default].[SpaVolume]
      ,[default].[SpaGems2019Rating]
      ,[default].[PhotovoltaicExists]
      ,[default].[PhotovoltaicCapacity]
      ,[default].[IncludeDrawingsInReport]
      ,[default].[StampDrawings]
  FROM [dbo].[RSS_ClientDefault] [default]
  INNER JOIN [dbo].[RSS_Client] [client] ON [default].[ClientDefaultId] = [client].[ClientDefaultId]
  WHERE 1=1
    AND [client].[ClientId] = 'c2d280bc-4274-c026-6c65-3a0b53d165e5'
  ORDER BY [CreatedOn] DESC