(function () {

    'use strict';

    let controllerId = 'constructionRenameModalController';

    angular.module('app')
        .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', '$stateParams', '$state', '$q',
            'bootstrap.dialog', 'uuid4',
            'constructionservice',
            'servicetemplateservice',
            'common',
            constructionRenameModalController]);

    function constructionRenameModalController($rootScope, $scope, $mdDialog, $stateParams, $state, $q,
                                      modalDialog, uuid4,
                                      constructionservice,
                                      servicetemplateservice,
                                      common) {

        // The model for this form
        const vm = this;

        vm.action = $scope.action;
        vm.type = $scope.type;
        vm.generalSectionDisplay = $scope.generalSectionDisplay;
        vm.parent = $scope.parent;
        vm.building = $scope.building;
        vm.option = $scope.option;
        vm.disabled = $scope.disabled;

        function initialize() {

            vm.data = {
                overrideDisplayDescription: vm.parent.overrideDisplayDescription || vm.parent.displayDescription || vm.parent.description,
                originalDescription: vm.parent.displayDescription || vm.parent.description,
                overrideInsulationDescription: vm.parent.overrideInsulationDescription || getOriginalInsulationDescription(),
                originalInsulation: getOriginalInsulationDescription(),
                chenathConstructionCode: getChenathConstructionCode(),
            }

            function getOriginalInsulationDescription() {
                // For surfaces, get insulation description from InsulationData
                if (vm.parent.insulationData && vm.parent.insulationData.description) {
                    return vm.parent.insulationData.description;
                }
                // For other types, return empty string or appropriate default
                return "";
            }

            function getChenathConstructionCode() {
                // Get the assessment software code from the option
                if (!vm.option || !vm.option.assessmentSoftware || !vm.option.assessmentSoftware.assessmentSoftwareCode) {
                    return null;
                }

                const softwareCode = vm.option.assessmentSoftware.assessmentSoftwareCode.toLowerCase();

                // Check if parent has ChenathData and ConstructionCodePerSoftware
                if (!vm.parent.chenathData ||
                    !vm.parent.chenathData.constructionCodePerSoftware ||
                    !vm.parent.chenathData.constructionCodePerSoftware[softwareCode]) {
                    return null;
                }

                return vm.parent.chenathData.constructionCodePerSoftware[softwareCode];
            }

            vm.substituteParent = null;
            vm.constructionTypes = ["All"];
            vm.selectedConstructionType = "All";

            if(vm.action === "rename") {
                const displayDescription = vm.parent.overrideDisplayDescription || vm.parent.displayDescription || vm.parent.description;
                vm.title = `Rename "${displayDescription}"`;
            }

            if(vm.action === "substitute" && vm.type === "construction") {

                vm.templateListInCategory = [];
                let constructionTemplatePromise = constructionservice.getAll()
                    .then(data => {
                        vm.templateListInCategory = data
                            .filter(x => x.constructionCategoryCode === vm.parent.category.constructionCategoryCode)

                        // Extract unique construction types
                        const uniqueTypes = [...new Set(vm.templateListInCategory.map(item => item.constructionSubCategoryTitle))];
                        // Sort the construction types alphabetically
                        const sortedTypes = uniqueTypes.filter(type => type).sort((a, b) => a.localeCompare(b));
                        vm.constructionTypes = ["All", ...sortedTypes]; // Filter out null/undefined values and sort alphabetically

                        // Sort favorited items to the top
                        vm.templateListInCategory.sort((a, b) => {
                            if (a.isFavourite && !b.isFavourite) return -1;
                            if (!a.isFavourite && b.isFavourite) return 1;
                            return 0;
                        });

                        vm.refinedTemplates = [...vm.templateListInCategory]
                    });

                vm.title = `Substitute "${vm.data.overrideDisplayDescription}"`

                let categoryTitle = "";

                if(vm.parent.category?.type === "glazing" || vm.parent.category.type === "glazing")
                    categoryTitle = "Opening"
                else if(vm.parent.category?.type === "surface")
                    categoryTitle = "Construction";

                vm.searchTitle = `Search ${categoryTitle} Database`;

            }

            if(vm.action === "substitute" && vm.type === "service") {

                vm.templateListInCategory = [];
                let constructionTemplatePromise = servicetemplateservice.getAll()
                    .then(data => {
                        vm.templateListInCategory = data
                            .filter(x => x.serviceCategory.serviceCategoryCode === vm.parent.serviceCategory.serviceCategoryCode)

                        // Extract unique construction types
                        const uniqueTypes = [...new Set(vm.templateListInCategory.map(item => item.constructionSubCategoryTitle))];
                        // Sort the construction types alphabetically
                        const sortedTypes = uniqueTypes.filter(type => type).sort((a, b) => a.localeCompare(b));
                        vm.constructionTypes = ["All", ...sortedTypes]; // Filter out null/undefined values and sort alphabetically

                        // Sort favorited items to the top
                        vm.templateListInCategory.sort((a, b) => {
                            if (a.isFavourite && !b.isFavourite) return -1;
                            if (!a.isFavourite && b.isFavourite) return 1;
                            return 0;
                        });

                        vm.refinedTemplates = [...vm.templateListInCategory]
                    });

                vm.title = `Substitute "${vm.data.overrideDisplayDescription}"`
                vm.searchTitle = `Search Services Database`;

            }

        }

        // Load construction categories to determine if current category is a construction category
        constructionservice.getConstructionCategoryList()
            .then(() => {
                vm.constructionCategories = constructionservice.constructionCategories();
            });

        // Check if the current category is a construction category (not an opening category)
        vm.isConstructionCategory = function() {
            if (!vm.constructionCategories || !vm.parent.category) {
                return false;
            }
            return vm.constructionCategories.some(cat =>
                cat.constructionCategoryCode === vm.parent.category.constructionCategoryCode
            );
        };

        initialize();

        vm.cancel = function () {
            $mdDialog.cancel();
        }

        vm.save = async function () {

            if(vm.action === "rename")
                $mdDialog.hide(vm.data);

            if(vm.action === "substitute")
                $mdDialog.hide(vm.selectedSubstitute);

        }

        vm.copyCodeToClipboard = async function(code) {

            if (!navigator.clipboard) {
                return;
            }

            await navigator.clipboard.writeText(code);
        }

        vm.restoreAll = function() {
            // Restore Description field
            vm.data.overrideDisplayDescription = vm.data.originalDescription;

            // Restore Insulation field (only for construction categories)
            if (vm.isConstructionCategory()) {
                vm.data.overrideInsulationDescription = vm.data.originalInsulation;
            }
        }
        vm.refineConstructionList = function() {

            common.debouncedThrottle('refineConstructionList', () => {
                // Apply search text filter
                let filteredList = [...vm.templateListInCategory];

                // Apply search text filter if provided
                var term = vm.templateSearchText ? vm.templateSearchText.trim() : "";
                if(term !== "") {
                    filteredList = filteredList.filter(x =>
                        x.description.toLowerCase().contains(term) ||
                        x.displayDescription.toLowerCase().contains(term) ||
                        x.overrideDisplayDescription?.toLowerCase().contains(term)
                    );
                }

                // Apply construction type filter if not "All"
                if (vm.selectedConstructionType && vm.selectedConstructionType !== "All") {
                    filteredList = filteredList.filter(x => x.constructionSubCategoryTitle === vm.selectedConstructionType);
                }

                vm.refinedTemplates = filteredList;

                // Maintain favorite sorting when filtering
                vm.refinedTemplates.sort((a, b) => {
                    if (a.isFavourite && !b.isFavourite) return -1;
                    if (!a.isFavourite && b.isFavourite) return 1;
                    return 0;
                });

            }, 300);

        }
    }
})();