<form class="main-content-wrapper"
      novalidate
      data-ng-controller='constructionSubstituteModalController as vm'
      style="min-width: 1512px; max-width: 1512px;">

    <div class="widget"
         ng-form="constructionSubstituteModalForm"
         ng-cloak>
        <div data-cc-widget-header
             data-title="{{vm.modalTitle}}"
             data-is-modal="true"
             data-cancel="vm.cancel()"
             data-back-button>
        </div>
        <div data-cc-widget-content
             data-is-modal="true">

            <!-- Construction Database Content -->
            <md-toolbar class="widget-actionbar-wrapper header-container {{vm.filtersExpanded ? 'header-container-expanded' : null}}" flex>
                <div class="md-toolbar-tools widget-actionbar header-inner-container" style="margin-bottom: 20px; font-size: 12px; background-color: rgb(255,255,255);">
                    <div class="filters-container" style="margin-top: 20px;">
                        <!-- Search -->
                        <div class="filter search-filter">
                            <div class="filter-label">Search</div>
                            <div class="search-input-container">
                                <input class="search-input"
                                       type="text"
                                       placeholder="Quick Filter"
                                       ng-model="vm.searchString"
                                       ng-keydown="$event.keyCode == 13 ? vm.refreshList() : null"
                                       ng-blur="vm.refreshList()">
                                <img src="/content/images/cross.png"
                                     class="search-clear-button"
                                     ng-show="vm.searchString"
                                     ng-click="vm.searchString = null; vm.refreshList(); $event.stopPropagation()">
                            </div>
                        </div>

                        <!-- Multi-Filters -->
                        <div ng-repeat="filter in vm.filters track by filter.field"
                             ng-if="filter.name != null"
                             ng-show="filter.section == 1 || vm.filtersExpanded"
                             class="filter {{vm.anyOptionsSelectedOnField(filter, vm.appliedFilters) ? 'options-selected' : ''}}">

                            <!-- Label -->
                            <div ng-if="filter.name != '[blank]' && filter.name != '[moreLessButton]'" class="filter-label">{{vm.keyToName(filter.name)}}</div>

                            <!-- Clear Button -->
                            <img src="/content/images/cross.png"
                                 class="filter-clear-button"
                                 ng-click="vm.clearFilter(filter);$event.stopPropagation()"
                                 ng-show="vm.anyOptionsSelectedOnField(filter, vm.appliedFilters)"
                            />

                            <!-- Normal Field -->
                            <md-select ng-if="filter.name != '[blank]' && filter.name != '[moreLessButton]' && !filter.isDate"
                                       class="filter-dropdown"
                                       multiple="true"
                                       md-selected-text="vm.getFilterSelectedText(filter, vm.filterOptions, vm.appliedFilters)"
                                       ng-model="vm.appliedFilters[filter.field]"
                                       ng-change="vm.refreshList()">

                                <!-- Group Option -->
                                <div ng-if="filter.groups != null"
                                     ng-repeat="group in filter.groups track by group.value">
                                    <md-option ng-value="group.value"
                                               ng-click="vm.selectOptionForGroup(filter, group, group)"
                                               class="group-option">
                                        {{group.name}} ({{vm.getGroupCount(filter, group)}})
                                    </md-option>
                                    <md-option ng-show="group.expanded && vm.getFilterCountItem(filter, item) > 0"
                                               ng-repeat="item in vm.getGroupOptions(filter, group) track by item.value"
                                               ng-value="item.value"
                                               ng-click="vm.selectOptionForGroup(filter, item, group)"
                                               class="group-child-option">
                                        {{vm.optionName(item, filter.isBool, filter.isDecimal)}} ({{vm.getFilterCountItem(filter, item)}})
                                    </md-option>
                                </div>

                                <!-- Normal Option -->
                                <md-option ng-if="filter.groups == null"
                                           ng-show="vm.getFilterCountItem(filter, item) > 0"
                                           ng-repeat="item in vm.filterOptions[filter.field] track by item.value"
                                           ng-value="item.value">
                                    {{vm.optionName(item, filter.isBool, filter.isDecimal)}} ({{vm.getFilterCountItem(filter, item)}})
                                </md-option>

                            </md-select>

                            <!-- Date Field -->
                            <md-select ng-if="filter.name != '[blank]' && filter.name != '[moreLessButton]' && filter.isDate"
                                        class="filter-dropdown"
                                        multiple="true"
                                        md-selected-text="vm.appliedFilters[filter.field].label"
                                        ng-model="vm.appliedFilters[filter.field]">
                            </md-select>
                            <div ng-if="filter.isDate"
                                    rd-date-range-picker
                                    class="date-range-picker"
                                    ng-model="vm.appliedFilters[filter.field]"
                                    format="MMMM D, YYYY"
                                    ranges="vm.ranges"
                                    on-change="vm.refreshList">
                            </div>

                            <!-- More/Less Button -->
                            <div ng-if="filter.name == '[moreLessButton]'" class="more-filters-button {{vm.filtersExpanded ? 'adjust-padding' : ''}}" ng-click="vm.filtersExpanded = !vm.filtersExpanded">
                                {{vm.filtersExpanded ? 'Less' : 'More'}}
                            </div>
                        </div>
                    </div>

                    <!-- Bottom section with filter description and toggle -->
                    <div class="header-bottom-section">
                        <!-- Number of Items -->
                        <div class="current-filter-description" ng-show="!vm.filtersApplied">
                            Showing {{vm.totalRecords}} constructions
                        </div>
                        <div class="current-filter-description" ng-show="vm.filtersApplied">
                            {{vm.totalFilteredConstructions}} of {{vm.totalConstructions}} constructions match your filters
                            (<u ng-click="vm.clearFilters()" style="text-decoration: underline; cursor:pointer;">clear filters</u>)
                        </div>

                        <!-- Show Display Description Toggle -->
                        <div class="show-display-desc-toggle">
                            <md-switch ng-model="vm.showDisplayDesc">Show Display Description</md-switch>
                        </div>
                    </div>
                </md-toolbar>

                <!-- Construction Database Table -->
                <div class="table-responsive-vertical shadow-z-1 table-container">
                    <table class="table table-striped table-hover table-condensed table-full-width"
                           st-table="vm.constructionList"
                           st-table-filtered-list="exportList"
                           st-global-search="vm.listFilter"
                           st-persist="constructionList"
                           st-pipe="vm.orderBy">
                        <thead>
                            <tr>
                                <!-- Glazing columns: Manufacturer, Window-ID, Description, Frame, Opening Style, Glass Type, Glass Colour, Low-E, U-Value, SHGC -->
                                <th ng-if="vm.isGlazing()" st-sort="manufacturerDescription" class="can-sort text-center">Manufacturer</th>
                                <th ng-if="vm.isGlazing()" st-sort="externalConstructionId" class="can-sort text-center">Window-ID</th>
                                <th ng-if="vm.isGlazing()" st-sort="description" class="can-sort text-left">Description</th>
                                <th ng-if="vm.isGlazing()" st-sort="frameMaterialTitle" class="can-sort text-center">Frame</th>
                                <th ng-if="vm.isGlazing()" st-sort="openingStyleTitle" class="can-sort text-center">Opening Style</th>
                                <th ng-if="vm.isGlazing()" st-sort="glassTypeTitle" class="can-sort text-center">Glass Type</th>
                                <th ng-if="vm.isGlazing()" st-sort="glassColourTitle" class="can-sort text-center">Glass Colour</th>
                                <th ng-if="vm.isGlazing()" st-sort="lowECoating" class="can-sort text-center">Low-E</th>
                                <th ng-if="vm.isGlazing()" st-sort="uValue" class="can-sort text-center">U-Value</th>
                                <th ng-if="vm.isGlazing()" st-sort="shgc" class="can-sort text-center">SHGC</th>

                                <!-- Non-glazing categories still show Description first -->
                                <th ng-if="!vm.isGlazing()" st-sort="description" class="can-sort text-left">Description</th>

                                <!-- Door columns: Description, Manufacturer, Insulation, Opening Style -->
                                <th ng-if="vm.isDoor()" st-sort="manufacturerDescription" class="can-sort text-center">Manufacturer</th>
                                <th ng-if="vm.isDoor()" st-sort="insulationDescription" class="can-sort text-center">Insulation</th>
                                <th ng-if="vm.isDoor()" st-sort="openingStyleTitle" class="can-sort text-center">Opening Style</th>

                                <!-- Opening columns: Description, Manufacturer, Opening Style -->
                                <th ng-if="vm.isOpening()" st-sort="manufacturerDescription" class="can-sort text-center">Manufacturer</th>
                                <th ng-if="vm.isOpening()" st-sort="openingStyleTitle" class="can-sort text-center">Opening Style</th>

                                <!-- Construction columns: Description, Construction Type, Insulation -->
                                <th ng-if="vm.isConstruction()" st-sort="constructionSubCategoryTitle" class="can-sort text-center">Construction Type</th>
                                <th ng-if="vm.isConstruction()" st-sort="insulationDescription" class="can-sort text-center">Insulation</th>

                                <th style="width: 50px;" ng-show="vm.actionButtons.length > 0 && vm.hasPermissionForAnyAction()"></th>
                            </tr>
                        </thead>

                        <tbody>
                            <tr ng-repeat="row in vm.constructionList track by row.constructionId"
                                class="list-row clickable"
                                ng-class="{'selected-row': vm.selectedConstruction && vm.selectedConstruction.constructionId === row.constructionId}"
                                ng-click="vm.selectConstruction(row)">

                                <!-- Glazing columns: Manufacturer, Window-ID, Description, Frame, Opening Style, Glass Type, Glass Colour, Low-E, U-Value, SHGC -->
                                <td ng-if="vm.isGlazing()" data-title="Manufacturer" class="text-center">{{row.manufacturerDescription}}</td>
                                <td ng-if="vm.isGlazing()" data-title="Window-ID" class="text-center">{{row.externalConstructionId}}</td>
                                <td ng-if="vm.isGlazing()" data-title="Description">
                                    <div style="width: 100%; padding-left: 10px; padding-right: 10px; box-sizing: border-box; text-align: left;">
                                        {{vm.showDisplayDesc ? row.displayDescription : row.description }}
                                    </div>
                                </td>
                                <td ng-if="vm.isGlazing()" data-title="Frame" class="text-center">{{row.frameMaterialTitle}}</td>
                                <td ng-if="vm.isGlazing()" data-title="Opening Style" class="text-center">{{row.openingStyleTitle}}</td>
                                <td ng-if="vm.isGlazing()" data-title="Glass Type" class="text-center">{{row.glassTypeTitle}}</td>
                                <td ng-if="vm.isGlazing()" data-title="Glass Colour" class="text-center">{{row.glassColourTitle}}</td>
                                <td ng-if="vm.isGlazing()" data-title="Low-E" class="text-center">{{row.lowECoating}}</td>
                                <td ng-if="vm.isGlazing()" data-title="U-Value" class="text-center">{{row.uValue}}</td>
                                <td ng-if="vm.isGlazing()" data-title="SHGC" class="text-center">{{row.shgc}}</td>

                                <!-- Non-glazing categories still show Description first -->
                                <td ng-if="!vm.isGlazing()" data-title="Description">
                                    <div style="width: 100%; padding-left: 10px; padding-right: 10px; box-sizing: border-box; text-align: left;">
                                        {{vm.showDisplayDesc ? row.displayDescription : row.description }}
                                    </div>
                                </td>

                                <!-- Door columns: Description, Manufacturer, Insulation, Opening Style -->
                                <td ng-if="vm.isDoor()" data-title="Manufacturer" class="text-center">{{row.manufacturerDescription}}</td>
                                <td ng-if="vm.isDoor()" data-title="Insulation" class="text-center">{{row.insulationDescription}}</td>
                                <td ng-if="vm.isDoor()" data-title="Opening Style" class="text-center">{{row.openingStyleTitle}}</td>

                                <!-- Opening columns: Description, Manufacturer, Opening Style -->
                                <td ng-if="vm.isOpening()" data-title="Manufacturer" class="text-center">{{row.manufacturerDescription}}</td>
                                <td ng-if="vm.isOpening()" data-title="Opening Style" class="text-center">{{row.openingStyleTitle}}</td>

                                <!-- Construction columns: Description, Construction Type, Insulation -->
                                <td ng-if="vm.isConstruction()" data-title="Construction Type" class="text-center">{{row.constructionSubCategoryTitle}}</td>
                                <td ng-if="vm.isConstruction()" data-title="Insulation" class="text-center">{{row.insulationDescription}}</td>
                                <td ng-show="vm.actionButtons.length > 0 && vm.hasPermissionForAnyAction()">
                                    <div class="three-dot-menu-container">
                                        <md-menu md-position-mode="target-right target" md-offset="0 0">
                                            <md-button aria-label="Open menu" class="md-icon-button three-dot-menu-button" ng-click="$mdMenu.open($event)">
                                                <md-icon md-menu-origin class="material-icons">more_vert</md-icon>
                                            </md-button>
                                            <md-menu-content width="4">
                                                <md-menu-item ng-repeat="button in vm.actionButtons track by $index"
                                                              ng-show="button.showCondition == null || button.showCondition(row)"
                                                              redi-allow-roles="button.allowedRoles">
                                                    <md-button ng-click="button.action(row)">
                                                        <md-icon md-menu-align-target class="material-icons">{{button.icon}}</md-icon>
                                                        {{button.title}}
                                                    </md-button>
                                                </md-menu-item>
                                            </md-menu-content>
                                        </md-menu>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div ng-show="vm.showingToCnt < vm.totalFilteredConstructions"
                         style="width:max-content; margin:auto; margin-top: -10px; margin-bottom: 20px;"
                         class="clickable"
                         ng-click="vm.getMoreResults()">
                        <span ng-show="!vm.showMoreBusy">Show more</span>
                        <i ng-show="vm.showMoreBusy" class="fa fa-spinner fa-spin"></i>
                    </div>
                    <div class="widget-pager" style="text-align: center;">
                        <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
                    </div>
                </div>

        </div>

        <!-- Action Buttons -->
        <div data-cc-widget-button-bar
             data-is-modal="true"
             style="height:70px;">

            <md-button class="md-raised md-primary"
                       ng-disabled="!vm.selectedConstruction"
                       ng-click="vm.save()">
                Save
            </md-button>
            <md-button class="md-raised"
                       ng-click="vm.cancel()">
                Cancel
            </md-button>
            <div class="clearfix"></div>
        </div>

    </div>

</form>

<style>
    /* Multi-filter styles - display flow in a row with content-fitting filters */
    .filters-container {
        flex: 10;
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        align-items: flex-start;
    }

    .filter {
        position: relative;
        margin-top: 32px; /* To account for heading above that is positioned absolute */
        margin-right: 45px;
        font-size: 1.2rem !important;
        flex: 0 0 auto; /* Don't grow or shrink, size to content */
        min-width: 150px !important;
        width: max-content; /* Size to content */
    }

    .filter.search-filter {
        margin-right: 20px;
    }

    .search-input-container {
        position: relative;
        width: 100%;
        min-width: max-content;
    }

    .search-input {
        width: 100%;
        min-width: max-content;
        height: 45px;
        margin-top: -6px;
        padding: 7px 15px 6px 10px; /* Increased right padding to make room for clear button */
        box-sizing: border-box;
        border: none;
        background-color: #e2e2e2;
        color: black !important;
    }

    .search-input:focus-visible {
        border: none !important;
    }

    .search-input::placeholder {
        color: black !important;
    }

    .search-input:-ms-input-placeholder {
        color: black !important;
    }

    /* Ensure search input text has proper margin from clear button */
    .search-input {
        text-indent: 0;
        padding-right: 30px; /* Space for clear button */
    }

    .search-clear-button {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 16px;
        height: 16px;
        padding: 4px;
        border-radius: 50%;
        cursor: pointer;
    }

    .search-clear-button:hover {
        background-color: #eeeeee;
    }

    .filter-label {
        position: absolute;
        top: -32px;
        left: 15px;
        font-size: 12px;
        color: #8e888e;
    }

    .filter-dropdown {
        margin: -6px 0 0 2px;
        width: 100%;
        min-width: max(150px, max-content); /* At least 150px, but expand to fit content if larger */
        height: 45px;
        padding: 1px 13px 0px 10px; /* Increased right padding to make room for dropdown arrow */
        box-sizing: content-box;
        background-color: #e2e2e2;
        color: black !important;
    }

    .filter-dropdown span {
        margin-top: -2px;
    }

    .filter-dropdown .md-select-placeholder {
        color: black !important;
    }

    .filter-dropdown .md-select-value {
        border-bottom: none !important;
        white-space: nowrap; /* Prevent text wrapping */
        overflow: visible; /* Allow text to show */
        text-overflow: unset; /* Don't truncate text */
    }

    /* Ensure dropdown text doesn't get truncated */
    .filter-dropdown .md-select-value .md-text {
        white-space: nowrap;
        overflow: visible;
        text-overflow: unset;
        margin-right: 20px; /* Add right margin to text content */
    }

    /* Ensure dropdown arrow stays on the right */
    .filter-dropdown .md-select-icon {
        position: absolute;
        right: 4px;
        top: 49%;
        transform: translateY(-50%);
    }

    .group-option {
        margin-left: 20px;
    }

    .group-child-option {
        margin-left: 40px;
    }

    .date-range-picker {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 45px;
        margin-top: -6px;
        padding: 7px 10px 6px 10px;
        box-sizing: border-box;
        background-color: #e2e2e2;
        color: black !important;
        border: none;
        z-index: 1;
    }

    .filter-clear-button {
        display: none;
        position: absolute;
        right: -20px;
        bottom: 14px;
        z-index: 50;
        width: 16px;
        height: auto;
        padding: 4px;
        border-radius: 50%;
        cursor: pointer;
    }

    .filter-clear-button:hover {
        background-color: #eeeeee;
    }

    .filter.options-selected > .filter-clear-button {
        display: inherit !important;
    }

    .filter.options-selected .md-select-icon {
        right: 4px;
        top: 49%;
    }

    .current-filter-description {
        color: #7b7b7b;
        font-size: 12px;
        flex-grow: 1;
    }

    /* More/Less Filters button styles - copied exactly from Construction Database page */
    .more-filters-button {
        margin-top: -6px;
        position: relative;
        height: 45px;
        padding-top: 17px;
        padding-left: 70px;
        background-color: white;
        color: #8e888e;
        cursor: pointer;
        user-select: none;
    }

    .more-filters-button.adjust-padding {
        padding-top: 15px;
    }

    .more-filters-button:hover {
        background-color: #f5f5f5;
    }

    .list-row {
        height: 52px;
    }

    /* Selected row highlighting - forced with high specificity */
    .table-striped tbody tr.selected-row,
    .table-striped tbody tr.selected-row:hover,
    .table-hover tbody tr.selected-row:hover,
    tbody tr.selected-row,
    tbody tr.selected-row:hover,
    tr.selected-row,
    tr.selected-row:hover {
        background-color: #bfdaa1 !important;
    }

    .selected-row td,
    .selected-row:hover td {
        background-color: #bfdaa1 !important;
    }

    .header-container {
        min-height: 150px !important;
        justify-content: center;
    }

    .header-container-expanded {
        min-height: 226px !important;
    }

    .header-inner-container {
        gap: 1%;
        max-height: max-content;
        height: max-content;
        align-items: flex-start;
        flex-direction: column;
    }

    .three-dot-menu-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
    }

    .three-dot-menu-button {
        margin: 0;
        min-width: 36px;
        width: 36px;
        height: 36px;
    }

    /* Header bottom section with space-between layout */
    .header-bottom-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 0 20px;
    }

    /* Show Display Description Toggle styling */
    .show-display-desc-toggle {
        font-size: 12px;
        flex-shrink: 0;
    }

    /* Table width expansion */
    .table-container {
        width: 100%;
        padding-bottom: 15px;
    }

    .table-full-width {
        width: 100%;
        min-width: 100%;
    }

    /* Height transition animation to match Orientate modal */
    md-dialog {
        transition: height 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    }

    md-dialog.ng-enter {
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        transform: scale(0.7);
        opacity: 0;
    }

    md-dialog.ng-enter.ng-enter-active {
        transform: scale(1);
        opacity: 1;
    }

    md-dialog.ng-leave {
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        transform: scale(1);
        opacity: 1;
    }

    md-dialog.ng-leave.ng-leave-active {
        transform: scale(0.7);
        opacity: 0;
    }
</style>
