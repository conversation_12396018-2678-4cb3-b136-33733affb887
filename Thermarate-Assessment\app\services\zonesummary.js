(function () {
    'use strict';
    var serviceId = 'zonesummaryservice';
    angular.module('appservices').factory(serviceId,
        ['$rootScope', 'common', 'config', '$http', 'uuid4', 'zoneservice', 'projectdescriptionservice', zonesummaryservice]);

    function zonesummaryservice($rootScope, common, config, $http, uuid4, zoneservice, projectdescriptionservice) {

        // This is just an array we can loop over to key in on the buildingSummaryGroups below...
        // Unfortunately the logic for each "Group" is custom so we can't generically
        // loop over it...
        const knownBuildingSummaryGroups = [
            'interiorZones',
            'zoneActivity',
            'habitable', // aka 'zone type'
            'conditioned',
            'nccClassification',
        ];

        // Shading options
        const SHADING_OPTIONS = [
            { title: 'All',          code: 'ALL' },
            { title: 'No Shading',   code: 'No Shading' },
            { title: 'Minimal',      code: 'Minimal' },
            { title: 'Moderate',     code: 'Moderate' },
            { title: 'High',         code: 'High' },
            { title: 'Obstructive',  code: 'Obstructive' }
        ];

        const ALL_ZONES_OPTIONS = [
            "All Zones"
        ];

        const GROUP_OPTIONS = [
            "All Zones",
            "Zone Name",
            "Zone Activity",
            "Zone Type",
            "Conditioning",
            "NCC Classification",
        ];

        const CONDITIONED_GROUP_OPTIONS = [
            "All Conditioning Types",
            "All Conditioned Zones",
            "All Unconditioned Zones",
        ];

        // Filters
        const DEFAULT_FILTERS = [{
            id: uuid4.generate(),
            storey: 'ALL',
            group: 'All Zones',
            selection: ['All Zones'],
            // All options selected by default
            wallHorizontalShading: {
                selectableArray: [...SHADING_OPTIONS],
                selection: [SHADING_OPTIONS[0]]
            },
            glazingHorizontalShading: {
                selectableArray: [...SHADING_OPTIONS],
                selection: [SHADING_OPTIONS[0]]
            },
            wallVerticalShading: {
                selectableArray: [...SHADING_OPTIONS],
                selection: [SHADING_OPTIONS[0]]
            },
            glazingVerticalShading: {
                selectableArray: [...SHADING_OPTIONS],
                selection: [SHADING_OPTIONS[0]]
            }
        }];
        const DEFAULT_FILTERS_CONDITIONED = [{
            id: uuid4.generate(),
            storey: 'ALL',
            group: 'Conditioning',
            selection: ['All Conditioned Zones'],
            selectableArray: [
                "All Conditioning Types",
                "All Conditioned Zones",
                "All Unconditioned Zones"
            ],
            // All options selected by default
            wallHorizontalShading: {
                selectableArray: [...SHADING_OPTIONS],
                selection: [...SHADING_OPTIONS]
            },
            glazingHorizontalShading: {
                selectableArray: [...SHADING_OPTIONS],
                selection: [...SHADING_OPTIONS]
            },
            wallVerticalShading: {
                selectableArray: [...SHADING_OPTIONS],
                selection: [...SHADING_OPTIONS]
            },
            glazingVerticalShading: {
                selectableArray: [...SHADING_OPTIONS],
                selection: [...SHADING_OPTIONS]
            }
        }];
        const DEFAULT_FILTERS_HABITABLE = [{
            id: uuid4.generate(),
            storey: 'ALL',
            group: 'Zone Type',
            selection: [{
                "title": "Habitable Room",
                "code": "ZTHabitableRoom"
            }],
            selectableArray: [
                {
                    "title": "All Zones Types",
                    "code": "ALL",
                    "$$mdSelectId": 26
                },
                {
                    "title": "Interconnecting Space",
                    "code": "ZTInterconnecting",
                    "$$mdSelectId": 27
                },
                {
                    "title": "Habitable Room",
                    "code": "ZTHabitableRoom",
                    "$$mdSelectId": 28
                },
                {
                    "title": "Non-Habitable Room",
                    "code": "ZTNonHabitableRoom",
                    "$$mdSelectId": 29
                }
            ],
            // All options selected by default
            wallHorizontalShading: {
                selectableArray: [...SHADING_OPTIONS],
                selection: [...SHADING_OPTIONS]
            },
            glazingHorizontalShading: {
                selectableArray: [...SHADING_OPTIONS],
                selection: [...SHADING_OPTIONS]
            },
            wallVerticalShading: {
                selectableArray: [...SHADING_OPTIONS],
                selection: [...SHADING_OPTIONS]
            },
            glazingVerticalShading: {
                selectableArray: [...SHADING_OPTIONS],
                selection: [...SHADING_OPTIONS]
            }
        }];





        // Envelope Summary Data
        function calculateEnvelopeSummaryData(building, sectorDetermination, filters) {
            let envelopeData = {};
            // Set for front end to loop over then get data via key (unique).
            envelopeData.sectorKeys = [... new Set(sectorDetermination?.sectors.map(sd => sd.label.toLowerCase())), 'total'];
            envelopeData.exteriorWallAreaTotalsPerSector = zoneservice.calcPropertyTotalPerSector(building, 'surface', 'ExteriorWall', sectorDetermination, filters);
            envelopeData.exteriorGlazingAreaTotalsPerSector = zoneservice.calcPropertyTotalPerSector(building, 'opening', 'ExteriorGlazing', sectorDetermination, filters);
            envelopeData.glassExteriorWallRatioPerSector = zoneservice.calcGlassExteriorWallRatioPerSector(envelopeData.exteriorWallAreaTotalsPerSector, envelopeData.exteriorGlazingAreaTotalsPerSector, sectorDetermination);
            let parents = building.openings.filter(o => o.category.constructionCategoryCode == "ExteriorGlazing");
            let rows = zoneservice.generateConstructionRowsFromElements(building, parents, filters);
            envelopeData.averageGlazingUValuePerSector = rows.length == 0 ? zoneservice.defaultSectorsData : zoneservice.calcAverageGlazingUValuePerSector(rows, sectorDetermination);
            envelopeData.averageGlazingSHGCPerSector = rows.length == 0 ? zoneservice.defaultSectorsData : zoneservice.calcAverageGlazingSHGCPerSector(rows, sectorDetermination);
            return envelopeData;
        }


        // Zone Summary
        /**
         * Loops over the supplied zones and breaks them up into building summary groups with calculations
         * pre-baked into our groups to allow for easy looping over within html side.
         *
         * @param {any} zones
         * @param {any} storeys
         */
        function constructBuildingSummaryGroups(building, storeys, zoneActivityList, zoneTypeList, nccClassificationList) {

            if (building.zones == null)
                return;

            const buildingSummaryGroups = {
                interiorZones: { groupName: 'Zone Name', rows: [], descriptionHeading: "Zone Name" },
                zoneActivity: { groupName: 'Zone Activity', rows: [] },
                nccClassification: { groupName: 'NCC Classification', rows: [] },
                habitable: { groupName: 'Zone Type', rows: [] },
                conditioned: { groupName: 'Conditioning', rows: [] },
            };
            const finalResult = {};

            // Create Interior and Exterior Zone group summaries
            const interiorZones = zoneservice.interiorZones(building.zones);

            // Get required constructions
            const allExtWallConstructions = building.surfaces
                .filter(x => x.category.constructionCategoryCode === "ExteriorWall")
                .map(x => x.elements)
                .reduce((a, b) => [...a, ...b], []);

            const allExtGlazings = building.openings
                .filter(x => x.category.constructionCategoryCode === "ExteriorGlazing")
                .map(x => x.elements)
                .reduce((a, b) => [...a, ...b], []);

            // const totalExteriorWallArea = allExtWallConstructions
            //     .map(x => x.grossArea)
            //     .reduce((a, b) => a + b, 0);

            createInteriorZoneBuildingGroupSummary(
                storeys,
                interiorZones,
                zoneActivityList,
                zoneTypeList,
                nccClassificationList,
                buildingSummaryGroups.interiorZones?.rows,
                allExtWallConstructions,
                allExtGlazings,
                'All Zones');

            buildingSummaryGroups.zoneActivity = createGenericBuildingGroupSummary(
                storeys,
                interiorZones,
                zoneActivityList,
                zoneTypeList,
                nccClassificationList,
                "Zone Activity",
                "Zone Activity",
                zoneActivityList,
                "zoneActivity",
                "zoneActivityCode",
                "description");

            buildingSummaryGroups.habitable = createGenericBuildingGroupSummary(
                storeys,
                interiorZones,
                zoneActivityList,
                zoneTypeList,
                nccClassificationList,
                "Zone Type",
                "Habitable",
                zoneTypeList,
                "zoneType",
                "zoneTypeCode",
                "description");

            buildingSummaryGroups.nccClassification = createGenericBuildingGroupSummary(
                storeys,
                interiorZones,
                zoneActivityList,
                zoneTypeList,
                nccClassificationList,
                "NCC Classification",
                "NCC Classification",
                nccClassificationList,
                "nccClassification",
                "nccClassificationCode",
                "description");

            buildingSummaryGroups.conditioned = createConditionedBuildingSummaryGroup(zoneActivityList, zoneTypeList, nccClassificationList, storeys, building.zones);

            // We need to smartly merge in new changes with the old vm.buildingSummaryGroup object or
            // we get weird UI things happening that affect the entire page (Paul said he has encountered
            // this on other projects due to the way in which we have a custom calculation cycle here).
            // Anyway so this is a bit of a 'slack' way to do things but essentially the main problem
            // we seem to have with just using angular.merge is it won't REMOVE objects from an array if
            // they don't exist in the array you're merging in, which is a problem for us since storeys and
            // zones can be added and changed around and so on.
            // The very simple fix for this in our case is to simply check if the size of our requirements
            // array (AND the ROWS within the requirements array) are identical in length, and if they are BIGGER or EQUAL
            // in the incoming array, that's ok. But if any are SMALLER, then instead we blow the whole object away.
            knownBuildingSummaryGroups.forEach(summaryGroup => {

                let sameOrSmaller = true; // By default we assume the requirements array is the same size or smaller (i.e. OK to angular.merge)

                if (finalResult[summaryGroup] == null ||
                    (finalResult[summaryGroup].rows?.length > buildingSummaryGroups[summaryGroup].rows?.length))
                    sameOrSmaller = false; // Ok, need to blow away old value...

                if (finalResult[summaryGroup] != null) {
                    let oldZoneCount = finalResult[summaryGroup].rows?.map(x => x.zones.length)?.reduce((a, b) => a + b, 0);
                    let newZoneCount = buildingSummaryGroups[summaryGroup].rows?.map(x => x.zones.length)?.reduce((a, b) => a + b, 0);

                    if (oldZoneCount > newZoneCount)
                        sameOrSmaller = false;
                }

                if (sameOrSmaller)
                    angular.merge(finalResult[summaryGroup], buildingSummaryGroups[summaryGroup]);
                else {
                    finalResult[summaryGroup] = buildingSummaryGroups[summaryGroup]; // Blow entire thing away - will cause UI problem (only noticeable if an element is open)
                }
            });
            return finalResult;
        }

        /**
         * Create all individual zone rows as well as final "Building Total" row.
         */
        function createInteriorZoneBuildingGroupSummary(storeys, zones, zoneActivityList, zoneTypeList, nccClassificationList, addTo, allExteriorWalls, allExteriorGlazings, title) {

            // TODO: 1 loop over all 4
            const zonesTotalFloorArea = zones.map(x => x.floorArea).reduce((a, b) => a + b, 0);
            const zonesTotalVolume = zones.map(x => x.volume).reduce((a, b) => a + b, 0);
            const zonesTotalExteriorWallArea = zones.map(x => x.exteriorWallArea).reduce((a, b) => a + b, 0);
            const zonesTotalGlazingArea = zones.map(x => x.exteriorGlazingArea).reduce((a, b) => a + b, 0);

            storeys?.forEach(storey => {

                let storeyGroup = { name: storey.name, zones: [] }
                const zonesInStorey = getZonesInStorey(storey.floor, zones);
                zonesInStorey.forEach(z => {
                    storeyGroup.zones.push(createZoneRow(
                        z,
                        zonesTotalFloorArea,
                        zonesTotalVolume,
                        zonesTotalExteriorWallArea,
                        zonesTotalGlazingArea));
                });

                // TODO: 1 loop over all 4
                // Add zones total row for storey
                const storeyFloorArea = zonesInStorey.map(x => x.floorArea).reduce((a, b) => a + b, 0);
                const storeyVolume = zonesInStorey.map(x => x.volume).reduce((a, b) => a + b, 0);

                const storeyWallArea = zonesInStorey.map(x => x.exteriorWallArea).reduce((a, b) => a + b, 0);
                const storeyGlazingArea = zonesInStorey.map(x => x.exteriorGlazingArea).reduce((a, b) => a + b, 0);

                storeyGroup.zones.push(createSumRowForZones(
                    'All Zones',
                    zonesInStorey,
                    zoneActivityList,
                    zoneTypeList,
                    nccClassificationList,
                    storeyFloorArea,
                    storeyVolume,
                    storeyWallArea,
                    storeyGlazingArea
                ));

                if (storeyGroup.zones.length > 0)
                    addTo.push(storeyGroup);
            });

            addTo.push({
                name: 'Whole Building',
                zoneCount: zones.length,
                zones: [createSumRowForZones(
                    title,
                    zones,
                    zoneActivityList,
                    zoneTypeList,
                    nccClassificationList,
                    zonesTotalFloorArea,
                    zonesTotalVolume,
                    zonesTotalExteriorWallArea,
                    zonesTotalGlazingArea
                )]});
        }

        function createGenericBuildingGroupSummary(
                storeys,
                zones,
                zoneActivityList,
                zoneTypeList,
                nccClassificationList,
                groupName,
                descriptionHeading,
                matchList,
                matchCriteriaA,
                matchCriteriaB,
                descriptionCriteria) {

            const summaryGroup = {
                rows: [],
                groupName: groupName,
                descriptionHeading: descriptionHeading
            };

            const zonesTotalFloorArea = zones.map(x => x.floorArea).reduce((a, b) => a + b, 0);
            const zonesTotalVolume = zones.map(x => x.volume).reduce((a, b) => a + b, 0);
            const zonesTotalExteriorWallArea = zones.map(x => x.exteriorWallArea).reduce((a, b) => a + b, 0);
            const zonesTotalExteriorGlazingArea = zones.map(x => x.exteriorGlazingArea).reduce((a, b) => a + b, 0);

            let totalZoneCount = 0;

            storeys?.forEach(storey => {

                let storeyGroup = { name: storey.name, zones: [] }
                const zonesInStorey = getZonesInStorey(storey.floor, zones);

                matchList.forEach(match => {

                    const matchingZones = zonesInStorey.filter(z => common.resolve(`${matchCriteriaA}.${matchCriteriaB}`, z) === common.resolve(matchCriteriaB, match));

                    // We only add the summary zone, not 1 per zone found.
                    if (matchingZones.length > 0) {

                        const floorArea = matchingZones.map(x => x.floorArea).reduce((a, b) => a + b, 0);
                        const volume = matchingZones.map(x => x.volume).reduce((a, b) => a + b, 0);
                        const exteriorWallArea = matchingZones.map(x => x.exteriorWallArea).reduce((a, b) => a + b, 0);
                        const exteriorGlazingArea = matchingZones.map(x => x.exteriorGlazingArea).reduce((a, b) => a + b, 0);
                        const lampPowerMaximumW = matchingZones.map(x => x.lampPowerMaximumW).reduce((a, b) => a + b, 0);

                        let exteriorWallAreaPercent = (exteriorWallArea / zonesTotalExteriorWallArea) * 100;
                        if (Number.isNaN(exteriorWallAreaPercent))
                            exteriorWallAreaPercent = 0;

                        let exteriorGlazingAreaPercent = (exteriorGlazingArea / zonesTotalExteriorGlazingArea) * 100;
                        if (Number.isNaN(exteriorGlazingAreaPercent))
                            exteriorGlazingAreaPercent = 0;

                        let glassExteriorWallAreaPercent = (exteriorGlazingArea / exteriorWallArea) * 100;
                        if (Number.isNaN(glassExteriorWallAreaPercent))
                            glassExteriorWallAreaPercent = 0;

                        let glassFloorAreaPercent = (exteriorGlazingArea / floorArea) * 100;
                        if (Number.isNaN(glassFloorAreaPercent))
                            glassFloorAreaPercent = 0;

                        totalZoneCount += matchingZones.length;
                        storeyGroup.zones.push({
                            description: common.resolve(`${matchCriteriaA}.${descriptionCriteria}`, matchingZones[0]),
                            zoneCount: matchingZones.length,
                            floorArea: floorArea,
                            volume: volume,
                            floorAreaPercent: ((floorArea / zonesTotalFloorArea) * 100),
                            volumePercent: (volume / zonesTotalVolume) * 100,
                            exteriorWallArea,
                            exteriorWallAreaPercent,
                            exteriorGlazingArea,
                            exteriorGlazingAreaPercent,
                            glassExteriorWallAreaPercent,
                            glassFloorAreaPercent,
                            lampPowerMaximumW
                        });
                    }
                });

                // Only add storey group if it featured any zones.
                if (storeyGroup.zones.length > 0) {

                    let floorArea = 0;
                    let volume = 0;
                    let exteriorWallArea = 0;
                    let exteriorWallAreaPercent = 0;
                    let exteriorGlazingArea = 0;
                    let exteriorGlazingAreaPercent = 0;
                    let lampPowerMaximumW = 0;

                    for(let i = 0; i < storeyGroup.zones.length; i++) {

                        const zone = storeyGroup.zones[i];

                        floorArea += zone.floorArea;
                        volume += zone.volume;
                        exteriorWallArea += zone.exteriorWallArea;
                        exteriorWallAreaPercent += zone.exteriorWallAreaPercent;
                        exteriorGlazingArea += zone.exteriorGlazingArea;
                        exteriorGlazingAreaPercent += zone.exteriorGlazingAreaPercent;
                        lampPowerMaximumW += zone.lampPowerMaximumW;
                    }

                    if (Number.isNaN(exteriorWallAreaPercent))
                        exteriorWallAreaPercent = 0;

                    if (Number.isNaN(exteriorGlazingAreaPercent))
                        exteriorGlazingAreaPercent = 0;

                    let glassExteriorWallAreaPercent = (exteriorGlazingArea / exteriorWallArea) * 100;
                    if (Number.isNaN(glassExteriorWallAreaPercent))
                        glassExteriorWallAreaPercent = 0;

                    let glassFloorAreaPercent = (exteriorGlazingArea / floorArea) * 100;
                    if (Number.isNaN(glassFloorAreaPercent))
                        glassFloorAreaPercent = 0;

                    storeyGroup.zones.push({
                        description: "All Zones",
                        zoneCount: storeyGroup.zones.map(x => x.zoneCount).reduce((a, b) => a + b),
                        floorArea: floorArea,
                        volume: volume,
                        floorAreaPercent: ((floorArea / zonesTotalFloorArea) * 100),
                        volumePercent: (volume / zonesTotalVolume) * 100,
                        exteriorWallArea,
                        exteriorWallAreaPercent,
                        exteriorGlazingArea,
                        exteriorGlazingAreaPercent,
                        glassExteriorWallAreaPercent,
                        glassFloorAreaPercent,
                        lampPowerMaximumW
                    });
                    summaryGroup.rows.push(storeyGroup);
                }
            });

            // Only whole building row if storeys exist.
            if (summaryGroup.rows.length > 0) {

                let wholeBuildingGroup = { name: "Whole Building", zones: [] };

                matchList.forEach(match => {

                    const matchingZones = zones
                        .filter(z => common.resolve(`${matchCriteriaA}.${matchCriteriaB}`, z) === common.resolve(matchCriteriaB, match));

                    // We only add the summary zone, not 1 per zone found.
                    if (matchingZones.length > 0) {

                        const floorArea = matchingZones.map(x => x.floorArea).reduce((a, b) => a + b);
                        const volume = matchingZones.map(x => x.volume).reduce((a, b) => a + b);

                        const exteriorWallArea = matchingZones.map(x => x.exteriorWallArea).reduce((a, b) => a + b, 0);
                        const exteriorGlazingArea = matchingZones.map(x => x.exteriorGlazingArea).reduce((a, b) => a + b, 0);
                        const lampPowerMaximumW = matchingZones.map(x => x.lampPowerMaximumW).reduce((a, b) => a + b, 0);

                        let exteriorWallAreaPercent = (exteriorWallArea / zonesTotalExteriorWallArea) * 100;
                        if (Number.isNaN(exteriorWallAreaPercent))
                            exteriorWallAreaPercent = 0;

                        let exteriorGlazingAreaPercent = (exteriorGlazingArea / zonesTotalExteriorGlazingArea) * 100;
                        if (Number.isNaN(exteriorGlazingAreaPercent))
                            exteriorGlazingAreaPercent = 0;

                        let glassExteriorWallAreaPercent = (exteriorGlazingArea / exteriorWallArea) * 100;
                        if (Number.isNaN(glassExteriorWallAreaPercent))
                            glassExteriorWallAreaPercent = 0;

                        let glassFloorAreaPercent = (exteriorGlazingArea / floorArea) * 100;
                        if (Number.isNaN(glassFloorAreaPercent))
                            glassFloorAreaPercent = 0;

                        totalZoneCount += matchingZones.length;

                        wholeBuildingGroup.zones.push({
                            description: common.resolve(`${matchCriteriaA}.${descriptionCriteria}`, matchingZones[0]),
                            zoneCount: matchingZones.length,
                            floorArea: floorArea,
                            volume: volume,
                            floorAreaPercent: ((floorArea / zonesTotalFloorArea) * 100),
                            volumePercent: (volume / zonesTotalVolume) * 100,
                            exteriorWallArea,
                            exteriorWallAreaPercent,
                            exteriorGlazingArea,
                            exteriorGlazingAreaPercent,
                            glassExteriorWallAreaPercent,
                            glassFloorAreaPercent,
                            lampPowerMaximumW
                        });
                    }
                });

                if (wholeBuildingGroup.zones.length > 0) {

                    wholeBuildingGroup.zones.push(createSumRowForZones(
                        "All Zones",
                        zones,
                        zoneActivityList,
                        zoneTypeList,
                        nccClassificationList,
                        zonesTotalFloorArea,
                        zonesTotalVolume,
                        zonesTotalExteriorWallArea,
                        zonesTotalExteriorGlazingArea
                    ));

                    summaryGroup.rows.push(wholeBuildingGroup);

                }
            }

            return summaryGroup;
        }

        function createConditionedBuildingSummaryGroup(zoneActivityList, zoneTypeList, nccClassificationList, storeys, zones) {

            const summaryGroup = {
                rows: [],
                groupName: "Conditioning",
                descriptionHeading: "Conditioning"
            };

            storeys?.forEach(storey => {

                let storeyGroup = { name: storey.name, zones: [] }

                const zonesInStorey = getZonesInStorey(storey.floor, zones);

                // Create "Conditioned" group.
                const conditionedZones = zoneservice.conditionedZones(zonesInStorey);
                const unconditionedZones = zoneservice.unconditionedZones(zonesInStorey);

                // NOTE: We purposefully have omitted the garage zones here.
                const mergedConditionedGroupZones = [...conditionedZones, ...unconditionedZones];
                const conditionedGroupingTotalFloorArea = mergedConditionedGroupZones
                    .map(x => x.floorArea)
                    .reduce((a, b) => a + b, 0);

                const conditionedGroupingTotalVolume = mergedConditionedGroupZones
                    .map(x => x.volume)
                    .reduce((a, b) => a + b, 0);

                const conditionedGroupingWallArea = mergedConditionedGroupZones
                    .map(x => x.exteriorWallArea)
                    .reduce((a, b) => a + b, 0);

                const conditionedGroupingGlazingArea = mergedConditionedGroupZones
                    .map(x => x.exteriorGlazingArea)
                    .reduce((a, b) => a + b, 0);


                if (conditionedZones.length > 0)
                    storeyGroup.zones.push(createSumRowForZones(
                    'Conditioned',
                    conditionedZones,
                    zoneActivityList,
                    zoneTypeList,
                    nccClassificationList,
                    conditionedGroupingTotalFloorArea,
                    conditionedGroupingTotalVolume,
                    conditionedGroupingWallArea,
                    conditionedGroupingGlazingArea));

                if (unconditionedZones.length > 0)
                    storeyGroup.zones.push(createSumRowForZones(
                        'Unconditioned',
                        unconditionedZones,
                        zoneActivityList,
                        zoneTypeList,
                        nccClassificationList,
                        conditionedGroupingTotalFloorArea,
                        conditionedGroupingTotalVolume,
                        conditionedGroupingWallArea,
                        conditionedGroupingGlazingArea));

                if (mergedConditionedGroupZones.length > 0)
                    storeyGroup.zones.push(createSumRowForZones(
                        'All Zones',
                        mergedConditionedGroupZones,
                        zoneActivityList,
                        zoneTypeList,
                        nccClassificationList,
                        conditionedGroupingTotalFloorArea,
                        conditionedGroupingTotalVolume,
                        conditionedGroupingWallArea,
                        conditionedGroupingGlazingArea));

                if (storeyGroup.zones.length > 0)
                    summaryGroup.rows.push(storeyGroup);

            });


            if (summaryGroup.rows.length > 0) {

                let wholeBuildingGroup = { name: "Whole Building", zones: [] }

                // Create "Conditioned" group.
                const conditionedZones = zoneservice.conditionedZones(zones);
                const unconditionedZones = zoneservice.unconditionedZones(zones);

                // NOTE: We purposefully have omitted the garage zones here.
                const mergedConditionedGroupZones = [...conditionedZones, ...unconditionedZones];
                const conditionedGroupingTotalFloorArea = mergedConditionedGroupZones
                    .map(x => x.floorArea)
                    .reduce((a, b) => a + b, 0);

                const conditionedGroupingTotalVolume = mergedConditionedGroupZones
                    .map(x => x.volume)
                    .reduce((a, b) => a + b, 0);

                const conditionedGroupingTotalWallArea = mergedConditionedGroupZones
                    .map(x => x.exteriorWallArea)
                    .reduce((a, b) => a + b, 0);

                const conditionedGroupingTotalGlazingArea = mergedConditionedGroupZones
                    .map(x => x.exteriorGlazingArea)
                    .reduce((a, b) => a + b, 0);

                if (conditionedZones.length > 0)
                    wholeBuildingGroup.zones.push(createSumRowForZones(
                        'Conditioned',
                        conditionedZones,
                        zoneActivityList,
                        zoneTypeList,
                        nccClassificationList,
                        conditionedGroupingTotalFloorArea,
                        conditionedGroupingTotalVolume,
                        conditionedGroupingTotalWallArea,
                        conditionedGroupingTotalGlazingArea));

                if (unconditionedZones.length > 0)
                    wholeBuildingGroup.zones.push(createSumRowForZones(
                        'Unconditioned',
                        unconditionedZones,
                        zoneActivityList,
                        zoneTypeList,
                        nccClassificationList,
                        conditionedGroupingTotalFloorArea,
                        conditionedGroupingTotalVolume,
                        conditionedGroupingTotalWallArea,
                        conditionedGroupingTotalGlazingArea));

                if (mergedConditionedGroupZones.length > 0)
                    wholeBuildingGroup.zones.push(createSumRowForZones(
                        'All Zones',
                        mergedConditionedGroupZones,
                        zoneActivityList,
                        zoneTypeList,
                        nccClassificationList,
                        conditionedGroupingTotalFloorArea,
                        conditionedGroupingTotalVolume,
                        conditionedGroupingTotalWallArea,
                        conditionedGroupingTotalGlazingArea));

                if (wholeBuildingGroup.zones.length > 0)
                    summaryGroup.rows.push(wholeBuildingGroup);

            }

            return summaryGroup;

        }

        /**
         * Returns the building zones for the given storey.
         *
         * @param {any} index 0 = ground level, 1 = first floor, etc.
         * @param {[]} zones Optional (filtered) zone array to draw data from.
         */
        function getZonesInStorey(index, zones = null) {
            let f = [];
            if (zones == null)
                f = zones?.filter(s => s.storey === index);
            else
                f = zones.filter(s => s.storey === index);
            return f;
        }

        /** Creates a row with data linked to a SPECIFIC zone */
        function createZoneRow(zone, totalFloorArea, totalVolume, totalExteriorWallArea, totalExteriorGlazingArea) {

            let row = {};

            let glassExteriorWallPercent = (zone.exteriorGlazingArea / zone.exteriorWallArea) * 100;
            if (Number.isNaN(glassExteriorWallPercent))
                glassExteriorWallPercent = 0;

            let glassFloorAreaPercent = (zone.exteriorGlazingArea / zone.floorArea) * 100;
            if (Number.isNaN(glassFloorAreaPercent))
                glassFloorAreaPercent = 0;

            row.zoneId = zone.zoneId;
            row.zoneNumber = zone.zoneNumber;
            row.description = zone.zoneDescription;
            row.zoneActivity = zone.zoneActivity?.description;
            row.zoneType = zone.zoneType?.description;
            row.conditioned = zone.conditioned ? "Yes" : "No";
            row.nccClassification = zone.nccClassification?.description;
            row.floorArea = zone.floorArea;
            row.floorAreaPercent = ((row.floorArea / totalFloorArea) * 100);
            row.volume = zone.volume;
            row.volumePercent = ((row.volume / totalVolume) * 100);

            row.exteriorWallArea = zone.exteriorWallArea;
            row.exteriorWallAreaPercent = (zone.exteriorWallArea / totalExteriorWallArea) * 100;
            row.exteriorGlazingArea = zone.exteriorGlazingArea;
            row.exteriorGlazingAreaPercent = (zone.exteriorGlazingArea / totalExteriorGlazingArea) * 100;

            row.glassExteriorWallAreaPercent = glassExteriorWallPercent;
            row.glassFloorAreaPercent = glassFloorAreaPercent;
            row.zoneCount = 1;

            // For individual rows, this data is already pre-calculated and in
            // the zone object itself, so we can use as-is.
            row.naturalLight = {
                requiredPercent: zone.naturalLightRequiredPercent,
                required: zone.naturalLightRequiredM2,
                achieved: "-"
            };

            row.ventilation = {
                requiredPercent: zone.ventilationRequiredPercent,
                required: zone.ventilationRequiredM2,
                achieved: "-",
            };

            row.airMovement = {
                requiredPercent: zone.airMovementRequiredPercent,
                required: zone.airMovementRequiredM2,
                achieved: "-",
            };

            return row;

        }

        /**
         * Creates a row with data being the SUMMED value of all zones passed in (i.e. pre-filtered)
         */
        function createSumRowForZones(
                description,
                zones,
                zoneActivityList,
                zoneTypeList,
                nccClassificationList,
                totalFloorArea,
                totalVolume,
                totalWallArea,
                totalGlazingArea) {

            let row = {};

            // Get all zones matching our filter.
            let matchingZones = zones;

            // TODO: Do this all in one loop...
            const floorArea = matchingZones.map(x => x.floorArea).reduce((a, b) => a + b, 0);
            const volume = matchingZones.map(x => x.volume).reduce((a, b) => a + b, 0);
            const exteriorWallArea = matchingZones.map(x => x.exteriorWallArea).reduce((a, b) => a + b, 0);
            const exteriorGlazingArea = matchingZones.map(x => x.exteriorGlazingArea).reduce((a, b) => a + b, 0);
            const lampPowerMaximumW = matchingZones.map(x => x.lampPowerMaximumW).reduce((a, b) => a + b, 0);

            let exteriorWallAreaPercent = (exteriorWallArea / totalWallArea) * 100;
            if (Number.isNaN(exteriorWallAreaPercent))
                exteriorWallAreaPercent = 0;

            let exteriorGlazingAreaPercent = (exteriorGlazingArea / totalGlazingArea) * 100;
            if (Number.isNaN(exteriorGlazingAreaPercent))
                exteriorGlazingAreaPercent = 0;

            let glassExteriorWallAreaPercent = (exteriorGlazingArea / exteriorWallArea) * 100;
            if (Number.isNaN(glassExteriorWallAreaPercent))
                glassExteriorWallAreaPercent = 0;

            let glassFloorAreaPercent = (exteriorGlazingArea / floorArea) * 100;
            if (Number.isNaN(glassFloorAreaPercent))
                glassFloorAreaPercent = 0;

            row.zoneNumber = ""; // Not an actual zone, so blank.
            row.description = description;
            row.zoneActivity = determineZoneActivitySum(matchingZones, zoneActivityList);
            row.zoneType = determineZoneTypeSum(matchingZones, zoneTypeList);
            row.conditioned = determineConditionedSum(matchingZones);
            row.nccClassification = determineNccClassificationSum(matchingZones, nccClassificationList);
            row.floorArea = floorArea;
            row.floorAreaPercent = totalFloorArea === 0 ? 0 : ((row.floorArea / totalFloorArea) * 100);
            row.volume = volume;
            row.volumePercent = totalVolume === 0 ? 0 : ((row.volume / totalVolume) * 100);

            row.exteriorWallArea = exteriorWallArea;
            row.exteriorWallAreaPercent = exteriorWallAreaPercent;
            row.exteriorGlazingArea = exteriorGlazingArea;
            row.exteriorGlazingAreaPercent = exteriorGlazingAreaPercent;

            row.lampPowerMaximumW = lampPowerMaximumW;

            row.glassExteriorWallAreaPercent = glassExteriorWallAreaPercent;
            row.glassFloorAreaPercent = glassFloorAreaPercent;


            row.zoneCount = zones.length;

            // For individual rows, this data is already pre-calculated and in
            // the zone object itself, so we can use as-is.
            row.naturalLight = {
                requiredPercent: determineSameOrVaries(matchingZones, 'naturalLightRequiredPercent'),
                required: matchingZones.map(x => x.naturalLightRequiredM2).reduce((a, b) => a + b, 0),
                achieved: "-"
            };

            row.ventilation = {
                requiredPercent: determineSameOrVaries(matchingZones, 'ventilationRequiredPercent'),
                required: matchingZones.map(x => x.ventilationRequiredM2).reduce((a, b) => a + b, 0),
                achieved: "-",
            };

            row.airMovement = {
                requiredPercent: determineSameOrVaries(matchingZones, 'airMovementRequiredPercent'),
                required: 0,
                achieved: "-",
            };

            //row.lampPower = {
            //    maximumWM2: determineSameOrVaries(matchingZones, 'lampPowerMaximumWM2'),
            //    maximumW: matchingZones.map(x => x.lampPowerMaximumW).reduce((a, b) => a + b),
            //    achieved: "-",
            //};

            return row;
        }

        /** Determines what the SUM ZoneActivity is for the given zones. Can be 'VARIES'. */
        function determineZoneActivitySum(zones, zoneActivityList) {

            if (zones == null || zones.length == 0)
                return "-";

            // Check over every possible zoneActivity available and see if EVERY zone given
            // matches. If it does, then that's our activity.
            for (let i = 0; i < zoneActivityList.length; i++) {
                let za = zoneActivityList[i];

                if (zones.every(x => x.zoneActivity?.zoneActivityCode == za.zoneActivityCode))
                    return za.description;
            }

            return "Varies";
        }

        /** Determines what the SUM ZoneType is for the given zones. Can be 'Varies'. */
        function determineZoneTypeSum(zones, zoneTypeList) {

            if (zones == null || zones.length == 0)
                return "-";

            // Check over every possible zoneActivity available and see if EVERY zone given
            // matches. If it does, then that's our activity.
            for (let i = 0; i < zoneTypeList.length; i++) {
                let za = zoneTypeList[i];

                if (zones.every(x => x.zoneType?.zoneTypeCode == za.zoneTypeCode))
                    return za.description;
            }

            return "Varies";
        }

        /** Determines what the SUM 'Conditioned' value is for the given zones. Can be 'Varies'. */
        function determineConditionedSum(zones) {

            if (zones == null || zones.length == 0)
                return "-";

            if (zones.every(x => x.conditioned === true))
                return "Yes";

            if (zones.every(x => x.conditioned === false))
                return "No";

            return "Varies";
        }

        /** Determines what the SUM NccClassification is for the given zones. Can be 'VARIES'. */
        function determineNccClassificationSum(zones, nccClassificationList) {

            if (zones == null || zones.length == 0)
                return "-";

            // Check over every possible zoneActivity available and see if EVERY zone given
            // matches. If it does, then that's our activity.
            for (let i = 0; i < nccClassificationList.length; i++) {
                let za = nccClassificationList[i];

                if (zones.every(x => x.nccClassification?.nccClassificationCode == za.nccClassificationCode))
                    return za.description;
            }

            return "Varies";
        }

        /**
        *  Does NOT return the sum. Either returns a single value if all matching
        *  rows are the same, OR returns "Varies".
        */
        function determineSameOrVaries(zones, path) {
            if (zones == null || zones.length == 0)
                return "0.00";
            else {

                // If the first zone % is not the same as ALL, then it naturally "Varies"
                let zone = zones[0];
                if (zones.every(x => common.resolve(path, x) == common.resolve(path, zone)))
                    return common.resolve(path, zone);
                else
                    return "Varies";
            }
        }

        /**
          *  Checks to see weather for ALL zones if achieved > required.
          *  If true, PASS, if false, FAIL.
          */
        function determinePassOrFail(zones, requiredPath, achievedPath) {
            if (zones == null || zones.length == 0)
                return "Insufficient Data";
            else {

                // If the first zone % is not the same as ALL, then it naturally "Varies"
                if (zones.every(x => common.resolve(achievedPath, x) >= common.resolve(requiredPath, x)))
                     return "Pass";
                else
                    return "Fail";
            }
        }

        function sectorFromLabel(label, sectors) {
            return sectors.find(x => x.label.toLowerCase() === label);
        }

        /**
         * Envelope Summary Only - Checks if a Sector total area column is empty (for hiding)
         *
         * @param   {string} sector 'n', 'ne', 'e', 'se', 's', 'sw', 'w', 'nw'
         * @returns {boolean}
        */
        function isSectorNotEmpty(sector, envelopeSummaryData) {
            if (sector === 'total' || envelopeSummaryData.isPlaceholder)
                return true; // Always show total.

            // Data might be have loaded yet
            if (!envelopeSummaryData.exteriorWallAreaTotalsPerSector || !envelopeSummaryData.exteriorGlazingAreaTotalsPerSector)
                return false;

            const isWallNotEmpty = envelopeSummaryData.exteriorWallAreaTotalsPerSector.hasOwnProperty(sector) &&
                (envelopeSummaryData.exteriorWallAreaTotalsPerSector[sector].area && envelopeSummaryData.exteriorWallAreaTotalsPerSector[sector].percentage);
            const isGlazingNotEmpty = envelopeSummaryData.exteriorGlazingAreaTotalsPerSector.hasOwnProperty(sector) &&
                (envelopeSummaryData.exteriorGlazingAreaTotalsPerSector[sector].area && envelopeSummaryData.exteriorGlazingAreaTotalsPerSector[sector].percentage);

            // A facade should be shown if EITHER the Exterior Wall Area OR Exterior Glazing Area is greater than 0
            return isWallNotEmpty || isGlazingNotEmpty;
        }


        // ----------- //
        // - FILTERS - //
        // ----------- //

        function applySelectionLogic(filter, selection, recalcCallback) {
            setTimeout(() => {
                // SELECT or DE-SELECT all selections when the 'ALL' option is clicked.
                if (selection.code === "ALL" && filter.selection.some(x => x.code === "ALL")) {
                    filter.selection = angular.copy(filter.selectableArray);
                } else if (selection.code === "ALL" && !filter.selection.some(x => x.code === "ALL")) {
                    filter.selection = [];
                } else {
                    if (filter.selection) {
                        // Just make sure to de-select the "ALL" checkbox if anything else is selected.
                        filter.selection = filter.selection.filter(x => x.code !== "ALL");
                    }
                }
                if (recalcCallback != null) {
                    recalcCallback();
                }
                // Force refresh (Was taking multiple seconds to reflect change otherwise...?)
                const phase = $rootScope.$$phase;
                if (!phase) {
                    $rootScope.$apply();
                }
            }, 50);
        }

        function applyStoreySelectLogic(building, filter, recalculateCallback) {
            setTimeout(() => {
                applyGroupSelectLogic(building, filter, filter.group, recalculateCallback);
            }, 30);
        }

        function applyGroupSelectLogic(building, filter, group, recalculateCallback) {

            setTimeout(() => {

                // Refine selection to only show what is available.
                let allExtGlazingElements = [];
                if (building.openings.length > 0) {
                    allExtGlazingElements = building.openings.filter(x => x.category.constructionCategoryCode === "ExteriorGlazing")
                                                             .map(x => x.elements)
                                                             .reduce((a, b) => [...a, ...b]);
                }

                  let allExtWallElements = [];
                  if (building.surfaces.length > 0) {
                      allExtWallElements = building.surfaces.filter(x => x.category.constructionCategoryCode === "ExteriorWall")
                                                            .map(x => x.elements)
                                                            .reduce((a, b) => [...a, ...b]);
                  }

                let zonesWithExtGlazingOrExtWalls = zoneservice.interiorZones(building.zones)
                                                               .filter(zone =>
                                                                   allExtGlazingElements.some(y => y.parentZoneId === zone.linkId)
                                                                   || allExtWallElements.some(y => y.parentZoneId === zone.linkId)
                                                               );

                if (filter.storey != null && filter.storey !== "ALL") {
                    zonesWithExtGlazingOrExtWalls = zonesWithExtGlazingOrExtWalls.filter(x => x.storey === filter.storey.floor);
                    allExtGlazingElements = allExtGlazingElements.filter(x => x.storey === filter.storey.floor);
                }

                if (filter.group === "All Zones")
                    filter.selectableArray = ALL_ZONES_OPTIONS;
                if (filter.group === "Zone Name")
                    filter.selectableArray = presentZoneNames(zonesWithExtGlazingOrExtWalls);
                if (filter.group === "Zone Activity")
                    filter.selectableArray = presentZoneActivities(zonesWithExtGlazingOrExtWalls);
                if (filter.group === "Zone Type")
                    filter.selectableArray = presentZoneTypes(zonesWithExtGlazingOrExtWalls);
                if (filter.group === "Conditioning")
                    filter.selectableArray = CONDITIONED_GROUP_OPTIONS;
                if (filter.group === "NCC Classification")
                    filter.selectableArray = presentNccClassifications(zonesWithExtGlazingOrExtWalls);
                if (group === "All Zones")
                    filter.selection = ["All Zones"];

                // Recalculate Envelope Summary data.
                recalculateCallback();
            },
            30);
        }


        function presentZoneNames(zones){
            // Figure out which unique opening styles are present in the construction data.
            const uniques = {};
            zones.forEach(x => {
                uniques[x.zoneDescription] = { title: x.zoneDescription, code: x.zoneDescription };
            });
            let converted = [{ title: 'All Zone Names', code: 'ALL' }];
            for(let prop in uniques)
                converted.push(uniques[prop]);
            return converted;
        }

        function presentZoneActivities(allZones) {
            // Figure out which unique opening styles are present in the construction data.
            const uniques = {};
            uniques["All Zone Activities"] = { title: "All Zones Activities", code: "ALL" };
            allZones.forEach(x => {
                uniques[x.zoneActivity.description] = {
                    title: x.zoneActivity.description,
                    code:  x.zoneActivity.zoneActivityCode
                };
            });
            let converted = [];
            for(let prop in uniques)
                converted.push(uniques[prop]);
            return converted;
        }

        function presentZoneTypes(allZones) {
            // Figure out which unique opening styles are present in the construction data.
            const uniques = {};
            uniques["All Zone Types"] = { title: "All Zones Types", code: "ALL" };
            allZones.forEach(x => {
                uniques[x.zoneType.description] = {
                    title: x.zoneType.description,
                    code:  x.zoneType.zoneTypeCode
                };
            });
            let converted = [];
            for(let prop in uniques)
                converted.push(uniques[prop]);
            return converted;
        }

        function presentNccClassifications(allZones) {
            // Figure out which unique opening styles are present in the construction data.
            const uniques = {};
            uniques["All Classifications"] = { title: "All Classifications", code: "ALL" };
            allZones.forEach(x => {
                if(x.nccClassification == null)
                    return;
                uniques[x.nccClassification?.description] = {
                    title: x.nccClassification.description,
                    code:  x.nccClassification.nccClassificationCode
                };
            });
            let converted = [];
            for(let prop in uniques)
                converted.push(uniques[prop]);
            return converted;
        }

        const DEFAULT_SECTORS = [{
                "operator1": ">",
                "min": 337.5,
                "operator2": "<",
                "max": 360,
                "label": "N",
                "description": "North"
            },{
                "operator1": ">=",
                "min": 0,
                "operator2": "<=",
                "max": 22.5,
                "label": "N",
                "description": "North"
            },{
                "operator1": ">",
                "min": 22.5,
                "operator2": "<=",
                "max": 67.5,
                "label": "NE",
                "description": "North-East"
            },{
                "operator1": ">",
                "min": 67.5,
                "operator2": "<=",
                "max": 112.5,
                "label": "E",
                "description": "East"
            },{
                "operator1": ">",
                "min": 112.5,
                "operator2": "<=",
                "max": 157.5,
                "label": "SE",
                "description": "South-East"
            },{
                "operator1": ">",
                "min": 157.5,
                "operator2": "<=",
                "max": 202.5,
                "label": "S",
                "description": "South"
            },{
                "operator1": ">",
                "min": 202.5,
                "operator2": "<=",
                "max": 247.5,
                "label": "SW",
                "description": "South-West"
            },{
                "operator1": ">",
                "min": 247.5,
                "operator2": "<=",
                "max": 292.5,
                "label": "W",
                "description": "West"
            },{
                "operator1": ">",
                "min": 292.5,
                "operator2": "<=",
                "max": 337.5,
                "label": "NW",
                "description": "North-West"
        }];

        return {
            defaultFilters: DEFAULT_FILTERS,
            defaultFiltersConditioned: DEFAULT_FILTERS_CONDITIONED,
            defaultFiltersHabitable: DEFAULT_FILTERS_HABITABLE,
            groupOptions: GROUP_OPTIONS,
            allZonesOptions: ALL_ZONES_OPTIONS,
            knownBuildingSummaryGroups: knownBuildingSummaryGroups,
            calculateEnvelopeSummaryData: calculateEnvelopeSummaryData,
            constructBuildingSummaryGroups: constructBuildingSummaryGroups,
            determineZoneActivitySum: determineZoneActivitySum,
            determineZoneTypeSum: determineZoneTypeSum,
            determineConditionedSum: determineConditionedSum,
            determineNccClassificationSum: determineNccClassificationSum,
            determineSameOrVaries: determineSameOrVaries,
            determinePassOrFail: determinePassOrFail,
            sectorFromLabel: sectorFromLabel,
            isSectorNotEmpty: isSectorNotEmpty,
            applySelectionLogic: applySelectionLogic,
            applyStoreySelectLogic: applyStoreySelectLogic,
            applyGroupSelectLogic: applyGroupSelectLogic,
            defaultSectors: DEFAULT_SECTORS
        };


    }
})();
