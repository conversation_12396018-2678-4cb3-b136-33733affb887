USE [thermarate];

SELECT [option].[ComplianceOptionsId]
      ,[option].[IsCompliant]
      ,[option].[RequiredHouseEnergyRating]
      ,[option].[AssessmentId]
      ,[option].[ComplianceMethodCode]
      ,[option].[Description]
      ,[option].[IsSelected]
      ,[option].[CreatedOn]
      ,[option].[CreatedByName]
      ,[option].[ModifiedOn]
      ,[option].[ModifiedByName]
      ,[option].[Deleted]
      ,[option].[ItemReference]
      ,[option].[IsLocked]
      ,[option].[EPComplianceDataJson]
      ,[option].[OptionIndex]
      ,[option].[IsCompliant]
      ,[option].[AssessmentSoftwareCode]
      ,[option].[NewPurchaseOrderRequired]
      ,[option].[UpdatedDrawingsRequired]
      ,[option].[RequiredHouseEnergyRating]
      ,[option].[MarkupFileRequired]
      ,[option].[ProposedBuildingId]
      ,[option].[ReferenceBuildingId]
      ,[option].[PurchaseOrderFileId]
      ,[option].[IsBaselineSimulation]
      ,[option].[PurchaseOrder]
      ,[option].[HeatingAndCoolingRulesetCode]
      ,[option].[CertificationId]
      ,[option].[SectorDeterminationCode]
      ,[option].[IsComplianceValid]
      ,[option].[NCC2022Json]
      ,[option].[IsShownToClient]
  FROM [dbo].[RSS_AssessmentComplianceOption] [option]
  INNER JOIN [dbo].[RSS_Assessment] [assessment] ON [option].[AssessmentId] = [assessment].[AssessmentId]
  INNER JOIN [dbo].[RSS_Job]        [job]        ON [assessment].[JobId] = [job].[JobId]
  INNER JOIN [dbo].[RSS_Client]     [client]     ON [job].[ClientId] = [client].[ClientId]
  --INNER JOIN [dbo].[RSS_Certification] [certification] ON [option].[CertificationId] = [certification].[CertificationId]
  WHERE 1=1
    -- AND [option].[ComplianceOptionsId] = '68087a41-dcdb-2201-30b1-3a070437d0c4'
    -- AND [client].[ClientId] = 'e416204a-0c32-4bc7-9acd-7e8b0e750acf'
    AND [assessment].[AssessmentId] = 'd502d67c-b457-4c31-b4ff-d4a3a255c1d2'
	--AND [option].[IsSelected] = 1
	-- AND [assessment].[StatusCode] = 'AIssued'
	--AND [option].[ComplianceMethodCode] IN ('CMHouseEnergyRating', 'CMPerfSolutionHER')

    -- Get Assessment that has more than 1 option
    -- AND (
    --     SELECT COUNT([ComplianceOptionsId])
    --     FROM [dbo].[RSS_AssessmentComplianceOption] [temp]
    --     WHERE [temp].[AssessmentId] = [assessment].[AssessmentId]
    -- ) > 1

	-- ------------------------------------
	-- To generate Specifications 44
	--AND [option].[IsSelected] = 1
	--AND [assessment].[StatusCode] = 'AComplete'
	--AND [option].[ComplianceMethodCode] IN ('CMPerfELL')
	-- ------------------------------------

  ORDER BY [assessment].[CreatedOn] DESC

  -- Set 'isSelected'
  -- UPDATE [dbo].[RSS_AssessmentComplianceOption]
  -- SET [IsSelected] = 1
  -- WHERE [AssessmentId] = '5c5c7746-9993-4c91-b560-7c80beede7a6'

  --UPDATE [dbo].[RSS_AssessmentComplianceOption]
  --SET [NCC2022Json] = NULL

  --UPDATE [dbo].[RSS_AssessmentComplianceOption]
  --SET [RequiredHouseEnergyRating] = 6
  --WHERE [ComplianceOptionsId] = '0D162155-5A9B-095C-0BE0-3A0F21A18FE3'