using RediSoftware.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using RediSoftware.Dtos;
using AutoMapper;
using RediSoftware.Redi_Utility;
using RediSoftware.Helpers;
using RediSoftware.App_Start;
using AutoMapper.QueryableExtensions;
using Newtonsoft.Json;

namespace RediSoftware.BusinessLogic
{

    public enum JobStatus {
        JDraft,
        JInProgress,
        JCancelled,
        JPreliminaryReview,
        JCompliance,
        JOptionSelected,
        JIssued,
        JSuperseded,
        JComplete,
        JRecertification
    }
    public partial class Job : BusinessLogicBase
    {
		private Guid _jobId;
		private RSS_Job _jobDbModel;

		/// <summary>
		/// Returns the Id in the database model (useful to get an Id after changes have been commited)
		/// </summary>
		public Guid CreatedId
		{
			get
			{
				return _jobDbModel.JobId;
			}
		}

        public JobDto Dto
        {
            get
            { 
                return _mapper.Map<JobDto>(_jobDbModel);
            }
        }

        public ExtendedJobDto ExtendedDto
        {
            get
            {
                ExtendedJobDto dto = _mapper.Map<ExtendedJobDto>(_jobDbModel);
                dto.Assessments = _unitOfWork.Context.RSS_Assessment.Where(s => s.JobId == _jobDbModel.JobId && s.Deleted == false).OrderByDescending(s => s.CreatedOn).ProjectTo<ExtendedAssessmentDto>(_mapper.ConfigurationProvider).ToList();
                //dto.JobEvents = _unitOfWork.Context.RSS_JobEvent.Where(s => s.JobId == _jobDbModel.JobId && s.Deleted == false).OrderByDescending(s => s.CreatedOn).ProjectTo<JobEventDto>(_mapper.ConfigurationProvider).ToList();

                return dto;
            }
        }

        public Job(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _mapper = mapper;
			_unitOfWork = unitOfWork;
        }

		/// <summary>
		/// Get db model for supplied Id
		/// </summary>
        public Job Get(Guid jobId)
        {
			_jobId = jobId;
			GetModel();

			if (_jobDbModel == null)
			{
				throw new Exception(string.Format("{1} row not found for Id: {0}", _jobId, "RSS_Job"));
			}

			return this;
        }

        public ExtendedJobDto GetById(Guid jobId)
        {
            try
            {
                var raw = _unitOfWork.Context.RSS_Job.Find(jobId);
                var mapped = this.BuildExtendedJobDto(raw);

                var ass = _unitOfWork.Context.RSS_Assessment.Find(mapped.CurrentAssessmentId);
                mapped.CurrentAssessment = Assessment.BuildExtendedDto(ass, _unitOfWork, _mapper);
                return mapped;
            }
            catch(Exception e)
            {
                throw;
            }
            
        }

        private Boolean GetModel()
		{
			_jobDbModel = _unitOfWork.Context.RSS_Job.Where(s => s.JobId == _jobId).FirstOrDefault();
			if (_jobDbModel != null)
			{
				return true;
			}
			return false;
		}

        /// <summary>
        /// Using the given Job, creates a new Job within the Database.
        /// If doCopy = true is specified, re-assigns all ID's so that when passed an 
        /// existing job, it instead creates a copy rather than trying to update it.
        /// </summary>
        /// <param name="job"></param>
        public Guid Create(ExtendedJobDto job)
        {

			if(job.JobId == null || job.JobId == Guid.Empty)
			{
				_jobId = SequentialGuid.NewSequentialGuid();
				job.JobId = _jobId;
            }
			else // Don't try an update if we're making a copy.
			{
				// Guid passed in - see if it already exists - if found update.
				_jobId = job.JobId;
				if (GetModel())
				{
					// Already exists so go update instead.
					Update(job);
					return job.JobId;
				}
			}

            // To avoid a host of circular reference problems, we unlink most of the model
            // from it's parent/children, then save to the context, then re-link.

            // STEP 1: SAVE JOB (WITH NO LINK TO ASSESSMENT!)
            var assessment = job.CurrentAssessment;
            if (assessment.AssessmentId == Guid.Empty)
                assessment.AssessmentId = SequentialGuid.NewSequentialGuid();

            job.CurrentAssessment = null;   // Unlink job reference to assessment.
            job.CurrentAssessmentId = null;

            // Set some initial job details.
            job.StatusCode = JobStatus.JDraft.ToString("G");
            job.Status = _unitOfWork.Context.RSS_Status.Where(s => s.StatusCode == job.StatusCode).ProjectTo<StatusDto>(_mapper.ConfigurationProvider).FirstOrDefault();

            job.JobReference = NextNumber.GetNextID("JobRef");
            job.CreatedOn = DateTime.Now;
            job.ModifiedOn = DateTime.Now;
            job.CreatedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";
            
            // Map Job to table row and save.
            var rssJob = _mapper.Map<RSS_Job>(job);
            rssJob.CurrentAssessmentId = null;
            rssJob.RSS_Assessment1 = null;
            _unitOfWork.Context.RSS_Job.Add(rssJob);
            _unitOfWork.Context.SaveChanges();

            // Assign some intiial ProjectDetail values.
            if (assessment.AssessmentProjectDetail == null)
                assessment.AssessmentProjectDetail = new AssessmentProjectDetailDto();

            assessment.AssessmentProjectDetail.AssessmentId = assessment.AssessmentId;
            assessment.AssessmentProjectDetail.CreatedOn = DateTime.Now;
            assessment.AssessmentProjectDetail.CreatedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";
            assessment.AssessmentProjectDetail.AssessmentVersion = 1.0M;
            assessment.AssessorUser = job.AssessorUser;
            
            if (assessment?.AllComplianceOptions[0].RequiredHouseEnergyRating == null)
                assessment.AllComplianceOptions[0].RequiredHouseEnergyRating = -1;

            // STEP 2: SAVE ASSESSMENT
            assessment.JobId = job.JobId;
            assessment.Job = job;
            assessment.StatusCode = AssessmentStatus.ADraft.ToString("G");
            assessment.Status = _unitOfWork.Context.RSS_Status.Where(s => s.StatusCode == assessment.StatusCode).ProjectTo<StatusDto>(_mapper.ConfigurationProvider).FirstOrDefault();

            var test = JsonConvert.SerializeObject(assessment);

            Assessment.InsertIntoDbInOrder(assessment,  _mapper, _unitOfWork);

            // Re-assign
            job.CurrentAssessment = assessment;
            job.CurrentAssessmentId = assessment.AssessmentId;
            rssJob.CurrentAssessmentId = assessment.AssessmentId; // Relink job.
            rssJob.RSS_Assessment1 = null;
            
            _unitOfWork.Context.SaveChanges();
            
            if (job.FilesInOrder?.Count > 0)
                AssessmentDrawing.StartPdfSplittingProcess(_unitOfWork, assessment.BaselineSimulation, job.FilesInOrder, job.Client.ClientDefault.IncludeDrawingsInReport, job.Client.ClientDefault.StampDrawings);

            _unitOfWork.Context.SaveChanges();

            LogEvent(string.Format("New job created for {0}", job.Client.ClientName));

            return assessment.AssessmentId;

        }

        

        public void Update(ExtendedJobDto jobDto)
        {
            if (_jobDbModel == null) {throw new Exception(string.Format("_jobDbModel not set"));}
            jobDto.ModifiedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";
            jobDto.ModifiedOn = DateTime.Now;

            // Make sure Job Status cannot be updated via this path.
            jobDto.StatusCode = _jobDbModel.StatusCode;
            if (jobDto.CurrentAssessment.AssessmentProjectDetail != null)
            {
                jobDto.CurrentAssessment.AssessmentProjectDetail.ModifiedOn = DateTime.Now;
                jobDto.CurrentAssessment.AssessmentProjectDetail.ModifiedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";
            }

			// Update the database model
            _mapper.Map((JobDto)jobDto, _jobDbModel);
        }

        public void UpdateJobStatus(JobStatus newStatus)
        {
            if (_jobDbModel == null) { throw new Exception(string.Format("_jobDbModel not set")); }

            if (_jobDbModel.StatusCode == newStatus.ToString("G"))
            {
                // No change, so get out of here.
                return;
            }

            string oldStatus = _jobDbModel.StatusCode;
            _jobDbModel.StatusCode = newStatus.ToString("G");

            //TODO: Add Logic Here to deal with things that need to happen based on status change.

            _jobDbModel.ModifiedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";
            _jobDbModel.ModifiedOn = DateTime.Now;

            Queue.AddNewQueueRecord(Queue.QueueCode.UpdateGoogleSheets, _jobDbModel.JobId.ToString(), _unitOfWork.Context);

            LogEvent(string.Format("Job status updated to {0}, was {1}", _jobDbModel.StatusCode, oldStatus));
        }

        public void UpdateJobStatusWithAssessmentStatus(AssessmentStatus assessmentStatus)
        {
            JobStatus newStatus = JobStatus.JInProgress;
            switch (assessmentStatus)
            {
                case AssessmentStatus.AComplete:
                    {
                        newStatus = JobStatus.JComplete;
                        break;
                    }
                case AssessmentStatus.ACompliance:
                    {
                        newStatus = JobStatus.JCompliance;
                        break;
                    }
                case AssessmentStatus.AInProgress:
                    {
                        newStatus = JobStatus.JInProgress;
                        break;
                    }
                case AssessmentStatus.AIssued:
                    {
                        newStatus = JobStatus.JIssued;
                        break;
                    }
                default:
                    {
                        break;
                    }
            }

            UpdateJobStatus(newStatus);
        }

        public List<JobDto> GetActiveJobs()
        {
            //job statuses to ignore
            string cancelled = JobStatus.JCancelled.ToString("G");
            string issued = JobStatus.JIssued.ToString("G");

            return _unitOfWork.Context.RSS_Job.Where(s => s.Deleted == false
                && s.StatusCode != cancelled 
                && s.StatusCode != issued).Take(int.MaxValue)
                .ProjectTo<JobDto>(_mapper.ConfigurationProvider).ToList();
        }

        public List<JobDto> GetActiveJobsForUser(string userName)
        {
            //job statuses to ignore
            string cancelled = JobStatus.JCancelled.ToString("G");
            string issued = JobStatus.JIssued.ToString("G");

            if (string.IsNullOrEmpty(userName)) { throw new Exception("Error getting active jobs for User - username is null or empty."); }
            userName = userName.ToLower();
            AspNetUser aspNetUser = _unitOfWork.Context.AspNetUsers.Where(s => s.LoweredUserName == userName).FirstOrDefault();
            if (aspNetUser == null) { throw new Exception("Error getting active jobs for User - could not find user to match username '" + userName + "'."); }
            RSS_User user = _unitOfWork.Context.RSS_User.Where(s => s.UserId.ToString() == aspNetUser.Id).FirstOrDefault();
            if (user == null) { throw new Exception("Error getting active jobs for User - could not find User to match username '" + userName + "'"); }
            Guid? userId = user.UserId;
            return _unitOfWork.Context.RSS_Job.Where(s => s.Deleted == false
                && s.StatusCode != cancelled
                && s.StatusCode != issued
                && s.AssessorUserId == userId).ProjectTo<JobDto>(_mapper.ConfigurationProvider).ToList();
        }

        /// <summary>
        /// Assign an Assessor to the job
        /// </summary>
        /// <param name="assessorUserId">Assessors UserId, or null to remove assessor</param>
        public void AssignAssessor(Guid? assessorUserId)
        {
            if (_jobDbModel == null) { throw new Exception(string.Format("_jobDbModel not set")); }
            if (assessorUserId == _jobDbModel.AssessorUserId)
            {
                return; // Do nothing - already assigned.
            }

            Guid? prevAssessorUserId = _jobDbModel.AssessorUserId;
            _jobDbModel.AssessorUserId = assessorUserId;

            //TODO: Add Logic Here to deal with things that need to happen when assessor changed

            _jobDbModel.ModifiedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";
            _jobDbModel.ModifiedOn = DateTime.Now;

            if (assessorUserId != null)
            {
                LogEvent(string.Format("Assessor '{0}' assigned.", _unitOfWork.Context.RSS_User.Find(assessorUserId).FullName));
            }
            else
            {
                LogEvent(string.Format("Assessor '{0}' removed from job.", _unitOfWork.Context.RSS_User.Find(prevAssessorUserId).FullName));
            }
        }

        public void Delete()
        {
			if (_jobDbModel == null) {throw new Exception(string.Format("_jobDbModel not set"));}

			// Update the database model
            _jobDbModel.ModifiedOn = DateTime.Now;
            _jobDbModel.ModifiedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";
            _jobDbModel.Deleted = true;

            LogEvent(string.Format("Job deleted"));
        }

        public string Cancel()
        {
            if (_jobDbModel == null) { throw new Exception(string.Format("_jobDbModel not set")); }

            // Update the database model
            _jobDbModel.ModifiedOn = DateTime.Now;
            _jobDbModel.ModifiedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";
            _jobDbModel.StatusCode = "JCancelled";
            
            // We also cancel the current assessment...
            var currentAssessment = _unitOfWork.Context.RSS_Assessment
                .Where(a => a.JobId == _jobDbModel.JobId && a.AssessmentId == _jobDbModel.CurrentAssessmentId)
                .FirstOrDefault();

            if (currentAssessment != null)
                currentAssessment.StatusCode = AssessmentStatus.ACancelled.ToString("G");
            

            LogEvent(string.Format("Job cancelled"));

            return _jobDbModel.StatusCode;
        }

        /// <summary>
        /// Reinstates a job after being cancelled.
        /// </summary>
        public string Reinstate()
        {
            if (_jobDbModel == null) { throw new Exception(string.Format("_jobDbModel not set")); }

            // Update the database model
            _jobDbModel.ModifiedOn = DateTime.Now;
            _jobDbModel.ModifiedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";
            _jobDbModel.StatusCode = "JInProgress"; // Possibly this might need to be set according to the current assessment?
            
            // Un-cancel latest assessment.. just put into the 'in progress' state.
            var currentAssessment = _unitOfWork.Context.RSS_Assessment
                .Where(a => a.JobId == _jobDbModel.JobId && a.AssessmentId == _jobDbModel.CurrentAssessmentId)
                .FirstOrDefault();

            if (currentAssessment != null)
                currentAssessment.StatusCode = AssessmentStatus.AInProgress.ToString("G");

            LogEvent(string.Format("Job reinstated"));

            return _jobDbModel.StatusCode;
        }

        public void UndoDelete()
        {
			if (_jobDbModel == null) {throw new Exception(string.Format("_jobDbModel not set"));}

			// Update the database model
            _jobDbModel.ModifiedOn = DateTime.Now;
            _jobDbModel.ModifiedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";
            _jobDbModel.Deleted = false;

            LogEvent(string.Format("Job restored (undeleted)"));
        }

        public void LogEvent(string description, string eventTypeCode = null)
        {
            RSS_JobEvent jobEvent = new RSS_JobEvent();
            jobEvent.JobEventId = SequentialGuid.NewSequentialGuid();
            jobEvent.JobId = _jobId;
            jobEvent.EventTypeCode = eventTypeCode;
            jobEvent.Description = description;

            jobEvent.CreatedOn = DateTime.Now;
            jobEvent.CreatedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";

            _unitOfWork.Context.RSS_JobEvent.Add(jobEvent);
        }

        //given the filepath of an uploaded file, move the file to amazon and create and return a filedto
        public FileDto AddFile(string originalFileName, 
            string folder, 
            Guid jobId, 
            Guid assessmentId, 
            string category = null, 
            string classification = null)
        {
            GeneratedDocStorageUtil docUtil = new GeneratedDocStorageUtil();
            string url = docUtil.StoreDocumentFile(originalFileName, folder, category, classification, jobId, assessmentId, null, null);
            RSS_File file = _unitOfWork.Context.RSS_File.Where(s => s.URL == url || s.OriginalFileUrl == url).FirstOrDefault();
            return _mapper.Map<FileDto>(file);
        }

        public ExtendedJobDto BuildExtendedJobDto(RSS_Job _model)
        {
            ExtendedJobDto dto = _mapper.Map<ExtendedJobDto>(_model);
            return dto;
        }

        /// <summary>
        /// Creates a copy of the given JobDto and it's CURRENT ASSESSMENT only.
        /// Changes passed in from the frontend are respected. GUIDS are re-assigned
        /// for everything but that's it.
        /// </summary>
        public Guid Copy(ExtendedJobDto jobDto)
        {
            // Assign high-level job values.
            // Includes clearing any objects which EF will fill-in based on their GUID.

            // Note: To preserve the ability to use the new file drawing file uploads field, we cannot
            // re-assign the Job ID GUID here. The frontend will (must!) have already done this for us.
            // jobDto.JobId = Guid.NewGuid();
            jobDto.CreatedOn = DateTime.Now;
            jobDto.ModifiedOn = null;
            jobDto.CreatedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";
            jobDto.JobReference = NextNumber.GetNextID("JobRef");
            jobDto.StatusCode = JobStatus.JInProgress.ToString("G");
            jobDto.Status = _unitOfWork.Context.RSS_Status.Where(s => s.StatusCode == jobDto.StatusCode).ProjectTo<StatusDto>(_mapper.ConfigurationProvider).FirstOrDefault();

            var assessmentDto = Assessment.ReassignGuids(
                    jobDto.CurrentAssessment,
                    jobDto.JobId);

            assessmentDto.Status = _unitOfWork.Context.RSS_Status.Where(s => s.StatusCode == assessmentDto.StatusCode).ProjectTo<StatusDto>(_mapper.ConfigurationProvider).FirstOrDefault();

            // Assign some more things we couldn't really do within the "ReassigGuids" call.
            assessmentDto.AssessorUserId = jobDto.AssessorUser.UserId;
            jobDto.CurrentAssessment = assessmentDto;
            jobDto.CurrentAssessmentId = assessmentDto.AssessmentId;

            // Now that are GUID's have been changed from the originals, we can insert
            // everything into the Database.
            var id = Create(jobDto);

            // Now that our assessment exists in the DB we can copy and assign our drawings to it (if needed). If new
            // drawings have been passed in via the 'files in order' option, these apply only to the baseline assessment
            // and are not being 'copied' from previous assessments, and thus must run through the usual splitting / 
            // conversion process.
            if (jobDto.FilesInOrder == null || jobDto.FilesInOrder.Count == 0)
            {
                foreach (var opt in assessmentDto.AllComplianceOptions)
                {
                    AssessmentDrawing.CopyAndInsert(
                        opt.AssessmentDrawings,
                        assessmentDto.AssessmentId,
                        opt.ComplianceOptionsId,
                        jobDto.JobId,
                        _mapper,
                        _unitOfWork);
                }
            }
            else
            {
                // Start processing.
                AssessmentDrawing.StartPdfSplittingProcess(_unitOfWork, assessmentDto.BaselineSimulation, jobDto.FilesInOrder, jobDto.Client.ClientDefault.IncludeDrawingsInReport, jobDto.Client.ClientDefault.StampDrawings);
            }
            
            if (assessmentDto.JobId != Guid.Empty && assessmentDto.JobId != null)
                LogEvent(string.Format("Assessment copied from v{0} to a new job.", string.Format("{0:N2}", assessmentDto.AssessmentProjectDetail.AssessmentVersion)));

            return id;
        }

    }
}