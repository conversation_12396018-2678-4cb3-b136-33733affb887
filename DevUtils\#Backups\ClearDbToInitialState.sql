-- Clear Database to Initial State Script
-- Clears Assessments and Jobs only, preserves Users/Auth, Clients, and Projects
-- Runs in transaction - rolls back if any part fails

SET NOCOUNT ON
SET XACT_ABORT ON

BEGIN TRANSACTION ClearDbToInitialState

BEGIN TRY
    -- Drop constraints that cannot be resolved by reordering
    PRINT 'Dropping foreign key constraints...'

    -- Drop File -> AssessmentComplianceOption constraint
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_File] DROP CONSTRAINT [FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId]
            PRINT 'Successfully dropped: FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId'

    -- Drop Job <-> Assessment circular dependency constraints
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_Job_RSS_Assessment')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_Job_RSS_Assessment'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_Job] DROP CONSTRAINT [FK_RSS_Job_RSS_Assessment]
            PRINT 'Successfully dropped: FK_RSS_Job_RSS_Assessment'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_Job_RSS_Assessment: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_Job_RSS_Assessment'

    -- Drop AssessmentComplianceOption <-> AssessmentComplianceBuilding circular dependency constraints
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_ACO_ReferenceBuilding')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_ACO_ReferenceBuilding'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_AssessmentComplianceOption] DROP CONSTRAINT [FK_RSS_ACO_ReferenceBuilding]
            PRINT 'Successfully dropped: FK_RSS_ACO_ReferenceBuilding'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_ACO_ReferenceBuilding: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_ACO_ReferenceBuilding'

    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_ACO_ProposedBuilding')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_ACO_ProposedBuilding'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_AssessmentComplianceOption] DROP CONSTRAINT [FK_RSS_ACO_ProposedBuilding]
            PRINT 'Successfully dropped: FK_RSS_ACO_ProposedBuilding'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_ACO_ProposedBuilding: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_ACO_ProposedBuilding'

    -- Clear tables in proper dependency order (deepest children first)
    PRINT 'Clearing data from tables in dependency order...'

    -- 1. Clear deepest child tables first (no dependencies on other tables being cleared)
    DELETE FROM [dbo].[RSS_Zone]
    PRINT 'Cleared RSS_Zone'

    -- 2. Clear tables that depend on AssessmentComplianceOption but not on AssessmentComplianceBuilding
    DELETE FROM [dbo].[RSS_AssessmentDrawing]
    PRINT 'Cleared RSS_AssessmentDrawing'

    -- 3. Clear AssessmentComplianceBuilding (depends on AssessmentComplianceOption)
    DELETE FROM [dbo].[RSS_AssessmentComplianceBuilding]
    PRINT 'Cleared RSS_AssessmentComplianceBuilding'

    -- 4. Clear AssessmentComplianceOption (now safe after AssessmentComplianceBuilding is cleared)
    DELETE FROM [dbo].[RSS_AssessmentComplianceOption]
    PRINT 'Cleared RSS_AssessmentComplianceOption'

    -- 5. Clear other assessment-related tables
    DELETE FROM [dbo].[RSS_AssessmentProjectDetail]
    PRINT 'Cleared RSS_AssessmentProjectDetail'

    -- 6. Clear job-related tables
    DELETE FROM [dbo].[RSS_JobEvent]
    PRINT 'Cleared RSS_JobEvent'

    -- 7. Clear Assessment (depends on Job, but Job.CurrentAssessmentId constraint is dropped)
    DELETE FROM [dbo].[RSS_Assessment]
    PRINT 'Cleared RSS_Assessment'

    -- 8. Clear Job last (now safe after Assessment is cleared)
    DELETE FROM [dbo].[RSS_Job]
    PRINT 'Cleared RSS_Job'

    IF OBJECT_ID('[dbo].[RSS_AssessmentSoftwareComplianceMethod]', 'U') IS NOT NULL
        DELETE FROM [dbo].[RSS_AssessmentSoftwareComplianceMethod]

    -- Clear file-related data (preserve files linked to projects)
    DELETE FROM [dbo].[RSS_FileVersion] WHERE [FileId] IN (
        SELECT [FileId] FROM [dbo].[RSS_File]
        WHERE ([JobId] IS NOT NULL OR [AssessmentId] IS NOT NULL)
           OR ([JobId] IS NULL AND [AssessmentId] IS NULL AND [FileId] NOT IN (
               SELECT DISTINCT [LogoFileId] FROM [dbo].[RSS_Project] WHERE [LogoFileId] IS NOT NULL
           ))
    )
    DELETE FROM [dbo].[RSS_File] WHERE ([JobId] IS NOT NULL OR [AssessmentId] IS NOT NULL)
       OR ([JobId] IS NULL AND [AssessmentId] IS NULL AND [FileId] NOT IN (
           SELECT DISTINCT [LogoFileId] FROM [dbo].[RSS_Project] WHERE [LogoFileId] IS NOT NULL
       ))

    -- Clear template and reference data
    DELETE FROM [dbo].[RSS_ServiceTemplate]
    DELETE FROM [dbo].[RSS_OpeningTemplate]
    DELETE FROM [dbo].[RSS_SurfaceTemplate]

    -- Clear reference data
    DELETE FROM [dbo].[RSS_Colour]
    DELETE FROM [dbo].[RSS_Manufacturer]

    -- Recreate the constraints that were dropped
    PRINT 'Recreating foreign key constraints...'

    -- Recreate File -> AssessmentComplianceOption constraint
    ALTER TABLE [dbo].[RSS_File] WITH CHECK ADD CONSTRAINT [FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId]
        FOREIGN KEY([ComplianceOptionId]) REFERENCES [dbo].[RSS_AssessmentComplianceOption]([ComplianceOptionsId])
    ALTER TABLE [dbo].[RSS_File] CHECK CONSTRAINT [FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId]
    PRINT 'Recreated: FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId'

    -- Recreate Job <-> Assessment circular dependency constraint
    ALTER TABLE [dbo].[RSS_Job] WITH CHECK ADD CONSTRAINT [FK_RSS_Job_RSS_Assessment]
        FOREIGN KEY([CurrentAssessmentId]) REFERENCES [dbo].[RSS_Assessment]([AssessmentId])
    ALTER TABLE [dbo].[RSS_Job] CHECK CONSTRAINT [FK_RSS_Job_RSS_Assessment]
    PRINT 'Recreated: FK_RSS_Job_RSS_Assessment'

    -- Recreate AssessmentComplianceOption <-> AssessmentComplianceBuilding circular dependency constraints
    ALTER TABLE [dbo].[RSS_AssessmentComplianceOption] WITH CHECK ADD CONSTRAINT [FK_RSS_ACO_ReferenceBuilding]
        FOREIGN KEY([ReferenceBuildingId]) REFERENCES [dbo].[RSS_AssessmentComplianceBuilding]([AssessmentComplianceBuildingId])
    ALTER TABLE [dbo].[RSS_AssessmentComplianceOption] CHECK CONSTRAINT [FK_RSS_ACO_ReferenceBuilding]
    PRINT 'Recreated: FK_RSS_ACO_ReferenceBuilding'

    ALTER TABLE [dbo].[RSS_AssessmentComplianceOption] WITH CHECK ADD CONSTRAINT [FK_RSS_ACO_ProposedBuilding]
        FOREIGN KEY([ProposedBuildingId]) REFERENCES [dbo].[RSS_AssessmentComplianceBuilding]([AssessmentComplianceBuildingId])
    ALTER TABLE [dbo].[RSS_AssessmentComplianceOption] CHECK CONSTRAINT [FK_RSS_ACO_ProposedBuilding]
    PRINT 'Recreated: FK_RSS_ACO_ProposedBuilding'

    -- Give "<EMAIL>" all roles
    INSERT INTO [dbo].[AspNetUserRoles]
    ([UserId], [RoleId])
    SELECT 'C65BA6BD-2D80-4BEE-B213-6ACFA0016C15', [role].[Id]
    FROM [dbo].[AspNetRoles] [role]
    WHERE NOT EXISTS (
        SELECT 1 
        FROM [dbo].[AspNetUserRoles] [userRole]
        WHERE [userRole].[UserId] = 'C65BA6BD-2D80-4BEE-B213-6ACFA0016C15'
          AND [userRole].[RoleId] = [role].[Id]
    );



    COMMIT TRANSACTION ClearDbToInitialState
    PRINT 'Database cleanup completed - Assessment and job data cleared.'

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0
        ROLLBACK TRANSACTION ClearDbToInitialState

    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY()
    DECLARE @ErrorState INT = ERROR_STATE()
    DECLARE @ErrorNumber INT = ERROR_NUMBER()
    DECLARE @ErrorProcedure NVARCHAR(128) = ERROR_PROCEDURE()
    DECLARE @ErrorLine INT = ERROR_LINE()

    PRINT 'ERROR: Database cleanup failed and rolled back!'
    PRINT 'Error Number: ' + CAST(@ErrorNumber AS NVARCHAR(10))
    PRINT 'Error Line: ' + CAST(@ErrorLine AS NVARCHAR(10))
    PRINT 'Error Severity: ' + CAST(@ErrorSeverity AS NVARCHAR(10))
    PRINT 'Error State: ' + CAST(@ErrorState AS NVARCHAR(10))
    IF @ErrorProcedure IS NOT NULL
        PRINT 'Error Procedure: ' + @ErrorProcedure
    PRINT 'Error Message: ' + @ErrorMessage

    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState)
END CATCH
