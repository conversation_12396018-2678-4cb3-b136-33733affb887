<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <!-- Keep these both here. The first is used while debugging in a browser window -->
    <link rel="stylesheet" href="template-styles.css">
    <link rel="stylesheet" href="$assessmentDetails.StyleSheetLocation">

    <style type="text/css">

        /* Site Page Stuff ------------------------------------------------*/
        /* Our PDF gen lib doesn't support the object-fit CSS property so here we are. */
        .cropped-site-map {
            background-image: url($assessmentDetails.SiteMapImageUrl);
            background-position: center center;
            background-repeat: no-repeat;
            background-size: cover;
        }

        .cropped-map {
            background-image: url($assessmentDetails.MapImageUrl);
            background-position: center center;
            background-repeat: no-repeat;
            background-size: cover;
        }



    </style>
</head>

<!-- NVelocity Values -->
#set($defaultIfNull = "Not Applicable")

<!-- Difference between Compliance Options:
    1) House Energy Rating (HER) && 2) Performance Solution (Comparison with DTS-HER)
        : Show Simulation Results (simple tables) and Executive Summary section with House Energy Rating
    3) Elemental Provisions (EP)
        : Hide Simulation Results completely
    4) Performance Solution (Comparison with DTS-EP) && 5) Performance Solution (VURB)
        : Show Simulation Results (advanced tables) and Executive Summary section without House Energy Rating

    IsHouseEnergyRating()
    IsNonHousePerformanceSolution()
    IsElementalProvision()
-->

<body style="width: 800px;">

<!-- Page - Cover -->
<div class="cover-page"
     style="page-break-inside: avoid; padding: 0 0 0 0;">

    <div style="height: 340px;">
        <!-- Blank, filled in via stamping method -->
    </div>


    <div style="height: 720px;">

        <!-- Document Title -->
        <div class="cover-header">
            <div class="cover-header-identifier">DOCUMENT</div>
            <div class="cover-header-title">Energy Efficiency<br/>Compliance Report</div>
        </div>

        <table style="width: 100%; float: bottom; ">
            <tbody>

            <tr>
                <!-- Left Hand Column Info -->
                <td class="cover-project-data"
                    valign="bottom">

                    <div class="project-data-item">
                        <div>PROJECT DESCRIPTION</div>
                        <div>$assessmentDetails.WorksDescription ($assessmentDetails.ProjectDescription)</div>
                    </div>

                    <div class="project-data-item">
                        <div>BUILDING CLASSIFICATION</div>
                        <div>$assessmentDetails.ProjectClassification</div>
                    </div>

                    <div class="project-data-item">
                        <div>PROJECT ADDRESS</div>
                        <div>$assessmentDetails.FinalAddressMultiLine</div>
                    </div>

                    <div class="project-data-item">
                        <div>OWNER</div>
                        <div>$assessmentDetails.Owner</div>
                    </div>

                    <div class="project-data-item">
                        <div>LOCAL GOVERNMENT AUTHORITY</div>
                        <div>$assessmentDetails.LocalGovernmentAuthority</div>
                    </div>

                    <div class="project-data-item">
                        <div>CLIENT</div>
                        <div>$assessmentDetails.ClientName</div>
                    </div>

                    <div class="project-data-item"
                         style="margin-bottom: 0;">
                        <div>JOB NUMBER</div>
                        <div>$assessmentDetails.JobNumber</div>
                    </div>

                </td>

                <!-- Right Hand Column Info -->
                <td style="width: 100%;" valign="bottom">
                    <div class="document-info">
                        <div class="document-data-item">
                            <div>DOCUMENT ID</div>
                            <div>$assessmentDetails.ReportCertificateNumber</div>
                        </div>

                        <div class="document-data-item">
                            <div>PREPARED BY</div>
                            <div>$assessmentDetails.ReportAssessorName</div>
                        </div>

                        <div class="document-data-item">
                            <div>VERSION</div>
                            <div>$assessmentDetails.DocumentVersion</div>
                        </div>

                        <div class="document-data-item"
                             style="margin-bottom: 0;">
                            <div>DATE</div>
                            <div>$assessmentDetails.ReportCertificateDate</div>
                        </div>

                    </div>
                </td>
            </tr>

            </tbody>
        </table>

    </div>

    <div style="height: 130px;">
        <!-- Blank, filled in via stamping method -->
    </div>

</div>

<!-- Page - Executive Summary -->
<div style="page-break-before: always; clear: both;">

    <h1>Executive Summary</h1>

    <!-- Project Details -->
    <div class="tabular-summary-block">
        <h2>Project Details</h2>
        <hr/>
        <p><span>Client:</span><span>$assessmentDetails.ClientName</span></p>
        <p><span>Client Job Number:</span><span>$assessmentDetails.JobNumber</span></p>
        <p><span>Project Description:</span><span>$assessmentDetails.WorksDescription ($assessmentDetails.ProjectDescription)</span></p>
        <p><span>NCC Building Classification:</span><span>$assessmentDetails.ProjectClassification</span></p>
        <p><span>Project Owner:</span><span>$assessmentDetails.Owner</span></p>
        #if($assessmentDetails.NominatedBuildingSurveyorName && $assessmentDetails.NominatedBuildingSurveyorName != "To Be Confirmed")
        <p><span>Nominated Building Surveyor:</span><span>$assessmentDetails.NominatedBuildingSurveyorName</span></p>
        #end
    </div>

    <!-- Site Information -->
    <div class="tabular-summary-block">
        <h2>Site Information</h2>
        <hr/>
        <p>
            <span>Project Address:</span><span>$assessmentDetails.FinalAddressSingleLine</span>
        </p>
        <p><span>Local Government Authority:</span><span>$assessmentDetails.LocalGovernmentAuthority</span></p>
        <p>
            <span>GPS Coordinates:</span><span><a href="https://maps.google.com/?q=$assessmentDetails.Latitude,$assessmentDetails.Longitude" target="_blank">$assessmentDetails.Latitude, $assessmentDetails.Longitude</a></span>
        </p>
        #if($assessmentDetails.HasParcelArea)
        <p><span>Parcel Area:</span><span>$assessmentDetails.ParcelArea m<sup>2</sup></span></p>
        #end
    </div>

    <!-- Climate Data -->
    <div class="tabular-summary-block">
        <h2>Climate Data</h2>
        <hr/>
        <p><span>NCC Climate Zone:</span><span>$assessmentDetails.NCCClimateZone</span></p>
        <p><span>NatHERS Climate Zone:</span><span>$assessmentDetails.NatHERSClimateZone</span></p>
        <p><span>Climate Zone Description:</span><span>$assessmentDetails.NCCClimateZoneDescription</span></p>
    </div>

    <!-- Assessment Scope -->
    <div>
        <h2>Assessment Scope</h2>
        <hr/>
        <p>
            This assessment examines the thermal performance of the proposed building's fabric and the energy use of the
            proposed building's domestic services to determine if the energy efficiency Performance Requirements specified in
            Part $assessmentDetails.H6Or26() of the $assessmentDetails.Certification were satisfied.
        </p>
    </div>

    <!-- Assessment Outcome -->
    <div>
        <h2>Assessment Outcome</h2>
        <hr/>
        <p>If constructed in accordance with the specifications and additional requirements provided in this assessment:</p>
        <ul class="roman">
            #if($assessmentDetails.IsComplianceMethod("CMPerfSolution"))
            <li>Performance Requirement $assessmentDetails.H6P1OrP261() is satisfied using the Verification Using a Reference Building method (Performance Solution) prescribed in the attached Performance-Based Design Brief (PBDB); and</li>
            #elseif($assessmentDetails.IsComplianceMethod("CMPerfSolutionDTS"))
            <li>Performance Requirement $assessmentDetails.H6P1OrP261() is satisfied using a Comparison with the Deemed-to-Satisfy Provisions (Performance Solution) prescribed in the attached Performance-Based Design Brief (PBDB); and</li>
            #elseif($assessmentDetails.IsComplianceMethod("CMPerfSolutionHER"))
            <li>Performance Requirement $assessmentDetails.H6P1OrP261() is satisfied using a Comparison with the Deemed-to-Satisfy Provisions (Performance Solution) prescribed in the attached Performance-Based Design Brief (PBDB); and</li>
            #elseif($assessmentDetails.IsComplianceMethod("CMHouseEnergyRating"))
            <li>Performance Requirement $assessmentDetails.H6P1OrP261() is satisfied using the Deemed-to-Satisfy House Energy Rating (HER) method specified in $assessmentDetails.H6D21AOr3120ai(); and</li>
            #elseif($assessmentDetails.IsComplianceMethod("CMPerfELL"))
            <li>Performance Requirement $assessmentDetails.H6P1OrP261() is satisfied by complying with the Energy Load Limits (Performance Solution) prescribed in the attached Performance-Based Design Brief (PBDB); and</li>
            #elseif($assessmentDetails.IsComplianceMethod("CMElementalProv"))
            <li>Performance Requirement $assessmentDetails.H6P1OrP261() is satisfied using the Deemed-to-Satisfy Elemental Provisions (EP) method specified in $assessmentDetails.H6D21BOr3120aii(); and</li>
            #elseif($assessmentDetails.IsComplianceMethod("CMPerfWAProtocolEP"))
            <li>Performance Requirement $assessmentDetails.H6P1OrP261() is satisfied using the WA Alterations & Additions Protocol – EP Option (Performance Solution) prescribed in the attached Performance-Based Design Brief (PBDB); and</li>
            #elseif($assessmentDetails.IsComplianceMethod("CMPerfWAProtocolHER"))
            <li>Performance Requirement $assessmentDetails.H6P1OrP261() is satisfied using the WA Alterations & Additions Protocol – HERS Option (Performance Solution) prescribed in the attached Performance-Based Design Brief (PBDB); and</li>
            #end

            #if($assessmentDetails.IsNcc2022Certification() && $assessmentDetails.IsComplianceMethod("CMHouseEnergyRating"))
            <li>Performance Requirement $assessmentDetails.H6P2OrP262() is satisfied using the Deemed-to-Satisfy House Energy Rating (HER) method specified in $assessmentDetails.H6D22BOr3120b().</li>
            #else
            <li>Performance Requirement $assessmentDetails.H6P2OrP262() is satisfied using the Deemed-to-Satisfy Elemental Provisions (EP) method specified in $assessmentDetails.H6D22BOr3120b().</li>
            #end
        </ul>
    </div>

    <!-- Assessment Notes -->
    #if($assessmentDetails.AssessmentNotes() != "")
    <div>
        <h2>Assessment Notes</h2>
        <hr/>
        <p>$assessmentDetails.AssessmentNotes()</p>
    </div>
    #end

    <table style="width: 100%; page-break-inside: avoid;">
        <tbody>
        <tr>
            <td>
                <!-- Calculated Building Floor Areas -->
                <div>
                    <h2 class="pdf-bookmark-here">Calculated Building Floor Areas</h2>
                    <hr/>
                    <table class="summary-table" show-reference="$assessmentDetails.ShowReferenceFloorAreaStr" is-ep="$assessmentDetails.IsElementalProvisionStr">
                        <tbody>
                        #if(!$assessmentDetails.IsHouseEnergyRating && !$assessmentDetails.IsElementalProvision)
                        <tr>
                            <th></th>
                            <th>Proposed Building</th>
                            #if($assessmentDetails.ShowReferenceFloorArea)
                            <th>
                                $assessmentDetails.AnnualEnergyLoadsReferenceHeader
                            </th>
                            #end
                        </tr>
                        #end
                        <tr>
                            <td>Conditioned (m<sup>2</sup>)</td>
                            <td>$assessmentDetails.ProposedConditionedFloorArea</td>
                            #if($assessmentDetails.ShowReferenceFloorArea)
                            <td>$assessmentDetails.ReferenceConditionedFloorArea</td>
                            #end
                        </tr>
                        <tr>
                            <td>Unconditioned (m<sup>2</sup>)</td>
                            <td>$assessmentDetails.ProposedUnconditionedFloorArea</td>
                            #if($assessmentDetails.ShowReferenceFloorArea)
                            <td>$assessmentDetails.ReferenceUnconditionedFloorArea</td>
                            #end
                        </tr>
                        <tr>
                            <td>Attached Garage (m<sup>2</sup>)</td>
                            <td>$assessmentDetails.ProposedAttachedGarageFloorArea</td>
                            #if($assessmentDetails.ShowReferenceFloorArea)
                            <td>$assessmentDetails.ReferenceAttachedGarageFloorArea</td>
                            #end
                        </tr>
                        <tr>
                            <td>Total (m<sup>2</sup>)</td>
                            <td>$assessmentDetails.ProposedTotalFloorArea</td>
                            #if($assessmentDetails.ShowReferenceFloorArea)
                            <td>$assessmentDetails.ReferenceTotalFloorArea</td>
                            #end
                        </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Calculated Annual Energy Loads -->
                #if(!$assessmentDetails.IsElementalProvision)
                <div>
                    <h2>Calculated Annual Energy Loads</h2>
                    <hr/>

                    <!-- Calculated Annual Energy Loads Table -->
                    <table class="summary-table" show-reference="$assessmentDetails.ShowReferenceEnergyStr">
                        <tbody>
                            #if($assessmentDetails.ShowReferenceEnergy)
                            <tr>
                                <th></th>
                                <th>
                                    $assessmentDetails.AnnualEnergyLoadsProposedHeader
                                </th>
                                <th>
                                    $assessmentDetails.AnnualEnergyLoadsReferenceHeader
                                </th>
                            </tr>
                            #end
                            <tr>
                                <td>Heating&nbsp;(${assessmentDetails.FinalEnergyLoadUnits})</td>
                                <td>$assessmentDetails.ProposedHeating</td>
                                #if($assessmentDetails.ShowReferenceEnergy)
                                <td>$assessmentDetails.ReferenceHeating</td>
                                #end
                            </tr>
                            <tr>
                                <td>Cooling&nbsp;(${assessmentDetails.FinalEnergyLoadUnits})</td>
                                <td>$assessmentDetails.ProposedCooling</td>
                                #if($assessmentDetails.ShowReferenceEnergy)
                                <td>$assessmentDetails.ReferenceCooling</td>
                                #end
                            </tr>
                            <tr>
                                <td>
                                    #if($assessmentDetails.IsNcc2022Certification() && $assessmentDetails.IsComplianceMethod("CMPerfELL"))
                                    Thermal
                                    #else
                                    Total
                                    #end
                                    &nbsp;(${assessmentDetails.FinalEnergyLoadUnits})
                                </td>
                                <td>$assessmentDetails.ProposedTotalThermalPerformanceLoads</td>
                                #if($assessmentDetails.ShowReferenceEnergy)
                                <td>$assessmentDetails.ReferenceTotalThermalPerformanceLoads</td>
                                #end
                            </tr>
                        </tbody>
                    </table>
                </div>
                #end

                <!-- Calculated House Energy Rating -->
                #if($assessmentDetails.IsComplianceMethod("CMHouseEnergyRating") || $assessmentDetails.IsComplianceMethod("CMPerfSolutionHER"))

                <div class="tabular-summary-block">
                    <h2>Calculated House Energy Rating</h2>
                    <hr/>
                    <table class="summary-table">
                        <tbody>
                            <tr>
                                <td>
                                    House Energy Rating
                                </td>
                                <td>
                                    $assessmentDetails.ProposedHouseEnergyRating Stars
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                #end
            </td>

            #if(!($assessmentDetails.IsComplianceMethod("CMElementalProv") || $assessmentDetails.IsCompliancemethod("CMPerfWAProtocolEP")))
            <td style="width: 50%">
                <!-- Dummy to fill in table -->
                <div style="opacity: 0;">

                </div>
            </td>
            #end
        </tr>
        </tbody>
    </table>

</div>

<!-- Page - Specification Summary -->
<div style="page-break-before: always; clear: both;">

    <h1>Specification Summary</h1>

    <p>
        A summary of the required specifications is provided below. Please refer to the Assessment Details and
        Proposed Building Specifications sections for additional compliance requirements and detailed specifications.
    </p>

    <!-- Construction -->
    #if($assessmentDetails.BuildingHasSurfaces($assessmentDetails.ProposedBuilding))
    <div class="specification-summary-section">
        <h2>Construction</h2>
        <hr/>
        <table class="specification-summary-table">
            <tbody>
            #foreach($category in $assessmentDetails.SummaryConstructionCategories())
            <!--                    %foreach($category in @assessmentDetails.ConstructionTabCategories())-->
                #if($assessmentDetails.ConstructionsGroupedForCategoryHasItems($assessmentDetails.ProposedBuilding, $category))
                <tr>
                    <td>
                        $assessmentDetails.FormattedSummaryCategoryTitle($category.Title, $category.ConstructionCategoryCode)
                    </td>
                    <td>
                        <!-- Grouped versions... originally asked for then asked to change, keep incase needs to be changed back -->

                        #foreach($construction in $assessmentDetails.ConstructionsGroupedForCategory($assessmentDetails.ProposedBuilding, $category))
                        <div>
                            <span>$assessmentDetails.FinalSummaryDescriptionWithExtraDetails($construction)</span>
                       </div>
                        #end
                        <!-- NON-Grouped versions... kept in case mind is changed again -->
                        <!-- #if(!$assessmentDetails.CategoryHasItems($assessmentDetails.ProposedBuilding, $category))
                            -
                        #else
                            #foreach($construction in $assessmentDetails.ConstructionsForCategory($assessmentDetails.ProposedBuilding, $category))
                                <div>
                                    $assessmentDetails.FinalDescription($construction)
                                    #if($assessmentDetails.IncludeInsulation($category))
                                        - $assessmentDetails.InsulationDescription($construction)
                                    #end
                                </div>
                            #end
                        #end   -->
                    </td>
                </tr>
                #end
            #end
            </tbody>
        </table>
    </div>
    #end

    <!-- Openings -->
    #if($assessmentDetails.BuildingHasOpenings($assessmentDetails.ProposedBuilding))
    <div  class="specification-summary-section">
        <h2>Openings</h2>
        <hr/>
        <table class="specification-summary-table">
            <tbody>
            #foreach($category in $assessmentDetails.SummaryOpeningsCategories())

                #if($assessmentDetails.CategoryHasItems($assessmentDetails.ProposedBuilding, $category))
                <tr>
                    <td>
                        $assessmentDetails.FormattedCategoryTitle($category.Title, $category.ConstructionCategoryCode)
                    </td>
                    <td>
                        #foreach($construction in $assessmentDetails.ConstructionsGroupedForCategory($assessmentDetails.ProposedBuilding, $category))
                        <div>
                            <span>$assessmentDetails.FinalDescription($construction)</span>#if($assessmentDetails.IsAnOpening($category) == true)<span>, U = $assessmentDetails.Round2DP($assessmentDetails.FinalUValue($construction)) &amp; SHGC = $assessmentDetails.Round2DP($assessmentDetails.FinalSHGC($construction))</span>#end
                        </div>
                        #end
                    </td>
                </tr>
                #end
            #end
            </tbody>
        </table>
    </div>
    #end

    <!-- Services -->
    #if($assessmentDetails.BuildingHasServices($assessmentDetails.ProposedBuilding))
    <div  class="specification-summary-section">
        <h2>Services</h2>
        <hr/>
        <table class="specification-summary-table">
            <tbody>
            #foreach($category in $assessmentDetails.SummaryServicesCategories())

                #if($assessmentDetails.ServiceCategoryHasItems($assessmentDetails.ProposedBuilding, $category))
                <tr>
                    <td>
                        $assessmentDetails.FormattedCategoryTitle($category.Title, $category.ConstructionCategoryCode)
                    </td>
                    <td>
                        #foreach($construction in $assessmentDetails.ServicesForCategory($assessmentDetails.ProposedBuilding, $category))
                        <div>
                            <span>$assessmentDetails.FinalDescription($construction)</span>#if($assessmentDetails.IsAnOpening($category) == true)<span>, U = $assessmentDetails.Round2DP($assessmentDetails.FinalUValue($construction)) &amp; SHGC = $assessmentDetails.Round2DP($assessmentDetails.FinalSHGC($construction))</span>#end
                        </div>
                        #end
                        <!-- Cooking -->
                        #if($category.ServiceCategoryCode == 'Cooking')
                            <span>$category.Description</span>
                        #end
                    </td>
                </tr>
                #end
            #end
            </tbody>
        </table>
    </div>
    #end

    <!-- "Other" -->
    <div class="specification-summary-section">
        <h2>Other</h2>
        <hr/>
        <table class="specification-summary-table">
            <tbody>

            <tr>
                <td>Thermal Insulation</td>
                <td>
                    Building fabric thermal insulation must comply with $assessmentDetails.N1322Or31211().
                </td>
            </tr>
            #if($assessmentDetails.IsElementalProvision)
            <tr>
                <td>Ceiling Fans</td>
                <td>
                    #if($assessmentDetails.IsNcc2022Certification())
                    Ceiling fans must be installed in accordance with 13.5.2.
                    #else
                    Ceiling fans must be installed in accordance with ********.
                    #end
                </td>
            </tr>
            #end
            <tr>
                <td>Exhaust Fans</td>
                <td>
                    Exhaust fans must be fitted with a sealing device.
                </td>
            </tr>
            <tr>
                <td>Recessed Light Fittings</td>
                <td>
                    Recessed light fittings must be sealed and IC rated in accordance with AS/NZS 60598.2.2:2016.
                </td>
            </tr>
            #if($assessmentDetails.CategoryHasServices($assessmentDetails.ProposedBuilding, 'CeilingFans'))
                <tr>
                    <td>Ceiling Fans</td>
                    #foreach($service in $assessmentDetails.ServicesInCategory($assessmentDetails.ProposedBuilding, 'CeilingFans'))
                        <td>$assessmentDetails.FinalDisplayDescription($service), $assessmentDetails.BladeDiameter($service)mm blade diameter, permanently installed with a speed controller.</td>
                    #end
                </tr>
            #end

            </tbody>
        </table>
    </div>
</div>

<!-- Page - Simulation Results -->
#if(!$assessmentDetails.IsElementalProvision)

<div style="page-break-before: always; clear: both;">

    <h1>Simulation Results</h1>

    <!-- These are headings with white-space below for charts to be stamped on after. -->
    <div style="height: $assessmentDetails.SimulationResultsChartHeight; page-break-inside: avoid;">
        <h2 class="pdf-bookmark-here" style="opacity: 0%;">Building Energy Use</h2>
    </div>

    <div style="height: $assessmentDetails.SimulationResultsChartHeight; page-break-inside: avoid;">
        <h2 class="pdf-bookmark-here" style="opacity: 0%;">Zone Energy Use (Total)</h2>
    </div>

    <div style="height: $assessmentDetails.SimulationResultsChartHeight; page-break-inside: avoid;">
        <h2 class="pdf-bookmark-here" style="opacity: 0%;">Zone Energy Use (Per Conditioned Floor Area)</h2>
    </div>

</div>
#end

<!-- Page - Building Zones -->
<div style="page-break-before: always; clear: both; width: 98%; max-width: 98%;">

    <h1>Building Zones</h1>

    <h2>Zone Summary</h2>

    <hr/>

    <br/>

    <table class="specification-table zone-summary-table" style="border: white; width: 98%; max-width: 98%; ">

        <!-- Grouped Header-->
        <thead>

            <tr>
                <th style="width: 70px;"><div style="text-align:center; color:#757575; font-size:8px;">Storey</div></th>
                <th style="width: 70px;"><div style="text-align:center; color:#757575; font-size:8px;">Zone Name</div></th>
                <th style="width: 70px;"><div style="text-align:center; color:#757575; font-size:8px;">Floor Area<br />(m<sup>2</sup>)</div></th>
                <th style="width: 70px;"><div style="text-align:center; color:#757575; font-size:8px;">Volume<br />(m<sup>3</sup>)</div></th>
                <th style="width: 70px;"><div style="text-align:center; color:#757575; font-size:8px;">Exterior Wall<br />(m<sup>2</sup>)</div></th>
                <th style="width: 70px;"><div style="text-align:center; color:#757575; font-size:8px;">Exterior Glazing<br />(m<sup>2</sup>)</div></th>
                <th style="width: 70px;"><div style="text-align:center; color:#757575; font-size:8px; line-height: 14px; margin-bottom: -2px;">Glass-Exterior Wall<br />(%)</div></th>
                <th style="width: 70px;"><div style="text-align:center; color:#757575; font-size:8px; line-height: 14px; margin-bottom: -2px;">Glass-Floor Area<br />(%)</div></th>
                <th style="width: 70px;"><div style="text-align:center; color:#757575; font-size:8px; line-height: 14px; margin-bottom: -2px;">Max. Illumination<br />Power Load (W)</div></th>

            </tr>

        </thead>

        #set ($storeysList = $assessmentDetails.InteriorZoneBuildingGroupSummary())
        #set ($storeyIndex = 1)
        #foreach ($storeyGroup in $storeysList)
        <tbody>

            <!-- Groups -->
            #set ($zoneIndex = 0)
            #foreach ($row in $storeyGroup.Zones)
            <tr>

                <!-- Group -->
                #if ($zoneIndex == 0)
                <td rowspan="$storeyGroup.Zones.Count" style="text-align: left; font-size: 10px; border-bottom: 1px solid #ADADAD; ">$storeyGroup.groupName</td>
                #end

                <!-- Description -->
                <td style="text-align: left; font-size: 10px; padding: 6px;">$row.zoneDescription</td>

                <!-- Floor Area m2 -->
                <td style="text-align: center; font-size: 10px;">$row.floorArea.ToString("N2")</td>

                <!-- Volume m2 -->
                <td style="text-align: center; font-size: 10px;">$row.volume.ToString("N2")</td>

                <!-- Exterior wall area m2 -->
                <td style="text-align: center; font-size: 10px;">$row.exteriorWallArea.ToString("N2")</td>

                <!-- Exterior Glazing area m2 -->
                <td style="text-align: center; font-size: 10px;">$row.exteriorGlazingArea.ToString("N2")</td>

                <!-- Glass Exterior % -->
                <td style="text-align: center; font-size: 10px;">$row.glassExteriorWallAreaPercent.ToString("N2")</td>

                <!-- Glass Exterior Floor % -->
                <td style="text-align: center; font-size: 10px;">$row.glassFloorAreaPercent.ToString("N2")</td>

                <!-- Max. Illumination Power Load (W) -->
                #if (!$row.lampPowerMaximumW)
                <td style="text-align: center; font-size: 10px;">-</td>
                #else
                <td style="text-align: center; font-size: 10px;">$row.lampPowerMaximumW.ToString("N2")</td>
                #end

            </tr>
            #set ($zoneIndex = $zoneIndex + 1)
            #end

        </tbody>
        #set ($storeyIndex = $storeyIndex + 1)
        #end

    </table>

    <br/>
    <br/>

    <h1 style="page-break-before: always;">Facade Analysis</h1>

    <h2>Whole Building</h2>
    <hr/>
    <table style="margin-top: 45px;">
        <tr>
            #set ($sectorsData = $assessmentDetails.EnvelopeSummary('wholeBuilding'))
            <td>
                <table class="specification-table facade-summary-table" style="border:white; width:450px;">

                    <thead>
                        <tr>
                            <th style="width: 100px;" />
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('wholeBuilding', $sectorKey))
                            <th style="text-align:center; color:#757575; font-size: 8px">$assessmentDetails.GetSectorDisplayLabel($sectorKey, 'wholeBuilding')</th>
                            #end
                            #end
                        </tr>
                    </thead>
                    <tbody class="border-last">
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Exterior Wall Area (m<sup>2</sup>)</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('wholeBuilding', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                $assessmentDetails.GetWallAreaSectorData('wholeBuilding', $sectorKey).Area.ToString("N2")
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Exterior Wall Area (%)</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('wholeBuilding', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                $assessmentDetails.GetWallAreaSectorData('wholeBuilding', $sectorKey).Percentage.ToString("N2")
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Exterior Glazing Area (m<sup>2</sup>)</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('wholeBuilding', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                $assessmentDetails.GetGlazingAreaSectorData('wholeBuilding', $sectorKey).Area.ToString("N2")
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Exterior Glazing Area (%)</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('wholeBuilding', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                $assessmentDetails.GetGlazingAreaSectorData('wholeBuilding', $sectorKey).Percentage.ToString("N2")
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Glass-Exterior Wall Ratio (%)</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('wholeBuilding', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                #if ($assessmentDetails.GetWallRatio('wholeBuilding', $sectorKey))
                                $assessmentDetails.GetWallRatio('wholeBuilding', $sectorKey).ToString("N2")
                                #else
                                N/A
                                #end
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Average Glazing U-Value</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('wholeBuilding', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                #if ($assessmentDetails.GetAvUValue('wholeBuilding', $sectorKey))
                                $assessmentDetails.GetAvUValue('wholeBuilding', $sectorKey).ToString("N2")
                                #else
                                N/A
                                #end
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Average Glazing SHGC</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('wholeBuilding', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                #if ($assessmentDetails.GetAvSHGC('wholeBuilding', $sectorKey))
                                $assessmentDetails.GetAvSHGC('wholeBuilding', $sectorKey).ToString("N2")
                                #else
                                N/A
                                #end
                            </td>
                            #end
                            #end
                        </tr>
                    </tbody>

                </table>
            </td>

            <td>
                <h2 class="pdf-bookmark-here" style="opacity: 0; margin-top: -30px;">Facade Summary Wind Rose Chart</h2>
                <div style="opacity: 0; margin-right: -50px">$sectorsData.WindRoseChartSvg</div>
                <div style="margin-bottom:-60px;"></div>
            </td>

        </tr>
    </table>

    <h2>Conditioned Zones</h2>
    <hr/>
    <table style="margin-top: 45px;">
        <tr>
            #set ($sectorsData = $assessmentDetails.EnvelopeSummary('conditioned'))
            <td>
                <table class="specification-table facade-summary-table" style="border:white; width:450px; margin-top: -50px;">

                    <thead>
                        <tr>
                            <th style="width: 100px;" />
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('conditioned', $sectorKey))
                            <th style="text-align:center; color:#757575; font-size: 8px">$assessmentDetails.GetSectorDisplayLabel($sectorKey, 'conditioned')</th>
                            #end
                            #end
                        </tr>
                    </thead>
                    <tbody class="border-last">
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Exterior Wall Area (m<sup>2</sup>)</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('conditioned', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                $assessmentDetails.GetWallAreaSectorData('conditioned', $sectorKey).Area.ToString("N2")
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Exterior Wall Area (%)</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('conditioned', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                $assessmentDetails.GetWallAreaSectorData('conditioned', $sectorKey).Percentage.ToString("N2")
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Exterior Glazing Area (m<sup>2</sup>)</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('conditioned', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                $assessmentDetails.GetGlazingAreaSectorData('conditioned', $sectorKey).Area.ToString("N2")
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Exterior Glazing Area (%)</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('conditioned', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                $assessmentDetails.GetGlazingAreaSectorData('conditioned', $sectorKey).Percentage.ToString("N2")
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Glass-Exterior Wall Ratio (%)</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('conditioned', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                #if ($assessmentDetails.GetWallRatio('conditioned', $sectorKey))
                                $assessmentDetails.GetWallRatio('conditioned', $sectorKey).ToString("N2")
                                #else
                                N/A
                                #end
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Average Glazing U-Value</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('conditioned', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                #if ($assessmentDetails.GetAvUValue('conditioned', $sectorKey))
                                $assessmentDetails.GetAvUValue('conditioned', $sectorKey).ToString("N2")
                                #else
                                N/A
                                #end
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Average Glazing SHGC</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('conditioned', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                #if ($assessmentDetails.GetAvSHGC('conditioned', $sectorKey))
                                $assessmentDetails.GetAvSHGC('conditioned', $sectorKey).ToString("N2")
                                #else
                                N/A
                                #end
                            </td>
                            #end
                            #end
                        </tr>
                    </tbody>

                </table>
            </td>

            <td>
                <h2 class="pdf-bookmark-here" style="opacity: 0; margin-top: -30px">Facade Summary Conditioned Wind Rose Chart</h2>
                <div style="opacity: 0;">$sectorsData.WindRoseChartSvg</div>
                <div style="margin-bottom:-60px;"></div>
            </td>

        </tr>
    </table>

    <h2>Habitable Zones</h2>
    <hr/>
    <table style="margin-top: 45px;">
        <tr>
            #set ($sectorsData = $assessmentDetails.EnvelopeSummary('habitable'))
            <td>
                <table class="specification-table facade-summary-table" style="border:white; width:450px; margin-top: -50px;">

                    <thead>
                        <tr>
                            <th style="width: 100px;" />
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('habitable', $sectorKey))
                            <th style="text-align:center; color:#757575; font-size: 8px">$assessmentDetails.GetSectorDisplayLabel($sectorKey, 'habitable')</th>
                            #end
                            #end
                        </tr>
                    </thead>
                    <tbody class="border-last">
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Exterior Wall Area (m<sup>2</sup>)</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('habitable', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                $assessmentDetails.GetWallAreaSectorData('habitable', $sectorKey).Area.ToString("N2")
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Exterior Wall Area (%)</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('habitable', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                $assessmentDetails.GetWallAreaSectorData('habitable', $sectorKey).Percentage.ToString("N2")
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Exterior Glazing Area (m<sup>2</sup>)</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('habitable', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                $assessmentDetails.GetGlazingAreaSectorData('habitable', $sectorKey).Area.ToString("N2")
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Exterior Glazing Area (%)</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('habitable', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                $assessmentDetails.GetGlazingAreaSectorData('habitable', $sectorKey).Percentage.ToString("N2")
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Glass-Exterior Wall Ratio (%)</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('habitable', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                #if ($assessmentDetails.GetWallRatio('habitable', $sectorKey))
                                $assessmentDetails.GetWallRatio('habitable', $sectorKey).ToString("N2")
                                #else
                                N/A
                                #end
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Average Glazing U-Value</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('habitable', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                #if ($assessmentDetails.GetAvUValue('habitable', $sectorKey))
                                $assessmentDetails.GetAvUValue('habitable', $sectorKey).ToString("N2")
                                #else
                                N/A
                                #end
                            </td>
                            #end
                            #end
                        </tr>
                        <tr class="border-last">
                            <td style="text-align: left; font-size: 10px; padding: 6px;">Average Glazing SHGC</td>
                            #foreach ($sectorKey in $sectorsData.SectorKeys)
                            #if ($assessmentDetails.IsSectorNotEmpty('habitable', $sectorKey))
                            <td style="text-align: center; font-size: 10px;">
                                #if ($assessmentDetails.GetAvSHGC('habitable', $sectorKey))
                                $assessmentDetails.GetAvSHGC('habitable', $sectorKey).ToString("N2")
                                #else
                                N/A
                                #end
                            </td>
                            #end
                            #end
                        </tr>
                    </tbody>

                </table>
            </td>

            <td>
                <h2 class="pdf-bookmark-here" style="opacity: 0; margin-top: -30px">Facade Summary Habitable Wind Rose Chart</h2>
                <div style="opacity: 0;">$sectorsData.WindRoseChartSvg</div>
                <div style="margin-bottom:-60px;"></div>
            </td>

        </tr>
    </table>

</div>

<!-- Page - Project Details -->
<div style="page-break-before: always; clear: both; width: 100%;">

    <h1>Site Analysis</h1>

    <!-- Site Information (Copy of data from summary page) -->
    <div class="tabular-summary-block" style="margin-bottom: 1rem;">
        <h2>Site Information</h2>
        <hr/>
        <p>
            <span>Project Address:</span><span>$assessmentDetails.FinalAddressSingleLine</span>
        </p>
        <p><span>Local Government Authority:</span><span>$assessmentDetails.LocalGovernmentAuthority</span></p>
        <p>
            <span>GPS Coordinates:</span><span><a href="https://maps.google.com/?q=$assessmentDetails.Latitude,$assessmentDetails.Longitude" target="_blank">$assessmentDetails.Latitude, $assessmentDetails.Longitude</a></span>
        </p>
        #if($assessmentDetails.HasParcelArea)
        <p><span>Parcel Area:</span><span>$assessmentDetails.ParcelArea m<sup>2</sup></span></p>
        #end
    </div>

    <!-- Satellite view - 490px or 505px -->
    <div class="cropped-map"
         style="position:relative; width: 90%; height: 440px; margin-top: 40px; margin-left: auto; margin-right: auto;">
        <img src="/content/images/north-arrow.svg"
             style="position: absolute; right: 15px; bottom: 15px;
                                                    height: 35px; z-index: 9999;" />
    </div>

    <!-- Map view -->
    <!-- Our PDF gen lib doesn't support the object-fit CSS property
        so these CSS classes applied here are a dodgy workaround | width: 440px; height: 440px -->
    <div class="cropped-site-map"
         style="position: relative; width: 90%; height: 440px; margin-top: 50px; margin-left: auto; margin-right: auto;">
        <img src="/content/images/north-arrow.svg"
             style="position: absolute; right: 15px; bottom: 15px; height: 35px; z-index: 9999;" />
    </div>


</div>

<!-- Page  Climate Charts (new) -->
<div style="page-break-before: always; clear: both; width: 100%;">

    <h1 class="pdf-bookmark-here">Climate Analysis</h1>

    <!-- Climate Data -->
    <div class="tabular-summary-block">
        <h2>Climate Data</h2>
        <hr/>
        <p><span>NCC Climate Zone:</span><span>$assessmentDetails.NCCClimateZone</span></p>
        <p><span>NatHERS Climate Zone:</span><span>$assessmentDetails.NatHERSClimateZone</span></p>
        <p><span>Climate Zone Description:</span><span>$assessmentDetails.NCCClimateZoneDescription</span></p>
    </div>

    <table class="chart-position-table" style="height: 1000px; width: 800px; margin-top: 12px;">
        <tr style="height: 0;">
            <th width="50%"></th>
            <th width="50%"></th>
        </tr>
        <!-- Chart titles need to be added here otherwise fonts don't match because of the two
            ways the SVG Charts are being added to this PDF (Long story) -->
        <tr>
            <td>
                <div class="chart-title">Air Temperature</div>
                <div style="opacity: 0;">
                    <!-- Passed in through InsertSvgsOntoExistingPdf() -->
                    $assessmentDetails.MonthlyTempChartSVG
                </div>
            </td>
            <td>
                <div class="chart-title">Air Temperature (by hour)</div>
                <div style="margin-top: -32px;">
                    $assessmentDetails.HeatmapTemperatureChartSVG
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <div class="chart-title">Daily Horizontal Radiation</div>
                <div style="opacity: 0;">
                    <!-- Passed in through InsertSvgsOntoExistingPdf() -->
                    $assessmentDetails.RadiationColumnChartSVG
                </div>
            </td>
            <td>
                <div class="chart-title">Global Horizontal Radiation (by hour)</div>
                <div style="margin-top: -18px;">
                    $assessmentDetails.HeatmapRadiationChartSVG
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <!-- Title needs to be pushed slightly to the left -->
                <div class="chart-title" style="width: 94%;">
                    Wind
                </div>
                <div style="opacity: 0;">
                    <!-- Passed in through InsertSvgsOntoExistingPdf() -->
                    $assessmentDetails.WindroseChartSVG
                </div>
            </td>
            <td>
                <div class="chart-title">Precipitable Water</div>
                <div style="opacity: 0;">
                    <!-- Passed in through InsertSvgsOntoExistingPdf() -->
                    $assessmentDetails.PrecipitableWaterChartSVG
                </div>
            </td>
        </tr>
    </table>

</div>

<!-- Page  Assessment Details - New -->
<div style="page-break-before: always; clear: both; page-break-inside: avoid;">

    <h1>Assessment Details</h1>

    <!-- Scope -->
    <h2>Scope</h2>
    <hr/>
    <p>
        The purpose of this assessment is to verify compliance with the Governing Requirements of the NCC and the energy
        efficiency Performance Requirements specified in Part $assessmentDetails.H6Or26() of the $assessmentDetails.Certification.
    </p>

    <!-- Applicable Performance Requirements -->
    <h2>Applicable Performance Requirements</h2>
    <hr/>
    <div>
        <p>The energy efficiency Performance Requirements comprise of:</p>


        #if($assessmentDetails.IsNcc2022Certification())
        <ul class="roman">
            <li>$assessmentDetails.H6P1OrP261() Thermal performance; and</li>
            <li>$assessmentDetails.H6P2OrP262() Energy usage.</li>
        </ul>

        <p>
            Performance Requirement H6P1 covers the thermal performance of a building's fabric and regulates the maximum
            permitted heating loads, cooling loads and thermal energy loads of buildings. Performance Requirement H6P2
            covers the energy use of a building's domestic services.
        </p>
        #else
        <!-- NCC 2019 and below -->
        <ul class="roman">
            <li>P2.6.1 Building; and</li>
            <li>P2.6.2 Services.</li>
        </ul>

        <p>
            Performance Requirement P2.6.1 relates to the thermal performance of a building that is needed in order to
            facilitate the efficient use of energy for artificial heating and cooling.
        </p>

        <p>
            Performance Requirement P2.6.2 relates to the domestic services and considers facilitating the efficient use
            of energy and constraining the use of a high greenhouse gas intensity source of energy used for heating.
        </p>
        #end

    </div>

    <!-- Methodolgy -->
    <h2>Methodology</h2>
    <hr/>
    <div>
        <p>This assessment will verify compliance with:</p>
        <ul class="roman">

            #if($assessmentDetails.IsComplianceMethod("CMPerfSolution"))
            <li>Performance Requirement $assessmentDetails.H6P1OrP261() using the Verification Using a Reference Building method (Performance Solution) prescribed in the attached Performance-Based Design Brief (PBDB); and</li>
            #elseif($assessmentDetails.IsComplianceMethod("CMPerfSolutionDTS"))
            <li>Performance Requirement $assessmentDetails.H6P1OrP261() using a Comparison with the Deemed-to-Satisfy Provisions (Performance Solution) prescribed in the attached Performance-Based Design Brief (PBDB); and</li>
            #elseif($assessmentDetails.IsComplianceMethod("CMPerfSolutionHER"))
            <li>Performance Requirement $assessmentDetails.H6P1OrP261() using a Comparison with the Deemed-to-Satisfy Provisions (Performance Solution) prescribed in the attached Performance-Based Design Brief (PBDB); and</li>
            #elseif($assessmentDetails.IsComplianceMethod("CMHouseEnergyRating"))
            <li>Performance Requirement $assessmentDetails.H6P1OrP261() using the Deemed-to-Satisfy House Energy Rating (HER) method specified in $assessmentDetails.H6D21AOr3120ai(); and</li>
            #elseif($assessmentDetails.IsComplianceMethod("CMPerfELL"))
            <li>Performance Requirement $assessmentDetails.H6P1OrP261() by complying with the Energy Load Limits (Performance Solution) prescribed in the attached Performance-Based Design Brief (PBDB); and</li>
            #elseif($assessmentDetails.IsComplianceMethod("CMElementalProv"))
            <li>Performance Requirement $assessmentDetails.H6P1OrP261() using the Deemed-to-Satisfy Elemental Provisions (EP) method specified in $assessmentDetails.H6D21BOr3120aii(); and</li>
            #elseif($assessmentDetails.IsComplianceMethod("CMPerfWAProtocolEP"))
            <li>Performance Requirement $assessmentDetails.H6P1OrP261() using the WA Alterations & Additions Protocol – EP Option (Performance Solution) prescribed in the attached Performance-Based Design Brief (PBDB); and</li>
            #elseif($assessmentDetails.IsComplianceMethod("CMPerfWAProtocolHER"))
            <li>Performance Requirement $assessmentDetails.H6P1OrP261() using the WA Alterations & Additions Protocol – HERS Option (Performance Solution) prescribed in the attached Performance-Based Design Brief (PBDB); and</li>
            #end

            #if($assessmentDetails.IsNcc2022Certification() && $assessmentDetails.IsComplianceMethod("CMHouseEnergyRating"))
            <li>Performance Requirement $assessmentDetails.H6P2OrP262() using the Deemed-to-Satisfy House Energy Rating (HER) method specified in $assessmentDetails.H6D22BOr3120b().</li>
            #else
            <li>Performance Requirement $assessmentDetails.H6P2OrP262() using the Deemed-to-Satisfy Elemental Provisions (EP) method specified in $assessmentDetails.H6D22BOr3120b().</li>
            #end
        </ul>
    </div>

    <!-- Limitations -->
    <h2>Limitations</h2>
    <hr/>
    <div>

        #if($assessmentDetails.IsNcc2022Certification())
        <p>
            This assessment is limited to energy efficiency Performance Requirements H6P1 Thermal performance and H6P2
            Energy usage specified in Part H6 of the $assessmentDetails.Certification.
        </p>
        #else
        <p>
            This assessment is limited to energy efficiency Performance Requirements P2.6.1 Building and P2.6.2 Services
            specified in Part 2.6 of the $assessmentDetails.Certification.
        </p>
        #end


        <p>
            The design documentation used for this assessment was supplied by $assessmentDetails.ClientName and is
            attached to this report. The assessment is limited to the information provided in this design documentation.
        </p>

        #if($assessmentDetails.IsNcc2022Certification())
        <p>
            Software tools alone cannot be used to meet certain requirements that are needed for compliance with the energy efficiency Performance Requirements.
            This means that certain additional requirements must also be met.
            Therefore, the proposed building must also comply with-
        </p>

        <ul class="roman">
            <li>
                Section 13 of the ABCB Housing Provisions, clauses-
                <ul>
                    <li>13.2.2 for building fabric thermal insulation; and</li>
                    <li>13.2.3(7) and 13.2.5(5), for thermal breaks; and</li>

                    #if($assessmentDetails.IsVurb)
                    <li>13.2.3(5) for compensating for a loss of ceiling insulation; and</li>
                    #end

                    <li>13.2.6(4), 13.2.6(5) and 13.2.6(6) for floor edge insulation; and</li>
                    <li>13.4 for building sealing; and</li>
                    <li>13.7.2 for insulation of services; and</li>
                    <li>13.7.3 for central heating water piping; and</li>
                    <li>13.7.4 heating and cooling ductwork; and</li>
                    <li>13.7.5 electric resistance space heating; and</li>
                    <li>13.7.6 artificial lighting; and</li>
                    <li>13.7.7 water heater in a heated water supply system; and</li>
                    <li>13.7.8 swimming pool heating and pumping; and</li>
                    #if($assessmentDetails.State == "WA" || $assessmentDetails.State == "SA")
                    <li>13.7.9 spa pool heating and pumping; and</li>
                    #else
                    <li>13.7.9 spa pool heating and pumping.</li>
                    #end

                </ul>
            </li>

            #if($assessmentDetails.State == "WA")
            <li>WA H9D2 for water use efficiency; and</li>
            <li>WA H9D3 for swimming pool covers and blankets; and</li>
            <li>WA H9D4 for heated water use efficiency.</li>
            #elseif($assessmentDetails.State == "SA")
            <li>SA H9D2 for additional water supply; and</li>
            <li>SA H9D3 for rainwater tank capacity; and</li>
            <li>SA H9D4 rainwater tank overflow; and</li>
            <li>SA H9D5 rainwater tank water quality; and</li>
            <li>SA H9D6 rainwater tank stands.</li>
            #end

        </ul>
        #else
        <p>
            As this assessment was completed during the design documentation stage of the project, not all relevant
            construction information was available. In addition to the specifications provided in this assessment,
            the project must be constructed in accordance with the following provisions:
        </p>

        <ul class="roman">
            <li>$assessmentDetails.N1322Or31211() for building fabric thermal insulation; and</li>
            <li>********(c) and ********(d), for thermal breaks; and</li>
            <li>********(e) for compensating for a loss of ceiling insulation; and</li>
            <li>********(c) and ********(d) for floor edge insulation; and</li>
            <li>3.12.3 for building sealing; and</li>
            <li>******** for insulation of services; and</li>
            <li>******** for central heating water piping; and</li>
            <li>******** heating and cooling ductwork; and</li>
            <li>******** electric resistance space heating; and</li>
            <li>******** artificial lighting; and</li>
            <li>******** water heating in a heated water supply system; and</li>
            <li>******** swimming pool heating and pumping; and</li>
            #if($assessmentDetails.State == "WA")
            <li>******** spa pool heating and pumping; and</li>
            <li>WA 2.3.1 for water use efficiency; and</li>
            <li>WA 2.3.2 for swimming pool covers and blankets; and</li>
            <li>WA 2.3.3 for heated water use efficiency.</li>
            #elseif($assessmentDetails.State == "SA")
            <li>SA 2.2.2 for rainwater tank capacity; and</li>
            <li>SA 2.2.3 rainwater tank overflow; and</li>
            <li>SA 2.2.4 rainwater tank water quality; and</li>
            <li>SA 2.2.5 rainwater tank stands.</li>
            #else
            <li>******** spa pool heating and pumping.</li>
            #end
        </ul>
        #end
    </div>

</div>

<!-- Page  Proposed/Reference/Deemed-To-Satisy Building Specifications -->
#foreach($building in $assessmentDetails.GetBuildings())

    <!-- Proposed           - Always there -->
    <!-- Reference/Deemed-to-Satisfy (same) - VURB/DTS-EP && IncludeReferenceBuildingElements -->
    #if($velocityCount == 1 || ($velocityCount == 2 && $assessmentDetails.IncludeReferenceBuildingElements && ($assessmentDetails.IsVurb || $assessmentDetails.IsComparisonDtsEp)))

        <!-- Construction -->
        #if($assessmentDetails.BuildingHasSurfaces($building))
        <div style="page-break-before: always; clear: both; width: 100%;">

            <!-- Header -->
            <h1>
                #if($velocityCount == 1)
                    Proposed Building Specifications - Construction
                #elseif($velocityCount == 2)
                    #if($assessmentDetails.IsVurb)
                        Reference Building Specifications - Construction
                    #elseif($assessmentDetails.IsComparisonDtsEp)
                        Deemed-to-Satisy Building Specifications - Construction
                    #end
                #end
            </h1>

            <!-- Table with Constructions -->
            #foreach($category in $assessmentDetails.ConstructionTabCategories())

                #if($assessmentDetails.CategoryHasItems($building, $category))
                <div style="page-break-inside: avoid;">

                    <h3 style="margin-bottom:0px">
                        $assessmentDetails.FormattedCategoryTitle($category.Title, $category.ConstructionCategoryCode)
                    </h3>
                    <hr/>

                    <table class="specification-table">
                        <thead>
                        <tr>

                            <th class="construction-desc-column">Description</th>

                            #if($$assessmentDetails.HasVentilation($category))
                            <th class="construction-ventilation-column">
                                #if($category.ConstructionCategoryCode == "Roof")
                                Roof Ventilation
                                #else
                                Subfloor Ventilation
                                #end
                            </th>
                            #end

                            #if($assessmentDetails.IncludeInsulation($category) == true)
                            <th class="construction-insulation-column">
                                Insulation
                            </th>
                            #end

                            <th class="construction-solar-column">Solar Absorptance</th>

                            <th class="construction-area-column">Net Area (m<sup>2</sup>)</th>
                        </tr>
                        </thead>
                        <tbody>
                            #foreach($construction in $assessmentDetails.ConstructionsForCategory($building, $category))
                            <tr>
                                <!-- Description -->
                                <td>
                                    $assessmentDetails.FinalDescription($construction)
                                </td>

                                <!-- Roof Space Ventilation (Roof) -->
                                #if($$assessmentDetails.HasVentilation($category))
                                <td>
                                    $construction.AirCavity.Title
                                </td>
                                #end

                                <!-- Insulation -->
                                #if($assessmentDetails.IncludeInsulation($category) == true)
                                <td>
                                    $assessmentDetails.InsulationDescription($construction)
                                </td>
                                #end

                                <!-- Solar Absorptance -->
                                <td>
                                    $assessmentDetails.Round2DP($assessmentDetails.SolarAbsorptance($construction, $category))
                                </td>

                                <!-- Net Area -->
                                <td>
                                    $assessmentDetails.Round2DP($construction.NetArea)
                                </td>
                            </tr>
                            #end
                        </tbody>

                    </table>
                </div>
                #end
            #end


        </div>
        #end

        <!-- Openings -->
        #if($assessmentDetails.BuildingHasOpenings($building))
        <div style="page-break-before: always; clear: both; width: 100%;">

            <h1>
                #if($velocityCount == 1)
                Proposed Building Specifications - Openings
                #elseif($velocityCount == 2)
                #if($assessmentDetails.IsVurb)
                Reference Building Specifications - Openings
                #elseif($assessmentDetails.IsComparisonDtsEp)
                Deemed-to-Satisy Building Specifications - Openings
                #end
                #end
            </h1>

            <!-- Table with openings -->
            #foreach($category in $assessmentDetails.OpeningsTabCategories())
                #if($assessmentDetails.CategoryHasItems($building, $category))
                <div style="page-break-inside: avoid;">

                    <h3 style="margin-bottom:0px">
                        $assessmentDetails.FormattedCategoryTitle($category.Title, $category.ConstructionCategoryCode)
                    </h3>
                    <hr/>

                    <table class="specification-table">

                        <thead>
                        <tr>
                            <th class="construction-desc-column">Description</th>

                            #if($category.ConstructionCategoryCode != "VerticalOpening" && $category.ConstructionCategoryCode != "HorizontalOpening")
                            <th class="construction-solar-column">
                                $assessmentDetails.SolarAbsorptanceType($category)
                            </th>
                            #end

                            #if($assessmentDetails.isFrame($category))
                            <th class="opening-uvalue-column">
                                U-Value
                            </th>
                            #end

                            #if($assessmentDetails.isFrame($category))
                            <th class="opening-uvalue-column">
                                SHGC
                            </th>
                            #end

                            <th class="opening-number-column">Quantity</th>
                            <th class="opening-area-column">Area (m<sup>2</sup>)</th>
                        </tr>
                        </thead>
                        <tbody>
                        #foreach($construction in $assessmentDetails.ConstructionsForCategory($building, $category))
                        <tr>

                            <!-- Description -->
                            <td>
                                $assessmentDetails.FinalDescription($construction)
                            </td>

                            <!-- (Frame) Solar Absorptance -->
                            #if($category.ConstructionCategoryCode != "VerticalOpening" && $category.ConstructionCategoryCode != "HorizontalOpening")
                            <td>
                                $assessmentDetails.Round2DP($assessmentDetails.SolarAbsorptance($construction, $category))
                            </td>
                            #end

                            <!-- U-Value -->
                            #if($assessmentDetails.isFrame($category))
                            <td>
                                $assessmentDetails.Round2DP($assessmentDetails.FinalUValue($construction))
                            </td>
                            #end

                            <!-- SHGC -->
                            #if($assessmentDetails.isFrame($category))
                            <td>
                                $assessmentDetails.Round2DP($assessmentDetails.FinalSHGC($construction))
                            </td>
                            #end

                            <!-- Quantity -->
                            <td>
                                $assessmentDetails.NumberOfElements($construction)
                            </td>

                            <!-- Area -->
                            <td>
                                $assessmentDetails.Round2DP($construction.GrossArea)
                            </td>
                        </tr>
                        #end
                        </tbody>

                    </table>
                </div>
                #end
            #end

        </div>
        #end

        <!-- Services -->
        #if($assessmentDetails.BuildingHasServices($building))
        <div style="page-break-before: always; clear: both; width: 100%;">

            <h1>
                #if($velocityCount == 1)
                Proposed Building Specifications - Services
                #elseif($velocityCount == 2)
                #if($assessmentDetails.IsVurb)
                Reference Building Specifications - Services
                #elseif($assessmentDetails.IsComparisonDtsEp)
                Deemed-to-Satisy Building Specifications - Services
                #end
                #end
            </h1>


            <!-- 1) Heating System -->
            #if($assessmentDetails.CategoryHasServices($building, 'SpaceHeatingSystem'))
            <div style="page-break-inside: avoid;">

                <h3 style="margin-bottom:0px">Heating System</h3>
                <hr/>

                <table class="specification-table">

                    <thead>
                    <tr>
                        <th class="service-column-1">Description</th>
                        <th class="service-column-3">System Type</th>
                        <th class="service-column-4">Fuel Type</th>
                        <th class="service-column-5">Minimum Energy Rating</th>
                    </tr>
                    </thead>

                    <tbody>

                    #foreach($service in $assessmentDetails.ServicesInCategory($building, 'SpaceHeatingSystem'))
                        <tr>

                            <!-- Description -->
                            <td>
                                $assessmentDetails.FinalDisplayDescription($service)
                            </td>

                            <!-- System Type -->
                            <td>
                                $assessmentDetails.ServiceType($service)
                            </td>

                            <!-- Fuel Type -->
                            <td>
                                $assessmentDetails.FuelType($service)
                            </td>

                            <!-- Minimum Energy Rating -->
                            <td>
                                $assessmentDetails.StarRating2019($service, false)
                            </td>

                        </tr>
                    #end

                    </tbody>
                </table>
            </div>
            #end

            <!-- 2) Cooling System -->
            #if($assessmentDetails.CategoryHasServices($building, 'SpaceCoolingSystem'))
            <div style="page-break-inside: avoid;">

                <h3 style="margin-bottom:0px">Cooling System</h3>
                <hr/>

                <table class="specification-table">
                    <thead>
                    <tr>
                        <th class="service-column-1">Description</th>
                        <th class="service-column-3">System Type</th>
                        <th class="service-column-4">Fuel Type</th>
                        <th class="service-column-5">Minimum Energy Rating</th>
                    </tr>
                    </thead>
                    #if(!$assessmentDetails.CategoryHasServices($building, 'SpaceCoolingSystem'))
                        <tr>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                        </tr>
                    #else
                        #foreach($service in $assessmentDetails.ServicesInCategory($building, 'SpaceCoolingSystem'))
                            <tr>

                                <!-- Description -->
                                <td>
                                    $assessmentDetails.FinalDisplayDescription($service)
                                </td>

                                <!-- System Type -->
                                <td>
                                    $assessmentDetails.ServiceType($service)
                                </td>

                                <!-- Fuel Type -->
                                <td>
                                    $assessmentDetails.FuelType($service)
                                </td>

                                <!-- Energy Rating -->
                                <td>
                                    $assessmentDetails.StarRating2019($service, false)
                                </td>

                            </tr>
                        #end
                    #end
                </table>
            </div>
            #end

            <!-- 3) Hot Water System -->
            #if($assessmentDetails.CategoryHasServices($building, 'HotWaterSystem'))
            <div style="page-break-inside: avoid;">

                <h3 style="margin-bottom:0px">Hot Water System</h3>
                <hr/>

                <table class="specification-table">
                    <thead>
                    <tr>
                        <th class="service-column-1">Description</th>
                        <th class="service-column-3">System Type</th>
                        <th class="service-column-4">Fuel Type</th>
                        <th class="service-column-5">Minimum Energy Rating</th>
                    </tr>
                    </thead>
                    #if(!$assessmentDetails.CategoryHasServices($building, 'HotWaterSystem'))
                        <tr>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                        </tr>
                    #else
                        #foreach($service in $assessmentDetails.ServicesInCategory($building, 'HotWaterSystem'))
                            <tr>

                                <!-- Description -->
                                <td>
                                    $assessmentDetails.FinalDisplayDescription($service)
                                </td>

                                <!-- System Type -->
                                <td>
                                    $assessmentDetails.ServiceType($service)
                                </td>

                                <!-- Fuel Type -->
                                <td>
                                    $assessmentDetails.FuelType($service)
                                </td>

                                <!-- Energy Rating (GEMS 2019) -->
                                <td>
                                    $assessmentDetails.StarRating2019($service, false)
                                </td>
                            </tr>
                        #end
                    #end
                </table>
            </div>
            #end

            <!-- 4) Cooktop -->
            #if($assessmentDetails.CategoryHasServices($building, 'Cooktop'))
            <div style="page-break-inside: avoid;">

                <h3 style="margin-bottom:0px">Cooktop</h3>
                <hr/>

                <table class="specification-table">
                    <thead>
                    <tr>
                        <th class="service-column-1">Description</th>
                        <th class="service-column-3">Cooktop Type</th>
                        <th class="service-column-4">Fuel Type</th>
                        <th class="service-column-5">Minimum Energy Rating</th>
                    </tr>
                    </thead>
                    #if(!$assessmentDetails.CategoryHasServices($building, 'Cooktop'))
                        <tr>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                        </tr>
                    #else
                        #foreach($service in $assessmentDetails.ServicesInCategory($building, 'Cooktop'))
                            <tr>

                                <!-- Description -->
                                <td>
                                    $assessmentDetails.FinalDisplayDescription($service)
                                </td>

                                <!-- System Type -->
                                <td>
                                    $assessmentDetails.ServiceType($service)
                                </td>

                                <!-- Fuel Type -->
                                <td>
                                    $assessmentDetails.FuelType($service)
                                </td>

                                <!-- Energy Rating (GEMS 2019) -->
                                <td>
                                    $assessmentDetails.StarRating2019($service, false)
                                </td>
                            </tr>
                        #end
                    #end
                </table>
            </div>
            #end

            <!-- 5) Oven -->
            #if($assessmentDetails.CategoryHasServices($building, 'Oven'))
            <div style="page-break-inside: avoid;">

                <h3 style="margin-bottom:0px">Oven</h3>
                <hr/>

                <table class="specification-table">
                    <thead>
                    <tr>
                        <th class="service-column-1">Description</th>
                        <th class="service-column-3">Oven Type</th>
                        <th class="service-column-4">Fuel Type</th>
                        <th class="service-column-5">Minimum Energy Rating</th>
                    </tr>
                    </thead>
                    #if(!$assessmentDetails.CategoryHasServices($building, 'Oven'))
                        <tr>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                        </tr>
                    #else
                        #foreach($service in $assessmentDetails.ServicesInCategory($building, 'Oven'))
                            <tr>

                                <!-- Description -->
                                <td>
                                    $assessmentDetails.FinalDisplayDescription($service)
                                </td>

                                <!-- System Type -->
                                <td>
                                    $assessmentDetails.ServiceType($service)
                                </td>

                                <!-- Fuel Type -->
                                <td>
                                    $assessmentDetails.FuelType($service)
                                </td>

                                <!-- Energy Rating (GEMS 2019) -->
                                <td>
                                    $assessmentDetails.StarRating2019($service, false)
                                </td>
                            </tr>
                        #end
                    #end
                </table>
            </div>
            #end

            <!-- 6) Swimming Pool -->
            #if($assessmentDetails.CategoryHasServices($building, 'SwimmingPool'))
            <div style="page-break-inside: avoid;">

                <h3 style="margin-bottom:0px">Swimming Pool</h3>
                <hr/>

                <table class="specification-table">
                    <thead>
                    <tr>
                        <th class="service-column-1">Description</th>
                        <th class="service-column-2">Volume (L)</th>
                        <th class="service-column-3">Pump Type</th>
                        <th class="service-column-4">Fuel Type</th>
                        <th class="service-column-5">Minimum Energy Rating</th>
                    </tr>
                    </thead>
                    #if(!$assessmentDetails.CategoryHasServices($building, 'SwimmingPool'))
                        <tr>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                        </tr>
                    #else
                        #foreach($service in $assessmentDetails.ServicesInCategory($building, 'SwimmingPool'))
                            <tr>
                                <!-- Description -->
                                <td>
                                    $assessmentDetails.FinalDisplayDescription($service)
                                </td>

                                <!-- Volume -->
                                <td>
                                    $assessmentDetails.Volume($service)
                                </td>

                                <!-- Pump Type -->
                                <td>
                                    $assessmentDetails.PumpType($service)
                                </td>

                                <!-- Fuel Type -->
                                <td>
                                    $assessmentDetails.FuelType($service)
                                </td>

                                <!-- Pump Energy Rating (GEMS 2019) -->
                                <td>
                                    $assessmentDetails.StarRating2019($service, false)
                                </td>

                            </tr>
                        #end
                    #end
                </table>
            </div>
            #end

            <!-- 7) Spa -->
            #if($assessmentDetails.CategoryHasServices($building, 'Spa'))
            <div style="page-break-inside: avoid;">

                <h3 style="margin-bottom:0px">Spa</h3>
                <hr/>

                <table class="specification-table">
                    <thead>
                    <tr>
                        <th class="service-column-1">Description</th>
                        <th class="service-column-2">Volume (L)</th>
                        <th class="service-column-3">Pump Type</th>
                        <th class="service-column-4">Fuel Type</th>
                        <th class="service-column-5">Minimum Energy Rating</th>
                    </tr>
                    </thead>
                    #if(!$assessmentDetails.CategoryHasServices($building, 'Spa'))
                        <tr>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                        </tr>
                    #else
                        #foreach($service in $assessmentDetails.ServicesInCategory($building, 'Spa'))
                            <tr>
                                <!-- Description -->
                                <td>
                                    $assessmentDetails.FinalDisplayDescription($service)
                                </td>

                                <!-- Volume -->
                                <td>
                                    $assessmentDetails.Volume($service)
                                </td>

                                <!-- Pump Type -->
                                <td>
                                    $assessmentDetails.PumpType($service)
                                </td>

                                <!-- Fuel Type -->
                                <td>
                                    $assessmentDetails.FuelType($service)
                                </td>

                                <!-- Pump Energy Rating (GEMS 2019) -->
                                <td>
                                    $assessmentDetails.StarRating2019($service, false)
                                </td>
                            </tr>
                        #end
                    #end
                </table>
            </div>
            #end

            <!-- 8) Photovoltaic System -->
            #if($assessmentDetails.CategoryHasServices($building, 'PhotovoltaicSystem'))
            <div style="page-break-inside: avoid;">

                <h3 style="margin-bottom:0px">Photovoltaic System</h3>
                <hr/>

                <table class="specification-table">
                    <thead>
                    <tr>
                        <th class="service-column-1">Description</th>
                        #if($assessmentDetails.ShowBattery($building, 'PhotovoltaicSystem'))
                        <th class="service-column-2">Array Capacity (kW)</th>
                        <th class="service-column-3">Inverter Capacity (kW)</th>
                        <th class="service-column-4">Battery Type</th>
                        <th class="service-column-5">Battery Capacity (kWh)</th>
                        #else
                        <th class="service-column-4">Array Capacity (kW)</th>
                        <th class="service-column-5">Inverter Capacity (kW)</th>
                        #end
                    </tr>
                    </thead>
                    #if(!$assessmentDetails.CategoryHasServices($building, 'PhotovoltaicSystem'))
                        <tr>
                            <td>-</td>
                            <td>-</td>
                        </tr>
                    #else
                        #foreach($service in $assessmentDetails.ServicesInCategory($building, 'PhotovoltaicSystem'))
                            <tr>
                                <!-- Description -->
                                <td>
                                    $assessmentDetails.FinalDisplayDescription($service)
                                </td>
                                <!-- Array Capacity -->
                                <td>
                                    $assessmentDetails.SystemCapacity($service)
                                </td>
                                <!-- Inverter Capacity -->
                                <td>
                                    $assessmentDetails.InverterCapacity($service)
                                </td>
                                #if($assessmentDetails.ShowBattery($building, 'PhotovoltaicSystem'))
                                <!-- Battery Type -->
                                <td>
                                    $assessmentDetails.BatteryType($service)
                                </td>
                                <!-- Battery Capacity -->
                                <td>
                                    $assessmentDetails.BatteryCapacity($service)
                                </td>
                                #end
                            </tr>
                        #end
                    #end
                </table>
            </div>
            #end

            <!-- 9) Artificial Lighting -->
            #if($assessmentDetails.CategoryHasServices($building, 'ArtificialLighting'))
            <div style="page-break-inside: avoid;">

                <h3 style="margin-bottom:0px">Artificial Lighting</h3>
                <hr/>

                <table class="specification-table">
                    <thead>
                    <tr>
                        <th class="service-column-1">Description</th>
                        <th class="service-column-2">Cut Out Diameter (mm)</th>
                        <th class="service-column-3">IC Rating</th>
                        <th class="service-column-4">Sealed/Unsealed</th>
                        <th class="service-column-5">Quantity</th>
                    </tr>
                    </thead>
                    #if(!$assessmentDetails.CategoryHasServices($building, 'ArtificialLighting'))
                    <tr>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                    </tr>
                    #else
                        #foreach($service in $assessmentDetails.ServicesInCategory($building, 'ArtificialLighting'))
                            <tr>

                                <!-- Description -->
                                <td>
                                    $assessmentDetails.FinalDisplayDescription($service)
                                </td>

                                <!-- Cut Out Diameter (mm) -->
                                <td>
                                    $assessmentDetails.CutOutDiameter($service)
                                </td>

                                <!-- IC Rating -->
                                <td>
                                    $assessmentDetails.ICRating($service)
                                </td>

                                <!-- Sealed -->
                                <td>
                                    $assessmentDetails.IsSealed($service)
                                </td>

                                <!-- Quantity -->
                                <td>
                                    $assessmentDetails.NumberSum($service)
                                </td>

                            </tr>
                        #end
                    #end
                </table>
            </div>
            #end

            <!-- 10) Exhaust Fans -->
            #if($assessmentDetails.CategoryHasServices($building, 'ExhaustFans'))
            <div style="page-break-inside: avoid;">

                <h3 style="margin-bottom:0px">Exhaust Fans</h3>
                <hr/>

                <table class="specification-table">
                    <thead>
                    <tr>
                        <th class="service-column-1">Description</th>
                        <th class="service-column-3">Cut Out Diameter (mm)</th>
                        <th class="service-column-4">Sealed/Unsealed</th>
                        <th class="service-column-5">Quantity</th>
                    </tr>
                    </thead>
                    #if(!$assessmentDetails.CategoryHasServices($building, 'ExhaustFans'))
                        <tr>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                        </tr>
                    #else
                        #foreach($service in $assessmentDetails.ServicesInCategory($building, 'ExhaustFans'))
                            <tr>

                                <!-- Description -->
                                <td>
                                    $assessmentDetails.FinalDisplayDescription($service)
                                </td>

                                <!-- Cut Out Diameter (mm) -->
                                <td>
                                    $assessmentDetails.CutOutDiameter($service)
                                </td>

                                <!-- Sealed -->
                                <td>
                                    $assessmentDetails.IsSealed($service)
                                </td>

                                <!-- Quantity -->
                                <td>
                                    $assessmentDetails.NumberSum($service)
                                </td>

                            </tr>
                        #end
                    #end
                </table>
            </div>
            #end

            <!-- 11) Ceiling Vents -->
            #if($assessmentDetails.CategoryHasServices($building, 'CeilingVents'))
            <div style="page-break-inside: avoid;">

                <h3 style="margin-bottom:0px">Ceiling Vents</h3>
                <hr/>

                <table class="specification-table">
                    <thead>
                    <tr>
                        <th class="service-column-1">Description</th>
                        <th class="service-column-2">Cut Out Length (mm)</th>
                        <th class="service-column-3">Cut Out Width (mm)</th>
                        <th class="service-column-4">Sealed/Unsealed</th>
                        <th class="service-column-5">Quantity</th>
                    </tr>
                    </thead>
                    #if(!$assessmentDetails.CategoryHasServices($building, 'CeilingVents'))
                        <tr>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                        </tr>
                    #else
                        #foreach($service in $assessmentDetails.ServicesInCategory($building, 'CeilingVents'))
                            <tr>
                                <!-- Description -->
                                <td>
                                    $assessmentDetails.FinalDisplayDescription($service)
                                </td>

                                <!-- Cut Out Length (mm) -->
                                <td>
                                    $assessmentDetails.Length($service)
                                </td>

                                <!-- Cut Out Width (mm) -->
                                <td>
                                    $assessmentDetails.Width($service)
                                </td>

                                <!-- Sealed -->
                                <td>
                                    $assessmentDetails.IsSealed($service)
                                </td>

                                <!-- Quantity -->
                                <td>
                                    $assessmentDetails.NumberSum($service)
                                </td>

                            </tr>
                        #end
                    #end
                </table>
            </div>
            #end

            <!-- 12) Ceiling Fans -->
            #if($assessmentDetails.CategoryHasServices($building, 'CeilingFans'))
            <div style="page-break-inside: avoid;">

                <h3 style="margin-bottom:0px">Ceiling Fans</h3>
                <hr/>

                <table class="specification-table">
                    <thead>
                    <tr>
                        <th class="service-column-1">Description</th>
                        <th class="service-column-4">Blade Diameter (mm)</th>
                        <th class="service-column-5">Quantity</th>
                    </tr>
                    </thead>
                    #if(!$assessmentDetails.CategoryHasServices($building, 'CeilingFans'))
                        <tr>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                        </tr>
                    #else
                        #foreach($service in $assessmentDetails.ServicesInCategory($building, 'CeilingFans'))
                            <tr>

                                <!-- Description -->
                                <td>
                                    $assessmentDetails.FinalDisplayDescription($service)
                                </td>

                                <!-- Blade Diameter -->
                                <td>
                                    $assessmentDetails.BladeDiameter($service)
                                </td>

                                <!-- Quantity -->
                                <td>
                                    $assessmentDetails.NumberSum($service)
                                </td>

                            </tr>
                        #end
                    #end
                </table>
            </div>
            #end


        </div>
        #end
    #end
#end


<!-- Page  Intellectual Property Rights & Disclaimer -->
<div style="page-break-before: always; clear: both; width: 100%;">

    <h1>Intellectual Property Rights &amp; Disclaimer</h1>

    <div>
        <p>
            This document is the copyright of Thermarate Pty Ltd and, apart from any use as permitted under the
            Copyright Act 1998, no part may be reproduced or distributed in any form or by any means without the prior
            written permission of Thermarate Pty Ltd.
        </p>
        <p>
            This report is based on the architectural drawings, specifications and other information provided by the
            client at the time the report was completed. Should there be any changes at all to the design, the drawings
            or the specifications (regardless of how minor), an updated report will be required to confirm that energy
            efficiency compliance is still achieved. An updated report will incur a reassessment fee.
        </p>
        <p>
            This document may be revised, updated, amended or superseded at any time. A hard copy of this document may
            not contain the most accurate and current information. If this document is to be used for regulatory or
            statutory purposes, contact Thermarate Pty Ltd to confirm that this document is current and appropriate for
            its intended use.
        </p>
        <p>
            No responsibility will be taken by Thermarate Pty Ltd if this report is altered in any way, or not
            reproduced in full.
        </p>
        <p>
            Thermarate Pty Ltd has relied upon the accuracy of the architectural drawings, specifications and other
            information provided by the client and other parties, accepts no liability for the accuracy of such
            information and expressly disclaims liability for any person's loss or damage arising directly or indirectly
            from any inaccuracy.
        </p>
        <p>
            The information contained herein is solely for the named recipients use only. It may contain privileged
            and/or confidential information and therefore must not be forwarded onto any third party or any third party
            granted access without prior written consent from Thermarate Pty Ltd. Any use or disclosure of the contents
            without such consent is unauthorised and may be unlawful.
        </p>
        <p>
            To the extent permitted by law, neither Thermarate Pty Ltd nor its directors, employees or contractors will
            be liable for any loss or damage of any kind arising directly or indirectly out of or in connection with the
            use of this report and the client and any other authorised recipients release Thermarate Pty Ltd and its
            directors, employees and contractors from any such liability.
        </p>
    </div>
</div>

</body>

</html>
