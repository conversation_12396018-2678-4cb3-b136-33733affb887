(function () {

    'use strict';
    var controllerId = 'EnergyLabsHomeController';
    angular.module('app')
        .controller(controllerId, ['common', '$state', '$stateParams', 'projectservice', energyLabsHomeController]);

    function energyLabsHomeController(common, $state, $stateParams, projectservice) {

        var vm = this;

        vm.backToProjects = function () {
          $state.go('energy-labs-parent-menu');
        }

        // Check project access and redirect if no permission
        projectservice.getProject($stateParams.projectId).then((data) => {
            if (data == null || data == "" || (data.clientId && !data.projectName)) {
                // Project not found or no permission - redirect to projects page
                $state.go('energy-labs-parent-menu');
                return;
            }
            vm.project = data;
        }).catch((error) => {
            // Error accessing project - redirect to projects page
            $state.go('energy-labs-parent-menu');
        });

        //Return to home page
        vm.cancel = function () {
            $state.go("/");
        }
    }
})();