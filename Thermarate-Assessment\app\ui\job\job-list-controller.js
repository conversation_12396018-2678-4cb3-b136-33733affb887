(function () {
    // The JobListCtrl supports a list page.
    'use strict';
    var controllerId = 'JobListCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$window', '$state', '$mdDialog', '$timeout', 'common', 'security', 'jobservice', 'jobanalyticsservice', 'daterangehelper', 'priorityservice', 'standardmodelservice', jobListController]);
    function jobListController($rootScope, $window, $state, $mdDialog, $timeout, common, securityservice, jobservice, jobanalyticsservice, daterangehelper, priorityservice, standardmodelservice) {
        // The model for this form
        var vm = this;
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.title = 'Jobs';
        vm.jobsList = [];
        vm.listFilter = "";
        vm.actionButtons = [];
        vm.statusOptions = [];
        vm.priorityList = [];
        vm.default_priority_theme = "default";
        vm.red_priority_theme = "altRed";

        vm.currentFilter = "All";
        vm.totalRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.currentQuery = {};
        vm.filterByCode = 'All';
        vm.statusCode = 'All';
        vm.showMoreLoading = false;

        let resultsPageSize = 100;

        // ---------------- //
        // - Persmissions - //
        // ---------------- //

        vm.permission_canAccessSelfAssigned  = securityservice.immediateCheckRoles(['home_page_/_job_page__assignedself__access']);
        vm.permission_canAccessOtherAssigned = securityservice.immediateCheckRoles(['home_page_/_job_page__assignedother__access']);

        // ------------- //
        // - Filtering - //
        // ------------- //

        vm.filters = [
            { section: 1, name: 'client',                   field: 'clientName' },
            { section: 1, name: 'status',                   field: 'jobStatusDescription' },
            { section: 1, name: 'createdOn',                field: 'jobCreatedOn',            isDate: true },
            { section: 1, name: 'certifiedOn',              field: 'assessmentCerficateDate', isDate: true },
            { section: 1, name: 'updatedOn',                field: 'jobModifiedOn',           isDate: true },
            { section: 1, name: '[moreLessButton]',         field: '' },
            { section: 2, name: securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__creator__view")          ? 'creator'  : null, field: 'creatorFullName' },
            { section: 2, name: securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__assignee__view")         ? 'assignee' : null, field: 'clientAssigneeFullName' },
            { section: 2, name: securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__assignedassessor__view") ? 'assessor' : null, field: 'assessorFullName' },
            { section: 2, name: 'version',                  field: 'assessmentVersion', isDecimal: true },
            { section: 2, name: 'priority',                 field: 'assessmentPriorityDescription' },
            { section: 2, name: 'suburb',                   field: 'assessmentProjectDetailSuburb' },
            { section: 2, name: 'localGovernmentAuthority', field: 'assessmentProjectDetailLGA' },
            { section: 2, name: securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__creator__view")          ? null : '[blank]', field: 'blank1'},
            { section: 2, name: securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__assignee__view")         ? null : '[blank]', field: 'blank2'},
            { section: 2, name: securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__assignedassessor__view") ? null : '[blank]', field: 'blank3'},
            { section: 2, name: 'buildingDescription',      field: 'projectDescriptionDescription' },
            { section: 2, name: 'houseType',                field: 'assessmentDesign', optional: true },
            { section: 2, name: 'nccClimateZone',           field: 'assessmentNCCClimateZone' },
            { section: 2, name: 'natHERSClimateZone',       field: 'assessmentNatHERSClimateZone' },
            { section: 2, name: 'North Offset (\u00B0)',    field: 'northOffset',
                                                            groups: [
                                                                { value: "optionGroupNorth",     name: "> 337.5 to 22.5 (North)",       startRange: 337.5,  endRange: 22.5  },
                                                                { value: "optionGroupNorthEast", name: "> 22.5 to 67.5 (North-East)",   startRange: 22.5,   endRange: 67.5  },
                                                                { value: "optionGroupEast",      name: "> 67.5 to 112.5 (East)",        startRange: 67.5,   endRange: 112.5 },
                                                                { value: "optionGroupSouthEast", name: "> 112.5 to 157.5 (South-East)", startRange: 112.5,  endRange: 157.5 },
                                                                { value: "optionGroupSouth",     name: "> 157.5 to 202.5 (South)",      startRange: 157.5,  endRange: 202.5 },
                                                                { value: "optionGroupSouthWest", name: "> 202.5 to 247.5 (South-West)", startRange: 202.5,  endRange: 247.5 },
                                                                { value: "optionGroupWest",      name: "> 247.5 to 292.5 (West)",       startRange: 247.5,  endRange: 292.5 },
                                                                { value: "optionGroupNorthWest", name: "> 292.5 to 337.5 (North-West)", startRange: 292.5,  endRange: 337.5 }
                                                            ]},
            { section: 2, name: 'garage',                   field: 'garageLocation' },
            { section: 2, name: 'coveredOutdoorLiving',     field: 'outdoorLivingLocation' },
            { section: 2, name: 'lotDescription',           field: 'assessmentProjectDetailLotDescription' },
            { section: 2, name: 'Lot Width (m)',            field: 'assessmentProjectDetailLotWidth',    isDecimal: true,
                                                            groups: [
                                                                { value: "optionGroup6",                  name: `${common.symbol("lessThanEqual")} 6 m`,     startRange: -0.01, endRange: 6      },
                                                                { value: "optionGroup6To7.5",             name: "> 6 to 7.5 m",                              startRange: 6,     endRange: 7.5    },
                                                                { value: "optionGroupEast7.5To8.5",       name: "> 7.5 to 8.5 m",                            startRange: 7.5,   endRange: 8.5    },
                                                                { value: "optionGroupSouthEast8.5To10.5", name: "> 8.5 to 10.5 m",                           startRange: 8.5,   endRange: 10.5   },
                                                                { value: "optionGroupSouth10.5To12.5",    name: "> 10.5 to 12.5 m",                          startRange: 10.5,  endRange: 12.5   },
                                                                { value: "optionGroupSouthWest12.5To14",  name: "> 12.5 to 14 m",                            startRange: 12.5,  endRange: 14     },
                                                                { value: "optionGroupWest14To15",         name: "> 14 to 15 m",                              startRange: 14,    endRange: 15     },
                                                                { value: "optionGroupNorthWest15To16",    name: "> 15 to 16 m",                              startRange: 15,    endRange: 16     },
                                                                { value: "optionGroupNorthWest16To18",    name: "> 16 to 18 m",                              startRange: 16,    endRange: 18     },
                                                                { value: "optionGroupNorthWest18",        name: `${common.symbol("greaterThanEqual")} 18 m`, startRange: 18,    endRange: 999999 }
                                                            ]},
            { section: 2, name: 'Lot Length (m)',           field: 'assessmentProjectDetailLotLength',   isDecimal: true,
                                                            groups: [
                                                                { value: "optionGroup16",     name: `${common.symbol("lessThanEqual")} 16 m`,    startRange: -0.01, endRange: 16     },
                                                                { value: "optionGroup16To20", name: "> 16 to 20 m",                              startRange: 16,    endRange: 20     },
                                                                { value: "optionGroup20To21", name: "> 20 to 21 m",                              startRange: 20,    endRange: 21     },
                                                                { value: "optionGroup21To23", name: "> 21 to 23 m",                              startRange: 21,    endRange: 23     },
                                                                { value: "optionGroup23To25", name: "> 23 to 25 m",                              startRange: 23,    endRange: 25     },
                                                                { value: "optionGroup25To28", name: "> 25 to 28 m",                              startRange: 25,    endRange: 28     },
                                                                { value: "optionGroup28To30", name: "> 28 to 30 m",                              startRange: 28,    endRange: 30     },
                                                                { value: "optionGroup30To32", name: "> 30 to 32 m",                              startRange: 30,    endRange: 32     },
                                                                { value: "optionGroup32To35", name: "> 32 to 35 m",                              startRange: 32,    endRange: 35     },
                                                                { value: "optionGroup35",     name: `${common.symbol("greaterThanEqual")} 35 m`, startRange: 35,    endRange: 999999 }
                                                            ]},
            { section: 2, name: 'Parcel Area (m\u00B2)',    field: 'assessmentProjectDetailParcelArea',  isDecimal: true,
                                                            groups: [
                                                                { value: "optionGroup150",       name: `${common.symbol("lessThanEqual")} 150 m${common.symbol("squared")}`,     startRange: -0.01, endRange: 150    },
                                                                { value: "optionGroup150To200",  name: `> 150 to 200 m${common.symbol("squared")}`,                              startRange: 150,   endRange: 200    },
                                                                { value: "optionGroup200To250",  name: `> 200 to 250 m${common.symbol("squared")}`,                              startRange: 200,   endRange: 250    },
                                                                { value: "optionGroup250To300",  name: `> 250 to 300 m${common.symbol("squared")}`,                              startRange: 250,   endRange: 300    },
                                                                { value: "optionGroup300To350",  name: `> 300 to 350 m${common.symbol("squared")}`,                              startRange: 300,   endRange: 350    },
                                                                { value: "optionGroup350To400",  name: `> 350 to 400 m${common.symbol("squared")}`,                              startRange: 350,   endRange: 400    },
                                                                { value: "optionGroup400To450",  name: `> 400 to 450 m${common.symbol("squared")}`,                              startRange: 400,   endRange: 450    },
                                                                { value: "optionGroup450To500",  name: `> 450 to 500 m${common.symbol("squared")}`,                              startRange: 450,   endRange: 500    },
                                                                { value: "optionGroup500To550",  name: `> 500 to 550 m${common.symbol("squared")}`,                              startRange: 500,   endRange: 550    },
                                                                { value: "optionGroup550To600",  name: `> 550 to 600 m${common.symbol("squared")}`,                              startRange: 550,   endRange: 600    },
                                                                { value: "optionGroup600To650",  name: `> 600 to 650 m${common.symbol("squared")}`,                              startRange: 600,   endRange: 650    },
                                                                { value: "optionGroup650To700",  name: `> 650 to 700 m${common.symbol("squared")}`,                              startRange: 650,   endRange: 700    },
                                                                { value: "optionGroup700To750",  name: `> 700 to 750 m${common.symbol("squared")}`,                              startRange: 700,   endRange: 750    },
                                                                { value: "optionGroup750To800",  name: `> 750 to 800 m${common.symbol("squared")}`,                              startRange: 750,   endRange: 800    },
                                                                { value: "optionGroup800To850",  name: `> 800 to 850 m${common.symbol("squared")}`,                              startRange: 800,   endRange: 850    },
                                                                { value: "optionGroup850To900",  name: `> 850 to 900 m${common.symbol("squared")}`,                              startRange: 850,   endRange: 900    },
                                                                { value: "optionGroup900To950",  name: `> 900 to 950 m${common.symbol("squared")}`,                              startRange: 900,   endRange: 950    },
                                                                { value: "optionGroup950To1000", name: `> 950 to 1000 m${common.symbol("squared")}`,                             startRange: 950,   endRange: 1000   },
                                                                { value: "optionGroup1000",      name: `${common.symbol("greaterThanEqual")} 1000 m${common.symbol("squared")}`, startRange: 1000,  endRange: 999999 }
                                                            ]},
            { section: 2, name: 'cornerLot',                field: 'assessmentProjectDetailCornerBlock' },
            { section: 2, name: 'rearLoaded',               field: 'assessmentProjectDetailRearLaneway', isBool: true },
            { section: 2, name: 'ruralLot',                 field: 'assessmentProjectDetailRuralLot',    isBool: true },
            // { section: 2, name: '[blank]',                  field: 'blank1'}, // Use this for blank spaces
        ];
        vm.filteredFilters = vm.filters.filter(f => f.name != null && f.name != "[blank]" && f.name != "[moreLessButton]");
        vm.filtersExpanded = false;
        vm.filterOptions = {};
        vm.searchFields = [
            "jobReference",
            "clientName",
            "statusCode",
            "jobStatusDescription",
            "assessorFullName",
            "assessmentPriorityDescription",
            "assessmentStatusCode",
            "clientJobNumber",
            "projectOwner",
            "address",
            "creatorFullName",
            "clientAssigneeFullName",
            "projectDescriptionDescription",
            "complianceMethodDescription",
        ];
        vm.appliedFilters = {};
        vm.appliedFiltersOld = {};
        // Get all filters with group headings removed
        vm.filteredAppliedFilters = function () {
            let newFilters = angular.copy(vm.appliedFilters);
            Object.keys(newFilters).forEach(fieldName => {
                if (vm.filters.find(f => f.field == fieldName)?.groups != null) {
                    newFilters[fieldName] = newFilters[fieldName].filter(o => !o.startsWith("optionGroup"));
                }
            });
            return newFilters;
        }
        vm.filterCountData = {};
        vm.cachedFilterCounts = {}; // Cache for filter counts
        vm.cachedGroupCounts = {}; // Cache for group counts
        vm.cachedGroupOptions = {}; // Cache for group options
        vm.keyToName = standardmodelservice.keyToName;
        vm.getFilterSelectedText = common.getMultiFilterSelectedText;
        vm.optionName = common.getOptionName;
        vm.anyOptionsSelectedOnField = common.anyOptionsSelectedOnField;
        vm.anyFiltersApplied = common.anyFiltersApplied;

        // Optimized function to get group options - uses caching
        vm.getGroupOptions = function (filter, group) {
            let fieldName = filter.field;
            let groupKey = `${fieldName}_${group.value}`;

            // Return cached result if available
            if (vm.cachedGroupOptions[groupKey]) {
                return vm.cachedGroupOptions[groupKey];
            }

            let filteredFilterOptions = vm.filterOptions[fieldName]?.filter(f => group.startRange > group.endRange
                                                                                 ? +f.value > group.startRange || +f.value <= group.endRange
                                                                                 : +f.value > group.startRange && +f.value <= group.endRange);

            // Create a lookup map for faster filtering
            if (!vm.filterCountLookup) {
                vm.updateFilterCountLookup();
            }

            filteredFilterOptions = filteredFilterOptions?.filter(o => {
                const countItem = vm.getFilterCountItem(filter, o);
                return countItem > 0;
            });

            // Cache the result
            vm.cachedGroupOptions[groupKey] = filteredFilterOptions;
            return filteredFilterOptions;
        }

        // Create a lookup map for faster filter count lookups
        vm.updateFilterCountLookup = function() {
            vm.filterCountLookup = {};

            if (!vm.filterCountData) return;

            Object.keys(vm.filterCountData).forEach(fieldName => {
                vm.filterCountLookup[fieldName] = {};

                if (vm.filterCountData[fieldName]) {
                    Object.keys(vm.filterCountData[fieldName]).forEach(key => {
                        vm.filterCountLookup[fieldName][key.toLowerCase()] = vm.filterCountData[fieldName][key];
                    });
                }
            });
        }

        // Optimized function to get filter count - uses caching and lookup map
        vm.getFilterCountItem = function (filter, item) {
            if (!item) return null;

            let fieldName = filter.field;
            let itemValue = item.value?.toString().toLowerCase();

            if (!itemValue) return null;

            // Use cache if available
            let cacheKey = `${fieldName}_${itemValue}`;
            if (vm.cachedFilterCounts[cacheKey] !== undefined) {
                return vm.cachedFilterCounts[cacheKey];
            }

            // Use lookup map for faster access
            if (vm.filterCountLookup && vm.filterCountLookup[fieldName]) {
                const count = vm.filterCountLookup[fieldName][itemValue];
                vm.cachedFilterCounts[cacheKey] = count || 0;
                return count || 0;
            }

            // Fallback to original method if lookup not available
            if (!vm.filterCountData[fieldName]) {
                vm.cachedFilterCounts[cacheKey] = 0;
                return 0;
            }

            let valueKey = Object.keys(vm.filterCountData[fieldName]).find(option =>
                option.toLowerCase() === itemValue);

            const count = valueKey ? vm.filterCountData[fieldName][valueKey] : 0;
            vm.cachedFilterCounts[cacheKey] = count;
            return count;
        }

        // Optimized function to get group count - uses caching
        vm.getGroupCount = function (filter, group) {
            let cacheKey = `${filter.field}_${group.value}`;

            // Return cached result if available
            if (vm.cachedGroupCounts[cacheKey] !== undefined) {
                return vm.cachedGroupCounts[cacheKey];
            }

            let optionsForThisGroup = vm.getGroupOptions(filter, group);
            if (!optionsForThisGroup || optionsForThisGroup.length === 0) {
                vm.cachedGroupCounts[cacheKey] = 0;
                return 0;
            }

            // Calculate the total once and cache it
            let thisGroupTotal = 0;
            for (let i = 0; i < optionsForThisGroup.length; i++) {
                const count = vm.getFilterCountItem(filter, optionsForThisGroup[i]);
                thisGroupTotal += count || 0;
            }

            vm.cachedGroupCounts[cacheKey] = thisGroupTotal;
            return thisGroupTotal;
        }

        // Clear caches when filter data changes
        vm.clearFilterCaches = function() {
            vm.cachedFilterCounts = {};
            vm.cachedGroupCounts = {};
            vm.cachedGroupOptions = {};
            vm.filterCountLookup = null;
        }
        vm.selectOptionForGroup = function (filter, selected, group) {
            let fieldName = filter.field;
            let allOptionsForThisGroup = [...vm.getGroupOptions(filter, group).map(o => o.value), group.value];
            // function selectAllLogic(allOptions, listBefore, listChildIdKey, selected, selectAllCode)
            let newSelectedForThisGroup = common.selectAllLogic(
                [...allOptionsForThisGroup, group.value],
                vm.appliedFilters[fieldName].filter(o => allOptionsForThisGroup.includes(o)), // Only looking at this group's selected options
                null,
                selected.value,
                group.value
            )
            // Now that we have new selected options for this group, add this group's selections to full list of selections by removing this group's options from full selected list then adding this group's new selected
            vm.appliedFilters[fieldName] = vm.appliedFilters[fieldName].filter(o => !allOptionsForThisGroup.includes(o)).concat(newSelectedForThisGroup);
        }
        vm.clearFilter = function (filter) {
            vm.appliedFilters[filter.field] = filter.isDate ? daterangehelper.getDefaultRange('All Time') : [];
            vm.filtersApplied = vm.anyFiltersApplied(vm.searchString, vm.filteredFilters, vm.appliedFilters);

            // Clear caches related to this filter
            if (filter && filter.field) {
                const fieldName = filter.field;

                // Clear filter count cache for this field
                Object.keys(vm.cachedFilterCounts).forEach(key => {
                    if (key.startsWith(`${fieldName}_`)) {
                        delete vm.cachedFilterCounts[key];
                    }
                });

                // Clear group options cache for this field
                Object.keys(vm.cachedGroupOptions).forEach(key => {
                    if (key.startsWith(`${fieldName}_`)) {
                        delete vm.cachedGroupOptions[key];
                    }
                });

                // Clear group count cache for this field
                Object.keys(vm.cachedGroupCounts).forEach(key => {
                    if (key.startsWith(`${fieldName}_`)) {
                        delete vm.cachedGroupCounts[key];
                    }
                });
            }

            vm.refreshList();
        }
        vm.clearFilters = function () {
            vm.initialiseComplete = false;
            vm.appliedFilters = {};
            vm.appliedFiltersOld = {};
            vm.filterCountData = {};
            vm.clearFilterCaches();
            vm.searchString = null;
            vm.searchStringOld = null;
            vm.appliedFilters['jobCreatedOn'] = daterangehelper.getDefaultRange('All Time');
            vm.appliedFilters['assessmentCerficateDate'] = daterangehelper.getDefaultRange('All Time');
            vm.appliedFilters['jobModifiedOn'] = daterangehelper.getDefaultRange('All Time');
            vm.filtersApplied = false;
            vm.initialRefreshList();
        }

        // --------- //
        // - Dates - //
        // --------- //

        vm.ranges = daterangehelper.getRanges('Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month',
                                                'This Quarter', 'Last Quarter', 'Current Year', 'Current Financial Year', 'Last Financial Year',
                                                'Last Year', '12 Months', 'All Time');

        // --------- //
        // - Table - //
        // --------- //

        var saveTableState = null;
        vm.filterColumns = null;
        vm.filterLocalName = "joblistCtrl-columnFilter"
        vm.columnOptions = {
            jobReference: true,
            clientName: true,
            contact: true,
            clientJobNumber: true,
            projectOwner: true,
            orderDate: false,
            jobModifiedOn: true,
            assessmentVersion: true,
            assessorFullName: true,
            assessmentPriorityDescription: true,
            status: true,
            address: true,
            projectDescriptionDescription: true,
            assessmentDesign: false,
            columnCount: 16,
            columnList: [
                { sortOrder: 0,  reference: 'jobReference',                  description: 'Ref',                  checked: true  },
                { sortOrder: 1,  reference: 'clientName',                    description: 'Client',               checked: true  },
                { sortOrder: 2,  reference: 'clientJobNumber',               description: 'Client Ref',           checked: true  },
                { sortOrder: 3,  reference: 'address',                       description: 'Project Address',      checked: true  },
                { sortOrder: 4,  reference: 'projectOwner',                  description: 'Owner',                checked: true  },
                { sortOrder: 5,  reference: 'projectDescriptionDescription', description: 'Building Description', checked: true  },
                { sortOrder: 6,  reference: 'assessmentDesign',              description: 'House Type',           checked: false },
                { sortOrder: 8,  reference: 'assessmentVersion',             description: 'Version',              checked: true  },
                { sortOrder: 9,  reference: 'creatorFullName',               description: 'Creator',              checked: true  },
                { sortOrder: 10, reference: 'assignee',                      description: 'Assignee',             checked: true  },
                { sortOrder: 11, reference: 'assessorFullName',              description: 'Assessor',             checked: true  },
                { sortOrder: 12, reference: 'assessmentPriorityDescription', description: 'Priority',             checked: true  },
                { sortOrder: 13, reference: 'status',                        description: 'Status',               checked: true  },
                { sortOrder: 14, reference: 'orderDate',                     description: 'Created',              checked: false },
                { sortOrder: 15, reference: 'assessmentCerficateDate',       description: 'Certified',            checked: false },
                { sortOrder: 16, reference: 'jobModifiedOn',                 description: 'Updated',              checked: true  },
            ],
        };
        vm.resetColumnOptions = function () {
            for (var ii = 0, len = vm.columnOptions.columnList.length; ii < len; ii++) {
                vm.columnOptions[vm.columnOptions.columnList[ii].reference] = false;
            }
        }
        vm.getDefaultColumnOptions = function () {
            var savedState = JSON.parse(localStorage.getItem(vm.filterLocalName));
            if (savedState) {
                vm.columnOptions = savedState;

                var filterColumns = [];
                for (var ii = 0, len = vm.columnOptions.columnList.length; ii < len; ii++) {
                    if (vm.columnOptions[vm.columnOptions.columnList[ii].reference]) {
                        filterColumns.push(vm.columnOptions.columnList[ii].reference);
                    }
                }
                vm.filterColumns = filterColumns;
            } else {
                vm.filterColumns = ['jobReference', 'client', 'creator', 'clientRef', 'owner', 'orderDate',
                    'jobModifiedOn', 'assessmentVersion', 'assignee', 'assessor', 'priority', 'status', 'projectAddress', 'projectDescription', 'assessmentDesign']
            }
        }
        vm.getDefaultColumnOptions();
        vm.columnSelectionsChanged = function () {
            let theTable = document.getElementsByClassName("job-list-table")[0];
            Object.keys(vm.columnOptions).forEach(key => {
                if (vm.columnOptions[key] == true) {
                    theTable.classList.add(`show-${key}`);
                } else {
                    theTable.classList.remove(`show-${key}`);
                }
            });
        }
        vm.getPriorityColour = function (row, index) {
            if (row.assessmentStatusCode == 'AComplete' || row.assessmentStatusCode == 'AIssued')
                return "";

            var isEven = false;
            if (index % 2 == 0)
                isEven = true;

            var expiresOn = new Date(row.assessmentCreatedOn);
            var turnaroundHours = 0;
            for (var ii = 0, len = vm.priorityList.length; ii < len; ii++) {
                if (vm.priorityList[ii].priorityCode == row.assessmentPriorityCode) {
                    turnaroundHours = vm.priorityList[ii].turnaroundHours;
                    break;
                }
            }

            if (turnaroundHours == 0)
                return "";

            expiresOn.setHours(expiresOn.getHours() + turnaroundHours);

            if (Date.now() > expiresOn) {
                if (isEven == true)
                    return "table-striped-red";
                else
                    return "table-striped-red-light";
            }

            return "";
        }
        vm.determineCanEditJob = function (job) {
            let currentUserId = securityservice.currentUser.rssUserId;
            if (job.assessorUserId == currentUserId && vm.permission_canAccessSelfAssigned) {
                return true;
            }
            else if ((job.assessorUserId != currentUserId || job.assessorUserId == null) && vm.permission_canAccessOtherAssigned) {
                return true;
            }
            return false;
        }
        vm.goToJob = function (job) {
            if (vm.determineCanEditJob(job)) {
                $state.go("assessment-updateform", { assessmentId: job.currentAssessmentId });
            }
        }

        // -------------- //
        // - INITIALISE - //
        // -------------- //

        vm.initialise = function () {
            priorityservice.getList().then(result => {
                if (result == undefined || result == null)
                    return; // Its been cancelled so get out of here.
                vm.priorityList = result.data;
                vm.columnSelectionsChanged();
            });
            vm.clearFilters();
        }

        vm.initialRefreshList = function () {
            console.time('TheGoodOne');
            vm.initialiseComplete = false;

            // Use Promise.all to make concurrent API calls
            Promise.all([
                jobservice.getList(null, null, 100, 1, null, null),
                jobservice.getMultiFilterOptions(false, vm.filteredFilters)
            ]).then(results => {
                // Process jobs data (first promise result)
                const jobsResult = results[0];
                vm.jobsList = jobsResult.data;
                vm.totalJobs = jobsResult.total;
                vm.totalFilteredJobs = vm.totalJobs;
                var pageSize = saveTableState?.pagination?.number ?? 100; // Number of entries showed per page.
                if (saveTableState != null) {
                    saveTableState.pagination.numberOfPages = Math.ceil(jobsResult.total / pageSize);
                }

                // Set data
                vm.jobsList.forEach((job, index) => {
                    // Priority colours
                    job.priorityColour = vm.getPriorityColour(job, index);
                    // Can edit job
                    job.canEditJob = vm.determineCanEditJob(job);
                });

                // Process filter options (second promise result)
                const filterOptions = results[1];
                vm.filterOptions = filterOptions;
                common.orderFilterOptions(vm.filterOptions);
                common.adjustFiltersForSelection(vm.filteredFilters, vm.appliedFilters, vm.appliedFiltersOld);
                vm.appliedFiltersOld = angular.copy(vm.appliedFilters);

                // Create search filter for additional API calls
                let searchFilter = [];
                for (let i = 0; i < vm.searchFields.length; i++) {
                    if (i == vm.searchFields.length-1) {
                        searchFilter.push({ field:vm.searchFields[i], operator: "contains", value: vm.searchString })
                    } else {
                        searchFilter.push({ field:vm.searchFields[i], operator: "contains", value: vm.searchString, logic: "or" })
                    }
                }

                // Make additional API calls after both initial calls are complete
                Promise.all([
                    jobservice.getFilterCountData(vm.filteredFilters, vm.filterOptions, vm.filteredAppliedFilters(), searchFilter),
                    jobanalyticsservice.getAnalytics(0, null, null, false, true, vm.filteredFilters, vm.filterOptions, vm.filteredAppliedFilters(), searchFilter)
                ]).then(additionalResults => {
                    // Process filter count data
                    vm.filterCountData = additionalResults[0];
                    vm.clearFilterCaches();
                    vm.updateFilterCountLookup();

                    // Process analytics data
                    const analyticsData = additionalResults[1];
                    vm.certificationList = analyticsData?.data?.certificationList;
                    vm.availableComplianceMethods = analyticsData?.data?.availableComplianceMethods;

                    // Check if any filters are applied and set the flag
                    vm.filtersApplied = vm.anyFiltersApplied(vm.searchString, vm.filteredFilters, vm.appliedFilters);

                    // Update UI
                    vm.showingFromCnt = vm.totalJobs > 0 ? 1 : 0;
                    vm.showingToCnt = vm.jobsList.length;
                    vm.spinnerBusy = false;
                    $timeout(() => vm.initialiseComplete = true, 1000);
                });
            });
            //console.timeEnd('TheGoodOne');
        }

        vm.refreshList = function (tableState) {

            if (tableState != null)
                saveTableState = tableState;

            vm.sortBy = {};
            if (saveTableState?.sort != null) {
                vm.sortBy.field = saveTableState.sort.predicate;
                vm.sortBy.dir = saveTableState.sort.reverse ? "desc" : "asc";
            }

            if ( vm.initialiseComplete && (!angular.equals(vm.appliedFilters, vm.appliedFiltersOld) || !angular.equals(vm.sortBy, vm.sortByOld)) || vm.searchString != vm.searchStringOld ) {

                var start = 0;

                vm.isBusy = true;
                daterangehelper.correctRangeDates(vm.rptDateRange);

                // TODO-341: This should be filtered on the backend by permission I suppose.
                jobservice.getListCancel();
                jobservice.getFilterCountCancel();

                common.adjustFiltersForSelection(vm.filteredFilters, vm.appliedFilters, vm.appliedFiltersOld);
                vm.appliedFiltersOld = angular.copy(vm.appliedFilters);
                let searchFilter = [];
                for (let i = 0; i < vm.searchFields.length; i++) {
                    if (i == vm.searchFields.length-1) {
                        searchFilter.push({ field:vm.searchFields[i], operator: "contains", value: vm.searchString })
                    } else {
                        searchFilter.push({ field:vm.searchFields[i], operator: "contains", value: vm.searchString, logic: "or" })
                    }
                }
                jobservice.getListMultiFiltered(resultsPageSize, 1, vm.sortBy, vm.filteredFilters, vm.filterOptions, vm.filteredAppliedFilters(), searchFilter).then(
                    result => {
                        if (result == undefined || result == null)
                            return; // Its been cancelled so get out of here.
                        vm.currentFilter = jobservice.currentFilter();
                        vm.jobsList = result.data;
                        // Set data
                        vm.jobsList.forEach((job, index) => {
                            // Priority colours
                            job.priorityColour = vm.getPriorityColour(job, index);
                            // Can edit job
                            job.canEditJob = vm.determineCanEditJob(job);
                        });
                        vm.totalFilteredJobs = result.total;
                        vm.filtersApplied = vm.anyFiltersApplied(vm.searchString, vm.filteredFilters, vm.appliedFilters);
                        if (saveTableState != null)
                            saveTableState.pagination.numberOfPages = Math.ceil(result.total / resultsPageSize);
                        vm.showingFromCnt = vm.totalJobs > 0 ? start + 1 : 0;
                        vm.showingToCnt = start + (result.data?.length??0);
                        vm.sortByOld = angular.copy(vm.sortBy);
                        vm.searchStringOld = vm.searchString;
                        let searchFilter = [];
                        for (let i = 0; i < vm.searchFields.length; i++) {
                            if (i == vm.searchFields.length-1) {
                                searchFilter.push({ field:vm.searchFields[i], operator: "contains", value: vm.searchString })
                            } else {
                                searchFilter.push({ field:vm.searchFields[i], operator: "contains", value: vm.searchString, logic: "or" })
                            }
                        }
                        jobservice.getFilterCountData(vm.filteredFilters, vm.filterOptions, vm.filteredAppliedFilters(), searchFilter).then(data => {
                            vm.filterCountData = data;
                            vm.clearFilterCaches();
                            vm.updateFilterCountLookup();
                        });
                        jobanalyticsservice.getAnalytics(0, null, null, false, true, vm.filteredFilters, vm.filterOptions, vm.filteredAppliedFilters(), searchFilter).then(data => {
                            vm.certificationList = data?.data?.certificationList;
                            vm.availableComplianceMethods = data?.data?.availableComplianceMethods;
                        });
                        vm.isBusy = false;
                    },
                    error => vm.isBusy = false
                );
            }
        };

        vm.getMoreResults = function () {
            // Use incremental loading for better performance
            vm.getMoreResultsIncremental();
        }

        vm.getMoreResultsIncremental = function () {
            vm.showMoreLoading = true;
            vm.isBusy = true;
            var currentCount = vm.jobsList.length;
            var pageSize = 100;
            var pageIndex = Math.floor(currentCount / 100) + 1;

            // Create search filter for incremental load
            let searchFilter = [];
            if (vm.searchString) {
                for (let i = 0; i < vm.searchFields.length; i++) {
                    if (i == vm.searchFields.length-1) {
                        searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString })
                    } else {
                        searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString, logic: "or" })
                    }
                }
            }

            // Use multi-filter API to maintain consistency with current filters and search
            jobservice.getListMultiFiltered(pageSize, pageIndex, vm.sortBy, vm.filteredFilters, vm.filterOptions, vm.filteredAppliedFilters(), searchFilter).then(function (result) {
                if (result && result.data && result.data.length > 0) {
                    // Filter out any jobs we already have (in case of overlap)
                    var existingJobIds = new Set(vm.jobsList.map(job => job.jobId));
                    var newJobs = result.data.filter(job => !existingJobIds.has(job.jobId));

                    if (newJobs.length > 0) {
                        // Process new jobs for priority colors and permissions
                        newJobs.forEach((job, index) => {
                            job.priorityColour = vm.getPriorityColour(job, vm.jobsList.length + index);
                            job.canEditJob = vm.determineCanEditJob(job);
                        });

                        // Append new results to existing list instead of replacing
                        vm.jobsList = vm.jobsList.concat(newJobs);
                        vm.showingToCnt = vm.jobsList.length;
                    }
                }
                vm.showMoreLoading = false;
                vm.isBusy = false;
            }, function (error) {
                vm.showMoreLoading = false;
                vm.isBusy = false;
                console.error('Error loading more results:', error);
            });
        }

        vm.showAnalytics = function () {
            let searchFilter = [];
            for (let i = 0; i < vm.searchFields.length; i++) {
                if (i == vm.searchFields.length-1) {
                    searchFilter.push({ field:vm.searchFields[i], operator: "contains", value: vm.searchString })
                } else {
                    searchFilter.push({ field:vm.searchFields[i], operator: "contains", value: vm.searchString, logic: "or" })
                }
            }
            var modalScope = $rootScope.$new();
            modalScope.filterData = {
                filters: vm.filteredFilters,
                filterOptions: vm.filterOptions,
                appliedFilters: vm.filteredAppliedFilters(),
                searchFilter: searchFilter
            };
            modalScope.availableComplianceMethods = vm.availableComplianceMethods;
            modalScope.certificationList = vm.certificationList;
            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/job/analytics-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
            })
            .then(response => null);
        }

        vm.jobRowClick = function ($event, row) {
            if ($event.button == 1) {
                var url = $state.href('assessment-updateform', { assessmentId: row.currentAssessmentId}, { absolute: true });
                $window.open(url,'_blank');
            }
        }

        // Permissions for column options
        if (!securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__creator__view")) {
            vm.columnOptions.columnList = vm.columnOptions.columnList.filter(i => i.reference != "creator");
        }
        if (!securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__assignee__view")) {
            vm.columnOptions.columnList = vm.columnOptions.columnList.filter(i => i.reference != "assignee");
        }
        if (!securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__assignedassessor__view")) {
            vm.columnOptions.columnList = vm.columnOptions.columnList.filter(i => i.reference != "assessor");
        }

        vm.initialise();
    }
})();