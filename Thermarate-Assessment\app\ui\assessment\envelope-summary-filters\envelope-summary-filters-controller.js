(function () {

    'use strict';
    angular
        .module('app')
        .component('envelopeSummaryFilters', {
            bindings: {
                building: '<',
                envelopeSummaryObject: '=',
                filtersObject: '='
            },
            templateUrl: 'app/ui/assessment/envelope-summary-filters/envelope-summary-filters.html',
            controller: EnvelopeSummaryFilters,
            controllerAs: 'vm'
        });

    EnvelopeSummaryFilters.$inject = ['$scope', 'common', 'zoneservice', 'zonesummaryservice', 'uuid4'];

    function EnvelopeSummaryFilters($scope, common, zoneservice, zonesummaryservice, uuid4) {

        // ------------- //
        // - VARIABLES - //
        // ------------- //

        let vm = this;

        const SHADING_OPTIONS = [
            { title: 'All',          code: 'ALL' },
            { title: 'No Shading',   code: 'No Shading' },
            { title: 'Minimal',      code: 'Minimal' },
            { title: 'Moderate',     code: 'Moderate' },
            { title: 'High',         code: 'High' },
            { title: 'Obstructive',  code: 'Obstructive' }
        ];

        const CONDITIONED_GROUP_OPTIONS = [
            "All Conditioning Types",
            "All Conditioned Zones",
            "All Unconditioned Zones",
        ];

        vm.GROUP_OPTIONS = zonesummaryservice.groupOptions;
        vm.knownBuildingSummaryGroups = zonesummaryservice.knownBuildingSummaryGroups;
        vm.sectors = zonesummaryservice.defaultSectors;
        vm.selectedStoreys = null;

        // -------------- //
        // - INITIALISE - //
        // -------------- //

        function initialise() {
            // Always reset to fresh default values on component initialization
            // This ensures clean state when component is destroyed and recreated
            vm.filtersObject = {
                id: uuid4.generate(),
                storey: 'ALL',
                group: 'All Zones',
                selection: ['All Zones'],
                // All options selected by default
                wallHorizontalShading: {
                    selectableArray: [...SHADING_OPTIONS],
                    selection: [SHADING_OPTIONS[0]]
                },
                glazingHorizontalShading: {
                    selectableArray: [...SHADING_OPTIONS],
                    selection: [SHADING_OPTIONS[0]]
                },
                wallVerticalShading: {
                    selectableArray: [...SHADING_OPTIONS],
                    selection: [SHADING_OPTIONS[0]]
                },
                glazingVerticalShading: {
                    selectableArray: [...SHADING_OPTIONS],
                    selection: [SHADING_OPTIONS[0]]
                }
            }
            vm.applySelectionLogic(vm.filtersObject.wallHorizontalShading,    SHADING_OPTIONS[0]);
            vm.applySelectionLogic(vm.filtersObject.glazingHorizontalShading, SHADING_OPTIONS[0]);
            vm.applySelectionLogic(vm.filtersObject.wallVerticalShading,      SHADING_OPTIONS[0]);
            vm.applySelectionLogic(vm.filtersObject.glazingVerticalShading,   SHADING_OPTIONS[0]);
            if (vm.building?.openings != null) {
                const storeysWithGlazing = zoneservice.determineStoreysWithGlazing(vm.building.openings);
                vm.selectedStoreys = vm.building.storeys?.filter(x => storeysWithGlazing.some(y => y === x.floor));
            } else {
                vm.selectedStoreys = [{
                    "name": "Ground Floor",
                    "description": "Ground Floor",
                    "floor": 0,
                },{
                    "name": "First Floor",
                    "description": "First Floor",
                    "floor": 1,
                },{
                    "name": "Second Floor",
                    "description": "Second Floor",
                    "floor": 2,
                }];
            }
            vm.applyGroupSelectLogic(vm.filtersObject.group, true);
            vm.calculateEnvelopeSummaryData();
            vm.initialised = true;
        }

        // ----------- //
        // - HANDLES - //
        // ----------- //

        // Calculate Envelope Summary
        vm.calculateEnvelopeSummaryData = function () {
            if (vm.building?.openings != null) {
                vm.envelopeSummaryObject.sectorKeys = [... new Set(vm.sectors.map(sd => sd.label.toLowerCase())), 'total'];
                vm.envelopeSummaryObject.exteriorWallAreaTotalsPerSector = zoneservice.calcPropertyTotalPerSector(vm.building, 'surface', 'ExteriorWall', {sectors: vm.sectors}, [vm.filtersObject]);
                vm.envelopeSummaryObject.exteriorGlazingAreaTotalsPerSector = zoneservice.calcPropertyTotalPerSector(vm.building, 'opening', 'ExteriorGlazing', {sectors: vm.sectors}, [vm.filtersObject]);
                vm.envelopeSummaryObject.glassExteriorWallRatioPerSector = zoneservice.calcGlassExteriorWallRatioPerSector(vm.envelopeSummaryObject.exteriorWallAreaTotalsPerSector, vm.envelopeSummaryObject.exteriorGlazingAreaTotalsPerSector, {sectors: vm.sectors});
                let parents = vm.building.openings.filter(o => o.category.constructionCategoryCode == "ExteriorGlazing");
                let rows = zoneservice.generateConstructionRowsFromElements(vm.building, parents, [vm.filtersObject]);
                vm.envelopeSummaryObject.averageGlazingUValuePerSector = rows.length == 0 ? zoneservice.defaultSectorsData : zoneservice.calcAverageGlazingUValuePerSector(rows, {sectors: vm.sectors});
                vm.envelopeSummaryObject.averageGlazingSHGCPerSector = rows.length == 0 ? zoneservice.defaultSectorsData : zoneservice.calcAverageGlazingSHGCPerSector(rows, {sectors: vm.sectors});
            }
        }

        vm.applySelectionLogic = function (filter, selected) {
            setTimeout(() => {
                // SELECT or DE-SELECT all selections when the 'ALL' option is clicked.
                if (selected.code == "ALL" && filter.selection.some(x => x.code == "ALL")) {
                    filter.selection = filter.selectableArray;
                } else if (selected.code == "ALL" && !filter.selection.some(x => x.code == "ALL")) {
                    filter.selection = [];
                } else {
                    if (filter.selection) {
                        // Just make sure to de-select the "ALL" checkbox if anything else is selected.
                        filter.selection = filter.selection.filter(x => x.code !== "ALL");
                    }
                }
                vm.calculateEnvelopeSummaryData();
                $scope.$apply();
            }, 50);
        }

        vm.applyGroupSelectLogic = function (group, firstRun) {
            setTimeout(() => {
                if (vm.building?.openings != null) {
                    // Refine selection to only show what is available.
                    let allExtGlazingElements = [];
                    if (vm.building.openings.length > 0) {
                        allExtGlazingElements = vm.building.openings.filter(x => x.category.constructionCategoryCode === "ExteriorGlazing")
                                                                    .map(x => x.elements)
                                                                    .reduce((a, b) => [...a, ...b]);
                    }
                    let allExtWallElements = [];
                    if (vm.building.surfaces.length > 0) {
                        allExtWallElements = vm.building.surfaces.filter(x => x.category.constructionCategoryCode === "ExteriorWall")
                                                            .map(x => x.elements)
                                                            .reduce((a, b) => [...a, ...b]);
                    }
                    let zonesWithExtGlazingOrExtWalls = zoneservice.interiorZones(vm.building.zones)
                                                                    .filter(zone =>
                                                                        allExtGlazingElements.some(y => y.parentZoneId === zone.linkId)
                                                                        || allExtWallElements.some(y => y.parentZoneId === zone.linkId)
                                                                    );
                    if (vm.filtersObject.storey != null && vm.filtersObject.storey !== "ALL") {
                        zonesWithExtGlazingOrExtWalls = zonesWithExtGlazingOrExtWalls.filter(x => x.storey === vm.filtersObject.storey.floor);
                        allExtGlazingElements = allExtGlazingElements.filter(x => x.storey === vm.filtersObject.storey.floor);
                    }
                    if (vm.filtersObject.group === "All Zones")
                        vm.filtersObject.selectableArray = zonesummaryservice.allZonesOptions;
                    if (vm.filtersObject.group === "Zone Name")
                        vm.filtersObject.selectableArray = presentZoneNames(zonesWithExtGlazingOrExtWalls);
                    if (vm.filtersObject.group === "Zone Activity")
                        vm.filtersObject.selectableArray = presentZoneActivities(zonesWithExtGlazingOrExtWalls);
                    if (vm.filtersObject.group === "Zone Type")
                        vm.filtersObject.selectableArray = presentZoneTypes(zonesWithExtGlazingOrExtWalls);
                    if (vm.filtersObject.group === "Conditioning")
                        vm.filtersObject.selectableArray = CONDITIONED_GROUP_OPTIONS;
                    if (vm.filtersObject.group === "NCC Classification")
                        vm.filtersObject.selectableArray = presentNccClassifications(zonesWithExtGlazingOrExtWalls);
                    if (group === "All Zones")
                        vm.filtersObject.selection = ["All Zones"];
                    if (firstRun) {
                        vm.filtersObject.selection = common.linkVariablesBetweenArrays(vm.filtersObject.selection, vm.filtersObject.selectableArray, 'code');
                    }
                    vm.calculateEnvelopeSummaryData();
                } else {
                    if (vm.filtersObject.group === "All Zones")
                        vm.filtersObject.selectableArray = zonesummaryservice.allZonesOptions;
                    if (vm.filtersObject.group === "Zone Name")
                        vm.filtersObject.selectableArray = presentZoneNames();
                    if (vm.filtersObject.group === "Zone Activity")
                        vm.filtersObject.selectableArray = presentZoneActivities();
                    if (vm.filtersObject.group === "Zone Type")
                        vm.filtersObject.selectableArray = presentZoneTypes();
                    if (vm.filtersObject.group === "Conditioning")
                        vm.filtersObject.selectableArray = CONDITIONED_GROUP_OPTIONS;
                    if (vm.filtersObject.group === "NCC Classification")
                        vm.filtersObject.selectableArray = presentNccClassifications();
                    if (group === "All Zones")
                        vm.filtersObject.selection = ["All Zones"];
                }
            }, 50)
        }

        function presentZoneNames(zones){
            // Figure out which unique opening styles are present in the construction data.
            const uniques = {};
            zones.forEach(x => {
                uniques[x.zoneDescription] = { title: x.zoneDescription, code: x.zoneDescription };
            });
            let converted = [{ title: 'All Zone Names', code: 'ALL' }];
            for (let prop in uniques)
                converted.push(uniques[prop]);
            return converted;
        }

        function presentZoneActivities(allZones) {
            if (vm.building == null) {
                return [
                    { "title": "All Zones Activities", "code": "ALL" },
                    { "title": "Bedroom",              "code": "ZABedroom" },
                    { "title": "Living",               "code": "ZALiving" },
                    { "title": "Day Time",             "code": "ZADayTime" },
                    { "title": "Night Time",           "code": "ZANightTime" },
                    { "title": "Unconditioned",        "code": "ZAUnconditioned" },
                    { "title": "Kitchen / Living",     "code": "ZAKitchenLiving" },
                    { "title": "Garage",               "code": "ZAGarage" },
                    { "title": "Garage Conditioned",   "code": "ZAGarageConditioned" }
                ];
            } else {
                // Figure out which unique opening styles are present in the construction data.
                const uniques = {};
                uniques["All Zone Activities"] = { title: "All Zones Activities", code: "ALL" };
                allZones.forEach(x => {
                    uniques[x.zoneActivity.description] = {
                        title: x.zoneActivity.description,
                        code:  x.zoneActivity.zoneActivityCode
                    };
                });
                let converted = [];
                for (let prop in uniques)
                    converted.push(uniques[prop]);
                return converted;
            }
        }

        function presentZoneTypes(allZones) {
            if (vm.building == null) {
                return [
                    { "title": "All Zones Types", "code": "ALL" },
                    { "title": "Habitable Room", "code": "ZTHabitableRoom" },
                    { "title": "Non-Habitable Room", "code": "ZTNonHabitableRoom" },
                    { "title": "Interconnecting Space", "code": "ZTInterconnecting" },
                    { "title": "Class 10a", "code": "ZTClass10A" }
                ];
            } else {
                // Figure out which unique opening styles are present in the construction data.
                const uniques = {};
                uniques["All Zone Types"] = { title: "All Zones Types", code: "ALL" };
                allZones.forEach(x => {
                    uniques[x.zoneType.description] = {
                        title: x.zoneType.description,
                        code:  x.zoneType.zoneTypeCode
                    };
                });
                let converted = [];
                for (let prop in uniques)
                    converted.push(uniques[prop]);
                return converted;
            }
        }

        function presentNccClassifications(allZones) {
            if (vm.building == null) {
                return [
                    { "title": "All Classifications", "code": "ALL" },
                    { "title": "Class 1a", "code": "Class1A" },
                    { "title": "Class 10a", "code": "Class10A" }
                ];
            } else {
                // Figure out which unique opening styles are present in the construction data.
                const uniques = {};
                uniques["All Classifications"] = { title: "All Classifications", code: "ALL" };
                allZones.forEach(x => {
                    if (x.nccClassification == null)
                        return;
                    uniques[x.nccClassification?.description] = {
                        title: x.nccClassification.description,
                        code:  x.nccClassification.nccClassificationCode
                    };
                });
                let converted = [];
                for (let prop in uniques)
                    converted.push(uniques[prop]);
                return converted;
            }
        }

        vm.sectorFromLabel = function (label, sectors) {
            return sectors.find(x => x.label.toLowerCase() === label);
        }

        /**
         * Envelope Summary Only - Checks if a Sector total area column is empty (for hiding)
         *
         * @param   {string} sector 'n', 'ne', 'e', 'se', 's', 'sw', 'w', 'nw'
         * @returns {boolean}
        */
        vm.isSectorNotEmpty = function (sector, envelopeSummaryData) {
            if (sector === 'total' || envelopeSummaryData.isPlaceholder)
                return true; // Always show total.

            // Data might be have loaded yet
            if (!envelopeSummaryData.exteriorWallAreaTotalsPerSector || !envelopeSummaryData.exteriorGlazingAreaTotalsPerSector)
                return false;

            const isWallNotEmpty = envelopeSummaryData.exteriorWallAreaTotalsPerSector.hasOwnProperty(sector) &&
                (envelopeSummaryData.exteriorWallAreaTotalsPerSector[sector].area && envelopeSummaryData.exteriorWallAreaTotalsPerSector[sector].percentage);
            const isGlazingNotEmpty = envelopeSummaryData.exteriorGlazingAreaTotalsPerSector.hasOwnProperty(sector) &&
                (envelopeSummaryData.exteriorGlazingAreaTotalsPerSector[sector].area && envelopeSummaryData.exteriorGlazingAreaTotalsPerSector[sector].percentage);

            // A facade should be shown if EITHER the Exterior Wall Area OR Exterior Glazing Area is greater than 0
            return isWallNotEmpty || isGlazingNotEmpty;
        }

        // ------------------ //
        // - RUN INITIALISE - //
        // ------------------ //

        initialise();

    }
})();