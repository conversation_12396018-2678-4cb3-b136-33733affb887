(function () {
    'use strict';
    angular
        .module('app')
        .component('complianceOptions', {
            bindings: {
                assessment: '<',
                complianceMethodList: '<',
                onSelectedChanged: '&',
                purchaseOrderDefault: '<',
                assessmentSoftwareList: '<',
                jobFiles: '<',
                isLocked: '<',
                enableSelection: '<',
                setFinalComplianceMethod: '&'
            },
            templateUrl: 'app/ui/assessment/assessment-outcomes/compliance-options/compliance-options.html',
            controller: ComplianceOptions,
            controllerAs: 'vm'
        });

    ComplianceOptions.$inject = ['$rootScope', 'uuid4', '$mdDialog', 'assessmentcomplianceoptionservice', 
        'compliancemethodservice', 'security'];

    function ComplianceOptions($rootScope, uuid4, $mdDialog, assessmentcomplianceoptionservice, 
                               compliancemethodservice, securityservice) {

        var _initialComplianceMethod = null;
        var vm = this;

        vm.$onChanges = onChanges;
        vm.addOption = addOption;
        vm.removeOption = removeOption;
        vm.cloneOption = cloneOption;
        vm.clearOption = clearOption;
        vm.onSelect = onSelect;

        vm.selectPermissionEnabled = securityservice.immediateCheckRoles('assessment_actions__selectcompliance');

        function onChanges(changes) {

            if (changes.complianceMethodCode &&
                changes.complianceMethodCode.currentValue != changes.complianceMethodCode.previousValue &&
                changes.complianceMethodCode.currentValue) {

                if (_initialComplianceMethod == null) {
                    _initialComplianceMethod = changes.complianceMethodCode.currentValue;
                }

                calculateOptionIndices();
            }

            if (changes.assessment && vm.assessment.allComplianceOptions) {
                var selected = vm.assessment.allComplianceOptions.find(function (x) {
                    return x.isSelected;
                });

                if (selected) {
                    vm.onSelectedChanged({ code: selected.complianceMethodCode });
                }
                else {
                    vm.onSelectedChanged({ code: null });
                }
                calculateOptionIndices();
            }

        }

        /** Determines the index of each compliance option. Use when adding or removing to the list. */
        function calculateOptionIndices() {

            var total = vm.assessment.allComplianceOptions.length;

            for (var i = 0; i < total; i++) {
                vm.assessment.allComplianceOptions[i].optionIndex = i;
            }
        }

        function addOption() {

            let baseline = vm.assessment.allComplianceOptions[0];

            const copyProposedEnergy = true;

            const copyReferenceEnergy =
              baseline.complianceMethod.complianceMethodCode === "CMPerfSolution" ||
              baseline.complianceMethod.complianceMethodCode === "CMPerfSolutionDTS";

            // Under certain conditions, we copy the reference design tab, otherwise we copy the proposed (even if it
            // is technically 'hidden'...
            const copyReferenceDesignTab =
                baseline.complianceMethod.complianceMethodCode === "CMPerfSolution" ||
                baseline.complianceMethod.complianceMethodCode === "CMPerfSolutionDTS";

            var option = {
                assessmentId: vm.assessment.assessmentId,
                isSelected: false,
                optionIndex: vm.assessment.allComplianceOptions.length, // 0 based remember
                updatedDrawingsUploadCount: 0,
                updatedDrawingsRequired: null,
                complianceMethod: baseline.complianceMethod,
                complianceMethodCode: baseline.complianceMethod.complianceMethodCode,
                epComplianceData: {},
                complianceOptionsId: uuid4.generate(),
                requiredHouseEnergyRating: baseline.requiredHouseEnergyRating,
                heatingAndCoolingRulesetCode: baseline.heatingAndCoolingRulesetCode,

                certification: baseline.certification,
                sectorDetermination: baseline.sectorDetermination,

                // Copy across some, but not all, values.
                // NOTE: We now default to 'required' or 'null' values for all construction data.
                assessmentSoftware: baseline.assessmentSoftware,
                isComplianceValid: true,
                isShownToClient: false,

                // Alistair wants these to default to null after all.
                proposed: {
                    assessmentComplianceBuildingId: uuid4.generate(),
                    zones: angular.copy(baseline.proposed.zones),
                    storeys: angular.copy(baseline.proposed?.storeys),
                    buildingZonesTemplateId: baseline.proposed.buildingZonesTemplateId,
                    projectDescription: baseline.proposed.projectDescription,
                    projectDescriptionOther: baseline.proposed.projectDescriptionOther,
                    projectClassification: baseline.proposed.projectClassification,
                    design: baseline.proposed.design,
                    buildingOrientation: baseline.proposed.buildingOrientation,
                    designFeatures: angular.copy(baseline.proposed.designFeatures),
                    buildingLength: baseline.proposed.buildingLength,
                    buildingWidth: baseline.proposed.buildingWidth,

                    // These now (potentially) get replaced in the modal.
                    services: angular.copy(baseline.proposed.services),
                    surfaces: angular.copy(baseline.proposed.surfaces),
                    openings: angular.copy(baseline.proposed.openings),
                    spaces: angular.copy(baseline.proposed.spaces),
                    roofs: angular.copy(baseline.proposed.roofs),
                    categoriesNotRequired: angular.copy(baseline.proposed.categoriesNotRequired),
                    categoriesWithExternalData: angular.copy(baseline.proposed.categoriesWithExternalData),
                    zoneTypesNotApplicable: angular.copy(baseline.proposed.zoneTypesNotApplicable),

                    heating: copyProposedEnergy ? baseline.proposed.heating : null,
                    cooling: copyProposedEnergy ? baseline.proposed.cooling : null,
                    totalEnergyLoad: copyProposedEnergy ? baseline.proposed.totalEnergyLoad : null,

                    // Used in modal, temp only.
                    newBuildingZoneTemplateId: "BLANK_TEMPLATE",
                    newConstructionTemplateId: "BLANK_TEMPLATE",
                    newOpeningTemplateId: "BLANK_TEMPLATE",
                    newServicesTemplateId: "BLANK_TEMPLATE",

                },
                reference: {
                    assessmentComplianceBuildingId: uuid4.generate(),
                    zones: angular.copy(copyReferenceDesignTab ? baseline.reference.zones : baseline.proposed.zones),
                    storeys: angular.copy(copyReferenceDesignTab ? baseline.reference?.storeys : baseline.proposed.storeys),
                    buildingZonesTemplateId: copyReferenceDesignTab ? baseline.reference.buildingZonesTemplateId : baseline.proposed.buildingZonesTemplateId,
                    projectDescription: copyReferenceDesignTab ? baseline.reference.projectDescription : baseline.proposed.projectDescription,
                    projectDescriptionOther: copyReferenceDesignTab ? baseline.reference.projectDescriptionOther : baseline.proposed.projectDescriptionOther,
                    projectClassification: copyReferenceDesignTab ? baseline.reference.projectClassification : baseline.proposed.projectClassification,
                    design: copyReferenceDesignTab ? baseline.reference.design : baseline.proposed.design,
                    buildingOrientation: copyReferenceDesignTab ? baseline.reference.buildingOrientation : baseline.proposed.buildingOrientation,
                    designFeatures: angular.copy(copyReferenceDesignTab ? baseline.reference.designFeatures : baseline.proposed.designFeatures),
                    buildingLength: copyReferenceDesignTab ? baseline.reference.buildingLength : baseline.proposed.buildingLength,
                    buildingWidth: copyReferenceDesignTab ? baseline.reference.buildingWidth : baseline.proposed.buildingWidth,

                    // These now (potentially) get replaced in the modal. Note that we take the baseline proposed value if the
                    // baseline has no reference building (determined via compliance method)
                    services: angular.copy(copyReferenceDesignTab ? baseline.reference.services : baseline.proposed.services),
                    surfaces: angular.copy(copyReferenceDesignTab ? baseline.reference.surfaces : baseline.proposed.surfaces),
                    openings: angular.copy(copyReferenceDesignTab ? baseline.reference.openings : baseline.proposed.openings),
                    spaces: angular.copy(copyReferenceDesignTab ? baseline.reference.spaces : baseline.proposed.spaces),
                    roofs: angular.copy(copyReferenceDesignTab ? baseline.reference.roofs : baseline.proposed.roofs),
                    categoriesNotRequired: angular.copy(copyReferenceDesignTab ? baseline.reference.categoriesNotRequired : baseline.proposed.categoriesNotRequired),
                    categoriesWithExternalData: angular.copy(copyReferenceDesignTab ? baseline.reference.categoriesWithExternalData : baseline.proposed.categoriesWithExternalData),
                    zoneTypesNotApplicable: angular.copy(copyReferenceDesignTab ? baseline.reference.zoneTypesNotApplicable : baseline.proposed.zoneTypesNotApplicable),

                    heating: copyReferenceEnergy ? baseline.reference.heating : null,
                    cooling: copyReferenceEnergy ? baseline.reference.cooling : null,
                    totalEnergyLoad: copyReferenceEnergy ? baseline.reference.totalEnergyLoad : null,

                    // Used in modal, temp only.
                    newBuildingZoneTemplateId: "BLANK_TEMPLATE",
                    newConstructionTemplateId: "BLANK_TEMPLATE",
                    newOpeningTemplateId: "BLANK_TEMPLATE",
                    newServicesTemplateId: "BLANK_TEMPLATE",
                },
            };

            option.proposed.zones.forEach(x => x.zoneId = uuid4.generate());
            option.reference.zones.forEach(x => x.zoneId = uuid4.generate());

            // We do NOT push this just yet. Instead, throw up a modal to allow more fine-grained control.
            const addOptionScope = $rootScope.$new();

            addOptionScope.assessment = vm.assessment;
            addOptionScope.baseline = vm.assessment.allComplianceOptions[0];
            addOptionScope.option = option;
            addOptionScope.complianceMethodList = vm.complianceMethodList;

            $mdDialog.show({

                templateUrl: 'app/ui/assessment/assessment-outcomes/compliance-options/add-option-modal.html',
                scope: addOptionScope

            }).then(response => {

                // TODO: Why does this take several seconds to populate in the DOM?
                // Ok so what we are doing here is removing all the heavy information which gets crammed into the DOM
                // in the construction/openings/services tabs and delaying them for a second or so, so that the 
                // performance of the compliance option insert is a bit snappier (Still not great tho).
                const backup = {
                    proposed: {
                        surfaces: response.proposed.surfaces,
                        openings: response.proposed.openings,
                        services: response.proposed.services,
                    },
                    reference: {
                        surfaces: response.reference.surfaces,
                        openings: response.reference.openings,
                        services: response.reference.services,
                    },
                };

                response.proposed.surfaces = [];
                response.proposed.openings = [];
                response.proposed.services = [];

                response.reference.surfaces = [];
                response.reference.openings = [];
                response.reference.services = [];

                vm.assessment.allComplianceOptions.push(response);
                safeApply();

                setTimeout(() => {
                    response.proposed.surfaces = backup.proposed.surfaces;
                    response.proposed.openings = backup.proposed.openings;
                    response.proposed.services = backup.proposed.services;

                    response.reference.surfaces = backup.reference.surfaces;
                    response.reference.openings = backup.reference.openings;
                    response.reference.services = backup.reference.services;
                }, 1500);

                // temp variable that will disallow us selecting this option until we save the assessment.
                response.tempNotSavedInDb = true; 

                // Soft create new Option with Deleted = TRUE, so can upload drawings to this Compliance Option now and won't cause foreign key issue, then will un-delete when save Assessment
                assessmentcomplianceoptionservice.createAssessmentComplianceOption(option, true);

            }, function(){ /* Do nothing on cancel */ });

        }

        function safeApply() {
            const phase = $rootScope.$$phase;
            if (!phase) {
                $rootScope.$apply();
            }
        }

        vm.complianceTypeChanged = function (option) {

            // NOTE: In the below, option.complianceMethodCode is still the OLD value.
            // When changing from (HER or DTS-HER) ---> (VURB or DTS-EP) THEN
            //  DO NOT copy reference.heating, reference.cooling, reference.totalEnergyLoad
            //  DO COPY proposed.heating, proposed.cooling, proposed.totalEnergyLoad
            if((option.complianceMethodCode === "CMHouseEnergyRating" || 
                option.complianceMethodCode === "CMPerfSolutionHER" ||
                option.complianceMethodCode === "CMPerfWAProtocolHER" || 
                option.complianceMethodCode === "CMPerfELL") 
              && 
              (option.complianceMethod.complianceMethodCode === "CMPerfSolution" || 
                option.complianceMethod.complianceMethodCode === "CMPerfSolutionDTS")) {

                option.reference.heating = null;
                option.reference.cooling = null;
                option.reference.totalEnergyLoad = null;

                // Also copy across baseline data into reference (since HER etc don't feature a reference building)
                option.reference.zones = angular.copy(option.proposed.zones); 
                option.reference.storeys = angular.copy(option.proposed?.storeys); 
                option.reference.buildingZonesTemplateId = option.proposed.buildingZonesTemplateId; 
                option.reference.projectDescription = option.proposed.projectDescription; 
                option.reference.projectDescriptionOther = option.proposed.projectDescriptionOther; 
                option.reference.projectClassification = option.proposed.projectClassification; 
                option.reference.design = option.proposed.design; 
                option.reference.buildingOrientation = option.proposed.buildingOrientation; 
                option.reference.designFeatures = angular.copy(option.proposed.designFeatures);

                option.reference.zones.forEach(x => x.zoneId = uuid4.generate());

            }

            option.complianceMethodCode = option.complianceMethod.complianceMethodCode;
            option.availableSoftware = compliancemethodservice.determineAvailableSoftware(vm.assessmentSoftwareList, option.complianceMethodCode);

            // When changing from (VURB or DTS-EP) ---> (HER or DTS-HER) THEN
            //  DO NOT copy reference.heating, reference.cooling, reference.totalEnergyLoad <--- This will 'update' these values.
            if(option.complianceMethod.complianceMethodCode === "CMHouseEnergyRating" ||
               option.complianceMethod.complianceMethodCode === "CMPerfSolutionHER" ||
               option.complianceMethod.complianceMethodCode === "CMPerfWAProtocolHER" ||
               option.complianceMethod.complianceMethodCode === "CMPerfELL") {
                compliancemethodservice.calculateHERCompliance(vm.assessment, option);
            }
        }

        vm.deleteInProgress = false;
        function removeOption(id) {

            vm.deleteInProgress = true;

            var ind = vm.assessment.allComplianceOptions.findIndex(function (x) {
                return x.complianceOptionsId == id;
            });

            // Can't delete baseline, ever.
            if(ind === 0) {
                vm.deleteInProgress = false;
                return; 
            }

            vm.assessment.allComplianceOptions.splice(ind, 1);
            calculateOptionIndices();

            vm.deleteInProgress = false;

        } 

        /** Creates a clone of the given option and inserts it into the list below the given option. */
        function cloneOption(option) {

            let n = angular.copy(option);

            n.complianceOptionsId = uuid4.generate();

            n.proposed.assessmentComplianceBuildingId = uuid4.generate();
            n.proposed.assessmentComplianceOptionId = n.complianceOptionsId;

            n.reference.assessmentComplianceBuildingId = uuid4.generate();
            n.reference.assessmentComplianceOptionId = n.complianceOptionsId;

            // Need to do an actual copy of every drawing...??
            n.assessmentDrawings.forEach(drawing => {

                drawing.assessmentDrawingId = uuid4.generate();
                drawing.complianceOptionId = n.complianceOptionsId;

            });

            // Put this directly after the original compliance option.
            vm.assessment.allComplianceOptions.splice(option.optionIndex, 0, n);

            calculateOptionIndices();
        }

        /** Clears the given option of _most_ values, attached files, building elements etc. */
        function clearOption(option) {

            let baseline = vm.assessment.allComplianceOptions[0]; // We need to pick some default values from the current baseline.

            option.description = "";
            option.purchaseOrder = null;
            option.purchaseOrderFileId = null;
            option.purchaseOrderFile = null;
            option.isCompliant = false;
            option.assessmentSoftwareCode = null;

            option.updatedDrawingsRequired = null;
            option.newPurchaseOrderRequired = false;
            option.markupFileRequired = false;

            option.requiredHouseEnergyRating = null;

            option.servicesNotRequired = null;

            let proposedStoreys = [];
            let referenceStoreys = [];

            baseline.proposed?.storeys?.forEach(floor => proposedStoreys.push({}));
            baseline.reference?.storeys?.forEach(floor => referenceStoreys.push({}));

            option.epComplianceData = {};

            option.assessmentDrawings = [];

            // I think we probably wish to retain these.
            let createdByName = option.proposed.createdByName;
            let createdOn = option.proposed.createdOn;
            let originalProposedId = option.proposed.assessmentComplianceBuildingId;
            option.proposed = {
                storeys: proposedStoreys,
                assessmentComplianceBuildingId: originalProposedId,
                assessmentComplianceOptionId: option.complianceOptionsId,
                createdByName: createdByName,
                createdOn: createdOn,
                services: [],
                surfaces: [],
                openings: [],
                categoriesWithExternalData: {},
                categoriesNotRequired: {},
                zoneTypesNotApplicable: {},
            };

            let originalReferenceId = option.reference.assessmentComplianceBuildingId;
            option.reference = {
                storeys: referenceStoreys,
                assessmentComplianceBuildingId: originalReferenceId,
                assessmentComplianceOptionId: option.complianceOptionsId,
                createdByName: createdByName,
                createdOn: createdOn,
                services: [],
                surfaces: [],
                openings: [],
                categoriesWithExternalData: {},
                categoriesNotRequired: {},
                zoneTypesNotApplicable: {},
            };
        }

        // Check if the given option is now compliant, and if it isn't, deselect it
        vm.checkCompliance = function (id) {

            // A more appropriate name for this function would be "deselectComplianceOptionsExceptForGiven"

            // Selecting and deselecting happens automatically as our checkbox ng-models are already
            // set to the given values. Therefore...:

            // This function UNSELECTS the compliance options that do NOT match the ID.
            // However, if the given ID's state is ALREADY unselected, we do not unselect anything else.
            // This was causing problems on-load, as onSelect would be fired multiple times from checkbox's
            // being set to their (correct) unselected state, but at the same time unselecting the
            // checkbox which was ticked at save time.

            // Non-null if the compliance option that has been 'selected' has actually been set
            // to true (i.e. selected).
            var switchedToSelected = vm.assessment.allComplianceOptions.find(function (x) {
                return x.isSelected == true && x.complianceOptionsId == id;
            });

            if (switchedToSelected != null) {
                // Our checkbox has been switched to 'isSelected', therefore we must unselect
                // the other checkboxes.
                for (var i = 0; i < vm.assessment.allComplianceOptions.length; i++) {
                    var el = vm.assessment.allComplianceOptions[i];
                    el.isSelected = el.complianceOptionsId == id ? el.isSelected : false;
                }
            }

            // Now actually make sure something is selected. If it isn't,
            // default to our baseline run.
            var selected = vm.assessment.allComplianceOptions.find(function (x) {
                return x.isSelected;
            });

            // If nothing was actually selected, default to baseline run.
            if (selected == null) {

                vm.assessment.allComplianceOptions[0].isSelected = true;
                selected = vm.assessment.allComplianceOptions[0];

                // This should only be called for baseline run from THIS function.
                setSelected(vm.assessment.allComplianceOptions[0]);
                vm.setFinalComplianceMethod();
            }

        }

        // Fired when the user actually de/selects the checkbox.
        // For options, will launch the popup window to add drawings and so on or
        // on deselect erasse everything.
        function onSelect(option) {

            if (option.isSelected && option.isCompliant) {

                // Alistair has asked us to show this popup even if they don't need to update the
                // PO or Drawings. This is because their is a small disclaimer on the modal.

                var newScope = $rootScope.$new();
                newScope.assessment = vm.assessment;
                newScope.option = option;
                newScope.purchaseOrderCode = vm.purchaseOrderDefault;

                $mdDialog.show({
                    templateUrl: 'app/ui/assessment/assessment-outcomes/compliance-options/onselect-popup.html',
                    scope: newScope,
                })
                .then(async function (response) {

                    setSelected(option);

                    vm.setFinalComplianceMethod({ saveAssessment: true });

                }, function () {
                    // Cancelled, deselect the option.
                    option.isSelected = false;
                    vm.onSelectedChanged({ code: null });
                });

            } else {

                // Unselect it...
                option.isSelected = false;
                vm.onSelectedChanged({ code: null });
            }

        }

        function setSelected(selected) {

            if (selected) {
                vm.onSelectedChanged({ code: selected.complianceMethodCode });
            }
            else {
                vm.onSelectedChanged({ code: null });
            }
        }

        vm.reorderComplianceOptions = function (item) {
            calculateOptionIndices();
        }

        // The hover visual cue should be enabled UNLESS an option is already selected
        // OR if the baseline is selected (note: Under some circumstances the baseline
        // can be 'selected' even if it is non-compliant).
        vm.isOnHoverEnabled = function (option) {

            var found = vm.assessment.allComplianceOptions.find(x => x.isSelected == true);
            return found == null || found == vm.assessment.allComplianceOptions[0];
        }

        // Fade the given option only if there IS a selected option and it's NOT this one.
        vm.shouldOptionFade = function (option) {

            var found = vm.assessment.allComplianceOptions.find(x => x.isSelected == true && x.isCompliant);

            if (found == null)
                return false; // Fade nothing
            else {
                if (found == vm.assessment.allComplianceOptions[0]) // Fade none if the baseline is selected.
                    return false;
                else
                    return found != option; // Don't fade the selected option, fade the rest.
            } 
        }

        /** Returns true if any condition is met which calls for input to be disabled.
          * At the moment this is basically relegated to whether ANY simulation is selected or not. */
        vm.shouldInputBeDisabled = function () {

            var found = vm.assessment.allComplianceOptions?.find(x => x.isSelected == true && x.isBaselineSimulation == false);
            return found; // If it's null, caller will interpret this as "false".
        }

        vm.shouldSelectBeDisabled = function(option) {

            // Alistair has asked for this to be disabled except when in the "Compliance Options Provided" state.
            const disabledDueToStatus = vm.assessment.statusCode !== 'ACompliance'; // I have a feeling this is not what Alistair ACTUALLY wants. 

            return disabledDueToStatus ||
                ((!vm.enableSelection || !option.isCompliant || vm.shouldOptionFade(option)) && option.isSelected !== true) ||
                option.tempNotSavedInDb === true;

        }

    }
})();