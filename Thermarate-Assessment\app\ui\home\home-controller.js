/*
* Dashboard Controller
* --------------------
*
* Main dashboard.
*
*/
// AKA home-controller
(function () {
    'use strict';
    var controllerId = 'DashboardCtrl';
    angular.module('app').controller(
        controllerId, ['common', '$rootScope', '$scope', '$state', '$window', 'jobservice', 'priorityservice', 'security', 'daterangehelper', '$timeout', 'standardmodelservice', '$http', dashboard]);
    function dashboard(common, $rootScope, $scope, $state, $window, jobservice, priorityservice, securityservice, daterangehelper, $timeout, standardmodelservice, $http) {

        var vm = this;
        vm.jobsList = [];
        vm.filteredJobs = [];

        //mirror of jobs array for angulars smart table
        vm.safeSourceJobs = [];
        vm.priorityList = [];
        vm.spinnerBusy = false;

        // Pagination variables
        vm.resultsPageSize = 100;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.totalFilteredJobs = 0;

        // Performance optimization flags
        vm.filterOptionsLoaded = false;
        vm.lastFilterHash = null;
        vm.showMoreLoading = false;

        // ---------------- //
        // - Permissions - //
        // ---------------- //

        vm.permission_canAccessSelfAssigned  = securityservice.immediateCheckRoles(['home_page_/_job_page__assignedself__access']);
        vm.permission_canAccessOtherAssigned = securityservice.immediateCheckRoles(['home_page_/_job_page__assignedother__access']);

        // ------------- //
        // - Filtering - //
        // ------------- //

        vm.filters = [
            { section: 1, name: 'client',                   field: 'clientName' },
            { section: 1, name: 'status',                   field: 'jobStatusDescription' },
            { section: 1, name: 'createdOn',                field: 'jobCreatedOn',            isDate: true },
            { section: 1, name: 'certifiedOn',              field: 'assessmentCerficateDate', isDate: true },
            { section: 1, name: 'updatedOn',                field: 'jobModifiedOn',           isDate: true },
            { section: 1, name: '[moreLessButton]',         field: '' },
            { section: 2, name: securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__creator__view")          ? 'creator'  : null, field: 'creatorFullName' },
            { section: 2, name: securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__assignee__view")         ? 'assignee' : null, field: 'clientAssigneeFullName' },
            { section: 2, name: securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__assignedassessor__view") ? 'assessor' : null, field: 'assessorFullName' },
            { section: 2, name: 'version',                  field: 'assessmentVersion', isDecimal: true },
            { section: 2, name: 'priority',                 field: 'assessmentPriorityDescription' },
            { section: 2, name: 'suburb',                   field: 'assessmentProjectDetailSuburb' },
            { section: 2, name: 'localGovernmentAuthority', field: 'assessmentProjectDetailLGA' },
            { section: 2, name: securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__creator__view")          ? null : '[blank]', field: 'blank1'},
            { section: 2, name: securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__assignee__view")         ? null : '[blank]', field: 'blank2'},
            { section: 2, name: securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__assignedassessor__view") ? null : '[blank]', field: 'blank3'},
            { section: 2, name: 'buildingDescription',      field: 'projectDescriptionDescription' },
            { section: 2, name: 'houseType',                field: 'assessmentDesign', optional: true },
            { section: 2, name: 'nccClimateZone',           field: 'assessmentNCCClimateZone' },
            { section: 2, name: 'natHERSClimateZone',       field: 'assessmentNatHERSClimateZone' },
            { section: 2, name: 'North Offset (\u00B0)',    field: 'northOffset',
                                                            groups: [
                                                                { value: "optionGroupNorth",     name: "> 337.5 to 22.5 (North)",       startRange: 337.5,  endRange: 22.5  },
                                                                { value: "optionGroupNorthEast", name: "> 22.5 to 67.5 (North-East)",   startRange: 22.5,   endRange: 67.5  },
                                                                { value: "optionGroupEast",      name: "> 67.5 to 112.5 (East)",        startRange: 67.5,   endRange: 112.5 },
                                                                { value: "optionGroupSouthEast", name: "> 112.5 to 157.5 (South-East)", startRange: 112.5,  endRange: 157.5 },
                                                                { value: "optionGroupSouth",     name: "> 157.5 to 202.5 (South)",      startRange: 157.5,  endRange: 202.5 },
                                                                { value: "optionGroupSouthWest", name: "> 202.5 to 247.5 (South-West)", startRange: 202.5,  endRange: 247.5 },
                                                                { value: "optionGroupWest",      name: "> 247.5 to 292.5 (West)",       startRange: 247.5,  endRange: 292.5 },
                                                                { value: "optionGroupNorthWest", name: "> 292.5 to 337.5 (North-West)", startRange: 292.5,  endRange: 337.5 }
                                                            ]},
            { section: 2, name: 'garage',                   field: 'garageLocation' },
            { section: 2, name: 'coveredOutdoorLiving',     field: 'outdoorLivingLocation' },
            { section: 2, name: 'lotDescription',           field: 'assessmentProjectDetailLotDescription' },
            { section: 2, name: 'Lot Width (m)',            field: 'assessmentProjectDetailLotWidth',    isDecimal: true,
                                                            groups: [
                                                                { value: "optionGroup6",                  name: "<= 6 m",           startRange: -0.01, endRange: 6      },
                                                                { value: "optionGroup6To7.5",             name: "> 6 to 7.5 m",     startRange: 6,     endRange: 7.5    },
                                                                { value: "optionGroupEast7.5To8.5",       name: "> 7.5 to 8.5 m",   startRange: 7.5,   endRange: 8.5    },
                                                                { value: "optionGroupSouthEast8.5To10.5", name: "> 8.5 to 10.5 m",  startRange: 8.5,   endRange: 10.5   },
                                                                { value: "optionGroupSouth10.5To12.5",    name: "> 10.5 to 12.5 m", startRange: 10.5,  endRange: 12.5   },
                                                                { value: "optionGroupSouthWest12.5To14",  name: "> 12.5 to 14 m",   startRange: 12.5,  endRange: 14     },
                                                                { value: "optionGroupWest14To15",         name: "> 14 to 15 m",     startRange: 14,    endRange: 15     },
                                                                { value: "optionGroupNorthWest15To16",    name: "> 15 to 16 m",     startRange: 15,    endRange: 16     },
                                                                { value: "optionGroupNorthWest16To18",    name: "> 16 to 18 m",     startRange: 16,    endRange: 18     },
                                                                { value: "optionGroupNorthWest18",        name: ">= 18 m",          startRange: 18,    endRange: 999999 }
                                                            ]},
            { section: 2, name: 'Lot Length (m)',           field: 'assessmentProjectDetailLotLength',   isDecimal: true,
                                                            groups: [
                                                                { value: "optionGroup16",     name: "<= 16 m",      startRange: -0.01, endRange: 16     },
                                                                { value: "optionGroup16To20", name: "> 16 to 20 m", startRange: 16,    endRange: 20     },
                                                                { value: "optionGroup20To21", name: "> 20 to 21 m", startRange: 20,    endRange: 21     },
                                                                { value: "optionGroup21To23", name: "> 21 to 23 m", startRange: 21,    endRange: 23     },
                                                                { value: "optionGroup23To25", name: "> 23 to 25 m", startRange: 23,    endRange: 25     },
                                                                { value: "optionGroup25To28", name: "> 25 to 28 m", startRange: 25,    endRange: 28     },
                                                                { value: "optionGroup28To30", name: "> 28 to 30 m", startRange: 28,    endRange: 30     },
                                                                { value: "optionGroup30To32", name: "> 30 to 32 m", startRange: 30,    endRange: 32     },
                                                                { value: "optionGroup32To35", name: "> 32 to 35 m", startRange: 32,    endRange: 35     },
                                                                { value: "optionGroup35",     name: ">= 35 m",      startRange: 35,    endRange: 999999 }
                                                            ]},
            { section: 2, name: 'Parcel Area (m\u00B2)',    field: 'assessmentProjectDetailParcelArea',  isDecimal: true,
                                                            groups: [
                                                                { value: "optionGroup150",       name: "<= 150 m2",        startRange: -0.01, endRange: 150    },
                                                                { value: "optionGroup150To200",  name: "> 150 to 200 m2",  startRange: 150,   endRange: 200    },
                                                                { value: "optionGroup200To250",  name: "> 200 to 250 m2",  startRange: 200,   endRange: 250    },
                                                                { value: "optionGroup250To300",  name: "> 250 to 300 m2",  startRange: 250,   endRange: 300    },
                                                                { value: "optionGroup300To350",  name: "> 300 to 350 m2",  startRange: 300,   endRange: 350    },
                                                                { value: "optionGroup350To400",  name: "> 350 to 400 m2",  startRange: 350,   endRange: 400    },
                                                                { value: "optionGroup400To450",  name: "> 400 to 450 m2",  startRange: 400,   endRange: 450    },
                                                                { value: "optionGroup450To500",  name: "> 450 to 500 m2",  startRange: 450,   endRange: 500    },
                                                                { value: "optionGroup500To550",  name: "> 500 to 550 m2",  startRange: 500,   endRange: 550    },
                                                                { value: "optionGroup550To600",  name: "> 550 to 600 m2",  startRange: 550,   endRange: 600    },
                                                                { value: "optionGroup600To650",  name: "> 600 to 650 m2",  startRange: 600,   endRange: 650    },
                                                                { value: "optionGroup650To700",  name: "> 650 to 700 m2",  startRange: 650,   endRange: 700    },
                                                                { value: "optionGroup700To750",  name: "> 700 to 750 m2",  startRange: 700,   endRange: 750    },
                                                                { value: "optionGroup750To800",  name: "> 750 to 800 m2",  startRange: 750,   endRange: 800    },
                                                                { value: "optionGroup800To850",  name: "> 800 to 850 m2",  startRange: 800,   endRange: 850    },
                                                                { value: "optionGroup850To900",  name: "> 850 to 900 m2",  startRange: 850,   endRange: 900    },
                                                                { value: "optionGroup900To950",  name: "> 900 to 950 m2",  startRange: 900,   endRange: 950    },
                                                                { value: "optionGroup950To1000", name: "> 950 to 1000 m2", startRange: 950,   endRange: 1000   },
                                                                { value: "optionGroup1000",      name: ">= 1000 m2",       startRange: 1000,  endRange: 999999 }
                                                            ]},
            { section: 2, name: 'cornerLot',                field: 'assessmentProjectDetailCornerBlock' },
            { section: 2, name: 'rearLoaded',               field: 'assessmentProjectDetailRearLaneway', isBool: true },
            { section: 2, name: 'ruralLot',                 field: 'assessmentProjectDetailRuralLot',    isBool: true },
        ];
        vm.filteredFilters = vm.filters.filter(f => f.name != null && f.name != "[blank]" && f.name != "[moreLessButton]");
        vm.filtersExpanded = false;
        vm.isTogglingFilters = false; // Flag to prevent API calls during filter expansion
        vm.filterOptions = {};
        vm.searchFields = [
            "jobReference",
            "clientName",
            "statusCode",
            "jobStatusDescription",
            "assessorFullName",
            "assessmentPriorityDescription",
            "assessmentStatusCode",
            "clientJobNumber",
            "projectOwner",
            "address",
            "creatorFullName",
            "clientAssigneeFullName",
            "projectDescriptionDescription",
            "complianceMethodDescription",
        ];
        vm.appliedFilters = {};
        vm.appliedFiltersOld = {};
        // Get all filters with group headings removed
        vm.filteredAppliedFilters = function () {
            let newFilters = angular.copy(vm.appliedFilters);
            Object.keys(newFilters).forEach(fieldName => {
                if (vm.filters.find(f => f.field == fieldName)?.groups != null) {
                    newFilters[fieldName] = newFilters[fieldName].filter(o => !o.startsWith("optionGroup"));
                }
            });
            return newFilters;
        }
        vm.filterCountData = {};
        vm.cachedFilterCounts = {}; // Cache for filter counts
        vm.cachedGroupCounts = {}; // Cache for group counts
        vm.cachedGroupOptions = {}; // Cache for group options
        vm.filterCountLookup = null; // Lookup map for faster filter count access
        vm.keyToName = standardmodelservice.keyToName;
        vm.getFilterSelectedText = common.getMultiFilterSelectedText;
        vm.optionName = common.getOptionName;
        vm.anyOptionsSelectedOnField = common.anyOptionsSelectedOnField;
        vm.anyFiltersApplied = common.anyFiltersApplied;
        // Further optimized function to get group options - uses caching
        vm.getGroupOptions = function (filter, group) {
            let fieldName = filter.field;
            let groupKey = `${fieldName}_${group.value}`;

            // Return cached result if available - this is the fast path
            if (vm.cachedGroupOptions[groupKey]) {
                return vm.cachedGroupOptions[groupKey];
            }

            // Return empty array if we're just toggling filters to avoid API calls
            if (vm.isTogglingFilters) {
                vm.cachedGroupOptions[groupKey] = [];
                return [];
            }

            // Create lookup map if not already created
            if (!vm.filterCountLookup) {
                vm.updateFilterCountLookup();
            }

            // First filter by range
            let filteredFilterOptions = vm.filterOptions[fieldName]?.filter(f => {
                // Convert to number once
                let fValue = +f.value;

                // Check if value is in range
                return group.startRange > group.endRange
                    ? fValue > group.startRange || fValue <= group.endRange
                    : fValue > group.startRange && fValue <= group.endRange;
            });

            // Then filter by count > 0 (only if we have options)
            if (filteredFilterOptions && filteredFilterOptions.length > 0) {
                // Pre-calculate all counts in one pass to avoid multiple calls
                let optionCounts = {};
                filteredFilterOptions.forEach(o => {
                    let count = vm.getFilterCountItem(filter, o);
                    optionCounts[o.value] = count;
                });

                // Now filter using the pre-calculated counts
                filteredFilterOptions = filteredFilterOptions.filter(o => optionCounts[o.value] > 0);
            }

            // Cache the result
            vm.cachedGroupOptions[groupKey] = filteredFilterOptions || [];
            return filteredFilterOptions || [];
        }
        // Further optimized function to get filter count - uses caching and lookup map
        vm.getFilterCountItem = function (filter, item) {
            if (!item) return null;

            let fieldName = filter.field;
            let itemValue = item.value?.toString().toLowerCase();

            if (!itemValue) return null;

            // Use cache if available - this is the fast path
            let cacheKey = `${fieldName}_${itemValue}`;
            if (vm.cachedFilterCounts[cacheKey] !== undefined) {
                return vm.cachedFilterCounts[cacheKey];
            }

            // Return 0 if we're just toggling filters to avoid API calls
            if (vm.isTogglingFilters) {
                vm.cachedFilterCounts[cacheKey] = 0;
                return 0;
            }

            // Create lookup map if not already created
            if (!vm.filterCountLookup) {
                vm.updateFilterCountLookup();
            }

            // Use lookup map for faster access
            if (vm.filterCountLookup && vm.filterCountLookup[fieldName]) {
                const count = vm.filterCountLookup[fieldName][itemValue];
                vm.cachedFilterCounts[cacheKey] = count || 0;
                return count || 0;
            }

            // Fallback to original method if lookup not available
            if (!vm.filterCountData[fieldName]) {
                vm.cachedFilterCounts[cacheKey] = 0;
                return 0;
            }

            let valueKey = Object.keys(vm.filterCountData[fieldName]).find(option =>
                option.toLowerCase() === itemValue);

            const count = valueKey ? vm.filterCountData[fieldName][valueKey] : 0;
            vm.cachedFilterCounts[cacheKey] = count;
            return count;
        }

        // Further optimized function to get group count - uses caching
        vm.getGroupCount = function (filter, group) {
            let cacheKey = `${filter.field}_${group.value}`;

            // Return cached result if available - this is the fast path
            if (vm.cachedGroupCounts[cacheKey] !== undefined) {
                return vm.cachedGroupCounts[cacheKey];
            }

            // Return 0 if we're just toggling filters to avoid API calls
            if (vm.isTogglingFilters) {
                vm.cachedGroupCounts[cacheKey] = 0;
                return 0;
            }

            // Create lookup map if not already created
            if (!vm.filterCountLookup) {
                vm.updateFilterCountLookup();
            }

            let optionsForThisGroup = vm.getGroupOptions(filter, group);
            if (!optionsForThisGroup || optionsForThisGroup.length === 0) {
                vm.cachedGroupCounts[cacheKey] = 0;
                return 0;
            }

            // Calculate the total once and cache it
            let thisGroupTotal = 0;
            for (let i = 0; i < optionsForThisGroup.length; i++) {
                const count = vm.getFilterCountItem(filter, optionsForThisGroup[i]);
                thisGroupTotal += count || 0;
            }

            vm.cachedGroupCounts[cacheKey] = thisGroupTotal;
            return thisGroupTotal;
        }
        // Clear caches when filter data changes
        vm.clearFilterCaches = function() {
            vm.cachedFilterCounts = {};
            vm.cachedGroupCounts = {};
            vm.cachedGroupOptions = {};
            vm.filterCountLookup = null;
        }

        // Optimized lookup map creation for faster filter count access
        vm.updateFilterCountLookup = function() {
            if (!vm.filterCountData) return;

            // Create a new lookup map
            const newLookup = {};

            // Process all fields in one pass
            const fieldNames = Object.keys(vm.filterCountData);
            for (let i = 0; i < fieldNames.length; i++) {
                const fieldName = fieldNames[i];
                newLookup[fieldName] = {};

                // Process all keys for this field
                const keys = Object.keys(vm.filterCountData[fieldName]);
                for (let j = 0; j < keys.length; j++) {
                    const key = keys[j];
                    const lowerKey = key.toLowerCase();
                    newLookup[fieldName][lowerKey] = vm.filterCountData[fieldName][key];
                }
            }

            // Assign the new lookup map
            vm.filterCountLookup = newLookup;
        }

        vm.selectOptionForGroup = function (filter, selected, group) {
            let fieldName = filter.field;
            let allOptionsForThisGroup = [...vm.getGroupOptions(filter, group).map(o => o.value), group.value];
            // function selectAllLogic(allOptions, listBefore, listChildIdKey, selected, selectAllCode)
            let newSelectedForThisGroup = common.selectAllLogic(
                [...allOptionsForThisGroup, group.value],
                vm.appliedFilters[fieldName].filter(o => allOptionsForThisGroup.includes(o)), // Only looking at this group's selected options
                null,
                selected.value,
                group.value
            )
            // Now that we have new selected options for this group, add this group's selections to full list of selections by removing this group's options from full selected list then adding this group's new selected
            vm.appliedFilters[fieldName] = vm.appliedFilters[fieldName].filter(o => !allOptionsForThisGroup.includes(o)).concat(newSelectedForThisGroup);

            // Clear caches related to this filter
            if (filter && filter.field) {
                const fieldName = filter.field;

                // Clear filter count cache for this field
                Object.keys(vm.cachedFilterCounts).forEach(key => {
                    if (key.startsWith(`${fieldName}_`)) {
                        delete vm.cachedFilterCounts[key];
                    }
                });

                // Clear group options cache for this field
                Object.keys(vm.cachedGroupOptions).forEach(key => {
                    if (key.startsWith(`${fieldName}_`)) {
                        delete vm.cachedGroupOptions[key];
                    }
                });

                // Clear group count cache for this field
                Object.keys(vm.cachedGroupCounts).forEach(key => {
                    if (key.startsWith(`${fieldName}_`)) {
                        delete vm.cachedGroupCounts[key];
                    }
                });
            }
        }
        vm.clearFilter = function (filter) {
            vm.appliedFilters[filter.field] = filter.isDate ? daterangehelper.getDefaultRange('All Time') : [];
            vm.filtersApplied = common.anyFiltersApplied(vm.searchString, vm.filteredFilters, vm.appliedFilters);
            vm.refreshList();
        }
        vm.clearFilters = function () {
            vm.initialiseComplete = false;
            vm.appliedFilters = {};
            vm.appliedFiltersOld = {};
            vm.filterCountData = {};
            vm.searchString = null;
            vm.searchStringOld = null;
            vm.appliedFilters['jobCreatedOn'] = daterangehelper.getDefaultRange('All Time');
            vm.appliedFilters['assessmentCerficateDate'] = daterangehelper.getDefaultRange('All Time');
            vm.appliedFilters['jobModifiedOn'] = daterangehelper.getDefaultRange('All Time');
            vm.filtersApplied = false;
            vm.clearFilterCaches();
            vm.resultsPageSize = 100; // Reset pagination
            vm.initialRefreshList();
        }



        // --------- //
        // - Dates - //
        // --------- //

        vm.ranges = daterangehelper.getRanges('Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month',
                                                'This Quarter', 'Last Quarter', 'Current Year', 'Current Financial Year', 'Last Financial Year',
                                                'Last Year', '12 Months', 'All Time');

        vm.filterColumns = null;
        vm.filterLocalName = "dashboardCtrl-columnFilter";
        vm.columnOptions = {
            jobReference: true,
            client: true,
            contact: true,
            clientRef: true,
            owner: true,
            orderDate: false,
            jobModifiedOn: true,
            assessmentVersion: true,
            assessor: true,
            priority: true,
            status: true,
            projectAddress: true,
            projectDescription: true,
            assessmentDesign: false,
            columnCount: 16,
            columnList: [
                { sortOrder: 0,  reference: 'jobReference',            description: 'Ref',                  checked: true  },
                { sortOrder: 1,  reference: 'client',                  description: 'Client',               checked: true  },
                { sortOrder: 2,  reference: 'clientRef',               description: 'Client Ref',           checked: true  },
                { sortOrder: 3,  reference: 'projectAddress',          description: 'Project Address',      checked: true  },
                { sortOrder: 4,  reference: 'owner',                   description: 'Owner',                checked: true  },
                { sortOrder: 5,  reference: 'projectDescription',      description: 'Building Description', checked: true  },
                { sortOrder: 6,  reference: 'assessmentDesign',        description: 'House Type',           checked: false },
                { sortOrder: 8,  reference: 'assessmentVersion',       description: 'Version',              checked: true  },
                { sortOrder: 9,  reference: 'creator',                 description: 'Creator',              checked: true  },
                { sortOrder: 10, reference: 'assignee',                description: 'Assignee',             checked: true  },
                { sortOrder: 11, reference: 'assessor',                description: 'Assessor',             checked: true  },
                { sortOrder: 12, reference: 'priority',                description: 'Priority',             checked: true  },
                { sortOrder: 13, reference: 'status',                  description: 'Status',               checked: true  },
                { sortOrder: 14, reference: 'orderDate',               description: 'Created',              checked: false },
                { sortOrder: 15, reference: 'assessmentCerficateDate', description: 'Certified',            checked: false },
                { sortOrder: 16, reference: 'jobModifiedOn',           description: 'Updated',              checked: true  },
            ],
        };

        vm.resetColumnOptions = function () {
            vm.columnOptions.jobReference = false;
            vm.columnOptions.client = false;
            vm.columnOptions.creator = false;
            vm.columnOptions.clientRef = false;
            vm.columnOptions.owner = false;
            vm.columnOptions.orderDate = false;
            vm.columnOptions.jobModifiedOn = false;
            vm.columnOptions.assessmentVersion = false;
            vm.columnOptions.assignee = false;
            vm.columnOptions.assessor = false;
            vm.columnOptions.priority = false;
            vm.columnOptions.status = false;
            vm.columnOptions.projectAddress = false;
            vm.columnOptions.projectDescription = false;
            vm.columnOptions.assessmentDesign = false;
        }

        vm.getDefaultColumnOptions = function () {
            var savedState = JSON.parse(localStorage.getItem(vm.filterLocalName));
            if (savedState) {
                vm.columnOptions = savedState;

                var filterColumns = [];
                if (vm.columnOptions.jobReference) { filterColumns.push('jobReference'); }
                if (vm.columnOptions.client) { filterColumns.push('client'); }
                if (vm.columnOptions.creator) { filterColumns.push('creator'); }
                if (vm.columnOptions.clientRef) { filterColumns.push('clientRef'); }
                if (vm.columnOptions.owner) { filterColumns.push('owner'); }
                if (vm.columnOptions.orderDate) { filterColumns.push('orderDate'); }
                if (vm.columnOptions.jobModifiedOn) { filterColumns.push('jobModifiedOn'); }
                if (vm.columnOptions.assessmentVersion) { filterColumns.push('assessmentVersion'); }
                if (vm.columnOptions.assignee) { filterColumns.push('assignee'); }
                if (vm.columnOptions.assessor) { filterColumns.push('assessor'); }
                if (vm.columnOptions.priority) { filterColumns.push('priority'); }
                if (vm.columnOptions.status) { filterColumns.push('status'); }
                if (vm.columnOptions.projectAddress) { filterColumns.push('projectAddress'); }
                if (vm.columnOptions.projectDescription) { filterColumns.push('projectDescription'); }
                if (vm.columnOptions.assessmentDesign) { filterColumns.push('assessmentDesign'); }
                vm.filterColumns = filterColumns;
            } else {
                vm.filterColumns = ['jobReference', 'client', 'creator', 'clientRef', 'owner', 'orderDate', 'jobModifiedOn', 'assessmentVersion', 'assessor', 'priority', 'status', 'projectAddress', 'projectDescription', 'assessmentDesign']
            }
        }

        vm.initialise = function () {
            activate();
            vm.clearFilters();
        }

        vm.initialRefreshList = function () {
            vm.initialiseComplete = false;

            // Use Promise.all to make concurrent API calls
            Promise.all([
                jobservice.getActiveJobs(vm.resultsPageSize, 1),
                jobservice.getMultiFilterOptions(true, vm.filteredFilters)
            ]).then(results => {
                // Process jobs data (first promise result)
                const jobsResult = results[0];
                if (jobsResult && jobsResult.data) {
                    // Set data and process each job
                    jobsResult.data.forEach((job, index) => {
                        // Can edit job
                        job.canEditJob = vm.determineCanEditJob(job);
                    });
                    vm.safeSourceJobs = jobsResult.data;
                    vm.jobsList = jobsResult.data;
                    vm.totalJobs = jobsResult.total;
                    vm.totalFilteredJobs = vm.totalJobs;
                    vm.showingFromCnt = vm.jobsList.length > 0 ? 1 : 0;
                    vm.showingToCnt = vm.jobsList.length;
                }

                // Process filter options (second promise result)
                vm.filterOptions = results[1];
                common.orderFilterOptions(vm.filterOptions);
                common.adjustFiltersForSelection(vm.filteredFilters, vm.appliedFilters, vm.appliedFiltersOld);
                vm.appliedFiltersOld = angular.copy(vm.appliedFilters);

                // Create search filter
                let searchFilter = [];
                for (let i = 0; i < vm.searchFields.length; i++) {
                    if (i == vm.searchFields.length-1) {
                        searchFilter.push({ field:vm.searchFields[i], operator: "contains", value: vm.searchString })
                    } else {
                        searchFilter.push({ field:vm.searchFields[i], operator: "contains", value: vm.searchString, logic: "or" })
                    }
                }

                // Get filter count data
                jobservice.getActiveJobsFilterCountData(vm.filteredFilters, vm.filterOptions, vm.filteredAppliedFilters(), searchFilter).then(data => {
                    vm.filterCountData = data;
                    vm.clearFilterCaches();
                    vm.updateFilterCountLookup();

                    // Pre-calculate filter counts for ALL filters to improve performance
                    $timeout(function() {
                        // Set a flag to prevent HTTP requests during pre-calculation
                        var originalHttpConfig = $http.defaults.headers.common;
                        $http.defaults.headers.common = angular.extend({}, originalHttpConfig, {
                            'X-No-Loading-Bar': 'true'
                        });

                        try {
                            // Pre-calculate all filter counts for both section 1 and section 2 filters
                            vm.filters.filter(f => f.name != null && f.name != "[blank]" && f.name != "[moreLessButton]").forEach(filter => {
                                if (filter.groups) {
                                    // Pre-calculate group counts
                                    filter.groups.forEach(group => {
                                        // Cache the group count
                                        vm.getGroupCount(filter, group);

                                        // Pre-calculate options for this group
                                        let options = vm.getGroupOptions(filter, group);
                                        if (options) {
                                            options.forEach(option => {
                                                // Cache the filter count for this option
                                                vm.getFilterCountItem(filter, option);
                                            });
                                        }
                                    });
                                } else if (vm.filterOptions[filter.field]) {
                                    // Pre-calculate counts for regular options
                                    vm.filterOptions[filter.field].forEach(option => {
                                        vm.getFilterCountItem(filter, option);
                                    });
                                }
                            });
                        } finally {
                            // Restore original HTTP config
                            $http.defaults.headers.common = originalHttpConfig;

                            vm.spinnerBusy = false;
                            vm.initialiseComplete = true;
                        }
                    }, 100);
                });
            });
        }
        vm.refreshList = function (skipFilterCountAndAnalytics = false) {
            common.adjustFiltersForSelection(vm.filteredFilters, vm.appliedFilters, vm.appliedFiltersOld);
            if ( vm.initialiseComplete && (!angular.equals(vm.appliedFilters, vm.appliedFiltersOld) || !angular.equals(vm.sortBy, vm.sortByOld)) || vm.searchString != vm.searchStringOld ) {
                vm.appliedFiltersOld = angular.copy(vm.appliedFilters);
                vm.sortByOld = angular.copy(vm.sortBy);
                vm.searchStringOld = vm.searchString;
                vm.spinnerBusy = true;

                // Cancel any pending requests before making new ones
                jobservice.getActiveJobsCancel();
                jobservice.getFilterCountCancel();

                // Create search filter
                let searchFilter = [];
                for (let i = 0; i < vm.searchFields.length; i++) {
                    if (i == vm.searchFields.length-1) {
                        searchFilter.push({ field:vm.searchFields[i], operator: "contains", value: vm.searchString })
                    } else {
                        searchFilter.push({ field:vm.searchFields[i], operator: "contains", value: vm.searchString, logic: "or" })
                    }
                }

                // Get filtered jobs
                jobservice.getActiveJobsMultiFiltered(vm.filteredFilters, vm.filterOptions, vm.filteredAppliedFilters(), searchFilter, vm.sortBy, vm.resultsPageSize, 1).then(function (result) {
                    if (result && result.data) {
                        // Set data and process each job
                        result.data.forEach((job, index) => {
                            // Can edit job
                            job.canEditJob = vm.determineCanEditJob(job);
                        });
                        vm.safeSourceJobs = result.data;
                        vm.jobsList = result.data;
                        vm.totalFilteredJobs = result.total;
                        vm.showingFromCnt = vm.jobsList.length > 0 ? 1 : 0;
                        vm.showingToCnt = vm.jobsList.length;
                    }
                    vm.filtersApplied = common.anyFiltersApplied(vm.searchString, vm.filteredFilters, vm.appliedFilters);

                    // Get filter count data and update caches
                    jobservice.getActiveJobsFilterCountData(vm.filteredFilters, vm.filterOptions, vm.filteredAppliedFilters(), searchFilter).then(data => {
                        vm.filterCountData = data;
                        vm.clearFilterCaches();
                        vm.updateFilterCountLookup();

                        // Pre-calculate filter counts for ALL filters to improve performance
                        $timeout(function() {
                            // Set a flag to prevent HTTP requests during pre-calculation
                            var originalHttpConfig = $http.defaults.headers.common;
                            $http.defaults.headers.common = angular.extend({}, originalHttpConfig, {
                                'X-No-Loading-Bar': 'true'
                            });

                            try {
                                // Pre-calculate all filter counts for both section 1 and section 2 filters
                                vm.filters.filter(f => f.name != null && f.name != "[blank]" && f.name != "[moreLessButton]").forEach(filter => {
                                    if (filter.groups) {
                                        // Pre-calculate group counts
                                        filter.groups.forEach(group => {
                                            // Cache the group count
                                            vm.getGroupCount(filter, group);

                                            // Pre-calculate options for this group
                                            let options = vm.getGroupOptions(filter, group);
                                            if (options) {
                                                options.forEach(option => {
                                                    // Cache the filter count for this option
                                                    vm.getFilterCountItem(filter, option);
                                                });
                                            }
                                        });
                                    } else if (vm.filterOptions[filter.field]) {
                                        // Pre-calculate counts for all options
                                        vm.filterOptions[filter.field].forEach(option => {
                                            vm.getFilterCountItem(filter, option);
                                        });
                                    }
                                });
                            } finally {
                                // Restore original HTTP config
                                $http.defaults.headers.common = originalHttpConfig;
                            }
                        }, 0);
                    });

                    vm.spinnerBusy = false;
                });
            }
        }

        vm.getDefaultColumnOptions();

        // Watch for column options changes
        $scope.$watch('vm.columnOptions', function (newValue, oldValue) {
            if (newValue != undefined && newValue != null && newValue != oldValue) {
                localStorage.setItem(vm.filterLocalName, JSON.stringify(newValue));
            }
        }, true);

        // Watch for filter expansion to prevent API calls
        $scope.$watch('vm.filtersExpanded', function (newValue, oldValue) {
            if (newValue !== oldValue) {
                // Set flags to prevent HTTP requests during filter expansion
                vm.isTogglingFilters = true;
                var originalHttpConfig = $http.defaults.headers.common;
                $http.defaults.headers.common = angular.extend({}, originalHttpConfig, {
                    'X-No-Loading-Bar': 'true'
                });

                // Restore original HTTP config and clear flag after a short delay
                $timeout(function() {
                    vm.isTogglingFilters = false;
                    $http.defaults.headers.common = originalHttpConfig;
                }, 300);
            }
        });

        vm.default_priority_theme = "default";
        vm.red_priority_theme = "altRed";
        vm.jobTableTitle = "";
        vm.searchTerm;
        vm.initialise();

        vm.filterOption = function (statusCode) {
            vm.filterByStatusCode = statusCode;
        }

        vm.sortBy = null;
        vm.orderBy = function (tableState, e, filterOption) {
            console.log(tableState, e, filterOption);
            console.log("Sort is: ", tableState.sort);

            vm.sortBy = {
                field: tableState.sort.predicate,
                dir: tableState.sort.reverse ? 'desc' : 'asc'
            };
            vm.refreshList();
        }

        vm.getMoreResults = function () {
            // Use incremental loading for better performance
            vm.getMoreResultsIncremental();
        }

        vm.getMoreResultsIncremental = function () {
            vm.showMoreLoading = true;
            vm.spinnerBusy = true;
            var currentCount = vm.jobsList.length;
            var pageSize = 100;

            // Determine which API to call based on whether filters are applied
            var apiCall;
            if (vm.filtersApplied) {
                var searchFilter = vm.searchString ? { field: 'jobReference', value: vm.searchString } : null;
                apiCall = jobservice.getActiveJobsMultiFilteredIncremental(
                    vm.filteredFilters,
                    vm.filterOptions,
                    vm.filteredAppliedFilters(),
                    searchFilter,
                    vm.sortBy,
                    currentCount,
                    pageSize
                );
            } else {
                apiCall = jobservice.getActiveJobsIncremental(currentCount, pageSize);
            }

            apiCall.then(function (result) {
                if (result && result.data && result.data.length > 0) {
                    // Process new jobs for permissions
                    result.data.forEach((job, index) => {
                        job.canEditJob = vm.determineCanEditJob(job);
                    });

                    // Append new results to existing list instead of replacing
                    vm.jobsList = vm.jobsList.concat(result.data);
                    vm.safeSourceJobs = vm.safeSourceJobs.concat(result.data);
                    vm.showingToCnt = vm.jobsList.length;
                }
                vm.showMoreLoading = false;
                vm.spinnerBusy = false;
            }, function (error) {
                vm.showMoreLoading = false;
                vm.spinnerBusy = false;
                console.error('Error loading more results:', error);
            });
        }

        // Helper function to generate a hash of current filter state
        vm.getFilterHash = function() {
            return JSON.stringify({
                appliedFilters: vm.appliedFilters,
                searchString: vm.searchString,
                sortBy: vm.sortBy
            });
        }

        // Check if filters have actually changed
        vm.filtersHaveChanged = function() {
            var currentHash = vm.getFilterHash();
            var changed = vm.lastFilterHash !== currentHash;
            vm.lastFilterHash = currentHash;
            return changed;
        }

        priorityservice.getList()
            .then(function (result) {
                if (result == undefined || result == null) {
                    // Its been cancelled so get out of here.
                    return;
                }
                vm.priorityList = result.data;
            },
            function (error) {

            });

        function activate() {
            vm.jobTableTitle = securityservice.immediateCheckRoles('assessment__assigned_view_other')
              ? "Active Jobs"
              : "My Active Jobs";
        }

        var newJobListener = $rootScope.$on('newJob', function () {
            activate();
        });

        $rootScope.$on('$destroy', function () {
            newJobListener();
        });

        vm.getPriorityColour = function (row, index) {
            if (row.assessmentStatusCode == 'AComplete' || row.assessmentStatusCode == 'AIssued') {
                return "";
            }

            var isEven = false;
            if (index % 2 == 0) {
                isEven = true;
            }

            var expiresOn = new Date(row.assessmentCreatedOn);
            var turnaroundHours = 0;
            for (var ii = 0, len = vm.priorityList.length; ii < len; ii++) {
                if (vm.priorityList[ii].priorityCode == row.assessmentPriorityCode) {
                    turnaroundHours = vm.priorityList[ii].turnaroundHours;
                    break;
                }
            }

            if (turnaroundHours == 0) {
                return "";
            }

            expiresOn.setHours(expiresOn.getHours() + turnaroundHours);

            if (Date.now() > expiresOn) {
                if (isEven == true) {
                    return "table-striped-red";
                } else {
                    return "table-striped-red-light";
                }
            }

            return "";
        }

        vm.determineCanEditJob = function (job) {
            let currentUserId = securityservice.currentUser.rssUserId;
            if (job.assessorUserId == currentUserId && vm.permission_canAccessSelfAssigned) {
                return true;
            }
            else if ((job.assessorUserId != currentUserId || job.assessorUserId == null) && vm.permission_canAccessOtherAssigned) {
                return true;
            }
            return false;
        }
        vm.goToJob = function (job) {
            if (vm.determineCanEditJob(job)) {
                $state.go("assessment-updateform", { assessmentId: job.currentAssessmentId });
            }
        }

        vm.jobRowClick = function ($event, row) {
            if ($event.button == 1) {
                var url = $state.href('assessment-updateform', { assessmentId: row.currentAssessmentId}, { absolute: true });
                $window.open(url,'_blank');
            }
        }

        // Permissions for column options
        if (!securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__creator__view")) {
            vm.columnOptions.columnList = vm.columnOptions.columnList.filter(i => i.reference != "creator");
        }
        if (!securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__assignee__view")) {
            vm.columnOptions.columnList = vm.columnOptions.columnList.filter(i => i.reference != "assignee");
        }
        if (!securityservice.immediateCheckRoles("assessment_page_(tabs/sub-tabs)__assignedassessor__view")) {
            vm.columnOptions.columnList = vm.columnOptions.columnList.filter(i => i.reference != "assessor");
        }

    }

})();