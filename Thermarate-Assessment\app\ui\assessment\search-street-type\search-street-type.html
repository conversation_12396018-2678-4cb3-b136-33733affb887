<md-autocomplete md-selected-item="vm.selectedSearchStreetType"
				 md-search-text="vm.searchStreetTypeText"
				 md-items="item in vm.searchStreetType(vm.searchStreetTypeText)"
				 md-selected-item-change="vm.searchStreetTypeItemChange(item)"
				 md-item-text="item"
				 md-input-maxlength="100"
				 md-select-on-match="true"
				 md-no-cache="true"
				 md-delay="300"
				 ng-required="vm.required"
                 md-require-match
				 ng-disabled="vm.isDisabled"
				 flex-gt-sm="100"
				 md-floating-label="Street Type"
				 md-input-name="searchStreetType"
				 placeholder="Street Type"
			     ng-class="{'vertically-condensed'  : vm.condensed }">
	<md-item-template>
		<span md-highlight-text="vm.name">{{item}}</span>
	</md-item-template>
	<md-not-found>
		No street type found.
	</md-not-found>
</md-autocomplete>