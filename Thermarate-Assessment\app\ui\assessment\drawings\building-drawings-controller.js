(function () {

    'use strict';

    // The name of the component in camelCase.
    // This is what you will use in the widget tree (but converted to <snake-case/>)
    const COMPONENT_NAME = "buildingDrawings";

    // The URL of the HTML template this controller will control.
    const HTML_TEMPLATE_URL = "app/ui/assessment/drawings/building-drawings.html";

    angular
        .module('app')
        .component(COMPONENT_NAME, {

            // PARAMETERS THAT CAN BE PASSED TO COMPONENT
            bindings: {
                assessment: '<',
                option: '<',
                disabled: '<',
                jobFiles: '<',
                clientOptions: '<',
            },

            templateUrl: HTML_TEMPLATE_URL,
            controller: Controller,

            controllerAs: 'vm'
        });

    // Inject all services required here (and make sure to add to the controller params too.)
    Controller.$inject = ['$scope', '$rootScope', '$mdDialog', 'common','uuid4', '$q', 'assessmentservice', 'Upload',
    'assessmentdrawingservice', 'fileservice'];

    function Controller($scope, $rootScope, $mdDialog, common, uuid4, $q, assessmentservice, Upload,
                        assessmentdrawingservice, fileservice) {

        const vm = this;

        vm.showArchiveTable = false;

        vm.pendingFileList = [];

        vm.pageSizeList = [
            { id: 0, description: 'A0' },
            { id: 1, description: 'A1' },
            { id: 2, description: 'A2' },
            { id: 3, description: 'A3' },
            { id: 4, description: 'A4' }
        ];

        vm.launchBulkEdit = async function (option) {
            let modalScope = $rootScope.$new();

            // Modal Inputs
            modalScope.showArchiveTable = vm.showArchiveTable;
            modalScope.pageSizeList = vm.pageSizeList;

            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/assessment/bulk-edit-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
            })
                .then(async function (response) {
                    if (response != undefined && response != null) {

                        vm.isBusy = true;

                        for (let ii = option.assessmentDrawings.length; ii > 0; ii--) {
                            if (option.assessmentDrawings[ii - 1].checkboxSelected === true && option.assessmentDrawings[ii - 1].archived === vm.showArchiveTable) {

                                // 0 is a valid option.
                                if (response.pageSize != null) {
                                    option.assessmentDrawings[ii - 1].pageSize = response.pageSize;
                                }

                                if (response.revision) {
                                    option.assessmentDrawings[ii - 1].revision = response.revision;
                                }

                                if (response.revisionDate) {
                                    option.assessmentDrawings[ii - 1].revisionDate = response.revisionDate;
                                }

                                if (response.isIncludedInReport != null) {
                                    option.assessmentDrawings[ii - 1].isIncludedInReport = response.isIncludedInReport;
                                }

                                if (response.isShownToClient != null) {
                                    option.assessmentDrawings[ii - 1].isShownToClient = response.isShownToClient;

                                    // This ones special. They all have to match in a set.
                                    const drawingsInSet = vm.option.assessmentDrawings
                                      .filter(x => x.documentId === option.assessmentDrawings[ii - 1].documentId);

                                    drawingsInSet.forEach(a => {
                                        a.isShownToClient = response.isShownToClient;
                                    });

                                }

                                if (response.stampAction != null) {
                                    switch (response.stampAction) {
                                        case "NO":
                                            option.assessmentDrawings[ii - 1].toStamp = false;
                                            break;
                                        case "YESDONTPLACESTAMP":
                                            option.assessmentDrawings[ii - 1].toStamp = true;
                                            break;
                                        case "YESPLACESTAMPDEFAULT":
                                            // Re-run auto-stamp functionality on drawings.
                                            let drawing = option.assessmentDrawings[ii - 1];
                                            drawing.toStamp = true;
                                            drawing.isStamped = true;
                                            drawing.useDefaultStampPosition = true;
                                            let url = "";
                                            if (drawing.archived) {
                                                url = drawing.attachment.originalFileUrl;
                                            } else {
                                                url = drawing.attachment.url;
                                            }
                                            let file = url.substr(0, url.lastIndexOf("."));
                                            url = file + ".png";
                                            // NOTE: We actually DO have to AWAIT here as we want the busy indicator
                                            // to continue while this process is in progress.
                                            // Due to the way the ORIGINAL auto-stamp process differs from 'getDefaultStampPosition'
                                            // function (i.e, the original works on the FULL PDF, this only works on individual pages)
                                            // we can't (yet???) set a "process in background" flag (Would require refactoring...)
                                            let result = await assessmentdrawingservice.getDefaultStampPosition(
                                                url,
                                                drawing.rotation != null ? drawing.rotation : 0,
                                                response.ignoreSheetSize
                                            );
                                            drawing.stampX = result.stampLeft.toString();
                                            drawing.stampY = result.stampTop.toString();
                                            drawing.stampWidth = result.width.toString();
                                            break;
                                    }
                                }

                                if (response.archive === true) {
                                    if(vm.showArchiveTable === false) {
                                        vm.archiveAssessmentDrawing(null, option.assessmentDrawings[ii - 1]);
                                    } else {
                                        vm.reinstateAssessmentDrawing(null, option.assessmentDrawings[ii - 1]);
                                    }
                                }

                                if (response.delete === true) {
                                    vm.removeAssessmentDrawing(null, option.assessmentDrawings[ii - 1], option);
                                }

                                // Uncheck after all processing.
                                option.assessmentDrawings[ii - 1].checkboxSelected = false;
                            }

                            vm.selectAllCheckboxState = false;
                            vm.bulkSelectCheckboxIsIndeterminate = false;

                        }

                        vm.isBusy = false;
                    }
                }, function () {
                    //cancelled
                });
        }

        vm.bulkSelectCheckboxIsIndeterminate = false;
        vm.updateBulkSelectStatus = function (option) {

            if (!vm.assessment || !vm.assessment.allComplianceOptions || !option.assessmentDrawings)
                return;

            const drawingsInTable = option.assessmentDrawings
                .filter(x => x.archived === vm.showArchiveTable && !x.isDeleted);

            const allChecked = drawingsInTable
                .every(x => x.checkboxSelected === true && x.archived === vm.showArchiveTable);

            const someChecked = drawingsInTable
                .some(x => x.checkboxSelected === true && x.archived === vm.showArchiveTable);

            if(allChecked) {
                vm.bulkSelectCheckboxIsIndeterminate = false;
                vm.selectAllCheckboxState = true;

                // Have to manually apply this class (Probably due to clashes with the indeterminate value)
                setTimeout(() => {
                    let currentClass = document.getElementById('allCheckbox').className;
                    if (currentClass.indexOf("ng-empty-add md-checked") === -1) {
                        document.getElementById('allCheckbox').className += " ng-empty-add md-checked";
                    }
                }, 25);

            } else if(someChecked) {
                vm.selectAllCheckboxState = false;
                vm.bulkSelectCheckboxIsIndeterminate = true;
            } else {
                vm.selectAllCheckboxState = false;
                vm.bulkSelectCheckboxIsIndeterminate = false;
            }

            safeApply();
        }

        function safeApply() {
            const phase = $rootScope.$$phase;
            if (!phase) {
                $rootScope.$apply();
            }
        }

        vm.switchArchiveState = function (state) {
            vm.showArchiveTable = state;
        }

        vm.selectAllCheckboxes = function (option, state) {

            if (!vm.assessment || !option.assessmentDrawings)
                return;

            setTimeout(() => {

                let wantedResult = false;
                if (vm.bulkSelectCheckboxIsIndeterminate === true)
                    wantedResult = true;
                else if (state !== true)
                    wantedResult = true;

                const drawingsInTable = option.assessmentDrawings
                    .filter(x => x.archived === vm.showArchiveTable && !x.isDeleted);
                drawingsInTable.forEach(x => x.checkboxSelected = wantedResult);

                vm.selectAllCheckboxState = wantedResult;
                vm.updateBulkSelectStatus(option);

            }, 25);
        }

        // Expose to UI
        vm.renumberWorkingDrawings = function () {
            common.setDrawingReferences(vm.option.assessmentDrawings);
        }

        vm.showStampDrawings = function (drawing, option) {

            if (vm.disableStampFunctionality())
                return;

            if(!drawing || drawing.processingProgress !== 100)
                return 0;

            let deferred = $q.defer();

            if (vm.assessment.attachmentStatusCode === 'ASuperseded') {
                deferred.resolve();
                return deferred.promise;
            }

            let modalScope = $rootScope.$new();
            modalScope.viewMode = "New";
            modalScope.assessmentId = vm.assessment.assessmentId;
            modalScope.expectedNumOfDrawings = option.assessmentDrawings.length;
            modalScope.assessmentDrawingId = drawing?.assessmentDrawingId;
            modalScope.assessmentDrawings = option.assessmentDrawings;
            modalScope.clientOptions = vm.clientOptions;
            let modalOptions = {
                templateUrl: 'app/ui/assessmentdrawing/assessmentdrawing-stamp.html',
                scope: modalScope,
                resolve: {
                    viewMode: function () {
                        return 'New';
                    }
                }
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions).then(function (updatedDrawings) {
                if (updatedDrawings != null) {
                    // Drawing stamp positions where modified and saved, so update list.
                    for (let i = 0, len = updatedDrawings.length; i < len; i++) {
                        let rec = _.findWhere(option.assessmentDrawings, { assessmentDrawingId: updatedDrawings[i].assessmentDrawingId });
                        if (rec != undefined) {
                            rec.stampX = updatedDrawings[i].stampX;
                            rec.stampY = updatedDrawings[i].stampY;
                            rec.toStamp = updatedDrawings[i].toStamp;
                            rec.isStamped = true;
                            rec.stampWidth = updatedDrawings[i].stampWidth;
                            rec.drawingDescription = updatedDrawings[i].drawingDescription;
                            rec.sheetNumber = updatedDrawings[i].sheetNumber;
                            rec.revision = updatedDrawings[i].revision;
                            rec.revisionDate = updatedDrawings[i].revisionDate;
                            rec.isIncludedInReport = updatedDrawings[i].isIncludedInReport;
                            rec.deleted = updatedDrawings[i].deleted;
                            rec.rotation = updatedDrawings[i].rotation;
                            rec.pageSize = updatedDrawings[i].pageSize;
                        }
                    }
                    //delete anything thatw as deleted in modal
                    if (vm.assessment && option.assessmentDrawings) {
                        for (let ii = option.assessmentDrawings.length - 1; ii >= 0; ii--) {
                            if (option.assessmentDrawings[ii].deleted) {
                                vm.removeAssessmentDrawing(null, option.assessmentDrawings[ii], option);
                            }
                        }
                    }
                }

                deferred.resolve();
            });

            return deferred.promise;
        }

        vm.stopClickThrough = function(event) {
            if(event && event.stopPropagation)
                event.stopPropagation();
        }

        vm.allUploadingFiles = [];
        vm.uploadFile = function ($files) {

            if ($files == null || $files.length == 0 || $files.every(f => f == null))
                return;

            if(vm.disabled)
                return;

            const option = vm.option;
            if (option.assessmentDrawings == null)
                option.assessmentDrawings = [];

            let url = "../api/Assessment/UploadFile?assessmentId=" + vm.assessment.assessmentId;
            url += "&jobId=" + vm.assessment.jobId;
            url += `&category=Assessment Drawing`
            url += `&classification=Generic`

            vm.allUploadingFiles = [];

            $files.forEach(($file, index) => {
                let promise = Upload.upload({
                    url: url, // webapi url
                    method: "POST",
                    file: $file
                });
                let newFileUpload = {
                    index: index,
                    progress: 0
                };
                vm.allUploadingFiles.push(newFileUpload);

                promise.progress(function (evt) {

                    newFileUpload.progress = 100 * (evt.loaded / evt.total);

                }).success(async function (file) {

                    newFileUpload.pageNumber = 1;

                    // Kickoff queue process. Poll process will pick up new drawings after split and through the rest of
                    // the process.
                    const stubs = await assessmentdrawingservice.startProcessingPdf(
                        vm.assessment.assessmentId,
                        file.url,
                        file.fileId,
                        file.fileName,
                        newFileUpload.documentId,
                        option.complianceOptionsId);

                    if(stubs && stubs.length) {
                        mergeDrawings(vm.option, stubs);
                        common.setDrawingReferences(vm.option.assessmentDrawings);
                    }

                    // Kickoff polling process if not already started.
                    vm.allUploadingFiles = vm.allUploadingFiles.filter(u => u.index != index);
                    getAssessmentOptionDrawingsStillProcessing();
                    console.log(vm.allUploadingFiles);

                }).error(function (data, status, headers, config) {
                    vm.allUploadingFiles = vm.allUploadingFiles.filter(u => u.index != index);
                });
            });
        }

        vm.drawingsUploadProgress = function() {
            if (vm.allUploadingFiles.length == 0) {
                return 0;
            } else {
                return vm.allUploadingFiles.map(u => u.progress).reduce((a,b) => a + b, 0) / vm.allUploadingFiles.length;
            }
        }

        // When attachemnt is changed from drop down, convert it to png if needed
        vm.attachmentChanged = function (item, option) {
            if (item.attachment != undefined && item.attachment != null) {
                assessmentdrawingservice.convert(vm.assessment.assessmentId, item.attachment.url, item.attachment.fileId, item.attachment.fileName, null, option.complianceOptionsId, true);
            }
        }

        //gets a list of the job files, filtered by the given type. e.ge pdf
        vm.getFilteredJobFiles = function (fileType) {
            let filteredFiles = [];
            fileType = fileType.toLowerCase();
            if (vm.jobFiles == undefined || vm.jobFiles == null) { return filteredFiles; }
            for (let ii = 0; ii < vm.jobFiles.length; ii++) {
                let split = vm.jobFiles[ii].fileName.split(".");
                if (split != null && split[split.length - 1].toLowerCase() === fileType) {
                    filteredFiles.push(vm.jobFiles[ii]);
                }
            }
            return filteredFiles;
        }

        vm.downloadFile = function (fileDto) {

            if(fileDto == null)
                return;

            let a = document.createElement('a');
            a.href = fileDto.url;
            //a.setAttribute('download', fileDto.fileName);
            a.target = '_blank';
            a.click();
        }

        //sets a variable to null, given context and variable name
        vm.setToNull = function (event, context, field) {
            vm.stopClickThrough(event);

            if (context != undefined && context != null && context[field] != undefined) {
                context[field] = null;
            }
        }

        // Called when some fields on working drawing are updated, triggers a 
        // modal to update all drawings from the same document(i.e all pages 
        // from the same multi-page pdf)
        vm.drawingUpdated = function (item, field, option) {
            let matchingDrawings = [];
            for (let ii = 0; ii < option.assessmentDrawings.length; ii++) {
                option.assessmentDrawings[ii].updateIndex = null;
                if (option.assessmentDrawings[ii].pageNumber !== item.pageNumber && option.assessmentDrawings[ii].documentId === item.documentId) {
                    //something to id the row to update
                    option.assessmentDrawings[ii].updateIndex = ii;
                    matchingDrawings.push(option.assessmentDrawings[ii]);
                }
            }
            if (matchingDrawings.length > 0) {
                let modalScope = $rootScope.$new();
                modalScope.matchingDrawings = matchingDrawings;
                $mdDialog.show({
                    scope: modalScope,
                    templateUrl: 'app/ui/assessment/group-drawing-update.html',
                    parent: angular.element(document.body),
                    clickOutsideToClose: true,
                })
                    .then(function (response) {
                        if (response && response.length) {
                            for (let ii = 0; ii < response.length; ii++) {
                                for (let jj = 0; jj < option.assessmentDrawings.length; jj++) {
                                    if (response[ii] === option.assessmentDrawings[jj].updateIndex) {
                                        option.assessmentDrawings[jj][field] = item[field];
                                    }
                                }
                            }
                        }
                    }, function () {
                        //cancelled
                    });
            }
        }

        vm.toggleIncludeInReport = function($event, item) {
            if (!(vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired)) && vm.clientOptions.includeDrawingsInReport) {
                if ($event)
                    vm.stopClickThrough($event);

                item.isIncludedInReport = !item.isIncludedInReport;
                if (!item.isIncludedInReport) {
                    item.toStamp = false;
                }
            }
        }

        vm.toggleShowToClient = function($event, item) {
            if (!(vm.disabled || (!vm.option.isBaselineSimulation && !vm.option.updatedDrawingsRequired))) {
                if ($event)
                    vm.stopClickThrough($event);

                const drawingsInSet = vm.option.assessmentDrawings.filter(x => x.documentId === item.documentId);

                item.isShownToClient = !item.isShownToClient;
                drawingsInSet.forEach(a => a.isShownToClient = item.isShownToClient);
            }
        }

        vm.archiveFilter = function (item) {
            return item.archived === vm.showArchiveTable;
        }

        vm.archiveAssessmentDrawing = function ($event, assessmentDrawing) {
            if ($event)
                vm.stopClickThrough($event);

            let archivePromise = assessmentservice.archiveAssessmentDrawing(assessmentDrawing.assessmentId, assessmentDrawing.assessmentDrawingId);

            archivePromise.then(function (result) {
                if (result.data != undefined && result.data != null && result.data != "") {
                    assessmentDrawing.archived = true;
                    assessmentDrawing.isIncludedInReport = false;
                    assessmentDrawing.attachment.url = result.data;
                }

                common.setDrawingReferences(vm.option.assessmentDrawings);
            });
        }

        vm.reinstateAssessmentDrawing = function ($event, assessmentDrawing) {
            if ($event)
                vm.stopClickThrough($event);

            let reinstatePromise = assessmentservice.reinstateAssessmentDrawing(assessmentDrawing.assessmentDrawingId);

            reinstatePromise.then(function (result) {
                if (result.data != undefined && result.data != null && result.data != "") {
                    assessmentDrawing.archived = false;
                    assessmentDrawing.attachment.url = result.data;
                }

                common.setDrawingReferences(vm.option.assessmentDrawings);
            });
        }

        /**
         * Marks the given drawing as deleted. We use a filter on the UI to hide - don't actually remove from list.
         * This way when we do a save, entity framework will save anything we have set her as deleted, as deleted.
         * @param {any} assessmentDrawing The drawing we wish to mark as deleted
         * @param {any} option The ComplianceOption itself.
         */
        vm.removeAssessmentDrawing = function ($event, assessmentDrawing, option) {
            if ($event)
                vm.stopClickThrough($event);

            assessmentDrawing.deleted = true;
            common.setDrawingReferences(option.assessmentDrawings);
        }

        vm.addAssessmentDrawing = function (option) {

            if (option.assessmentDrawings == undefined) {
                option.assessmentDrawings = [];
            }

            option.assessmentDrawings.push({ isIncludedInReport: true, archived: false, deleted: false, useDefaultStampPosition: false, stampX: null, stampY: null  });
            common.setDrawingReferences(option.assessmentDrawings);
        }

        vm.importDrawings = function (option) {
            let modalScope = $rootScope.$new();
            modalScope.jobId = vm.assessment.job.jobId;
            modalScope.clientId = vm.assessment.job.clientId;
            modalScope.clientName = vm.assessment.job.clientName;
            modalScope.assessmentId = vm.assessment.assessmentId;

            let existingFileNames = [];
            for (let ii = 0, len = option.assessmentDrawings.length; ii < len; ii++) {
                let wkDrawing = option.assessmentDrawings[ii];
                if (wkDrawing.attachment && wkDrawing.attachment.fileName) {
                    existingFileNames.push(wkDrawing.attachment.fileName);
                }
            }

            $mdDialog.show({
                scope: modalScope,
                templateUrl: 'app/ui/assessment/import-drawings-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
            })
            .then(async function (response) {

                if(response == null || response.length === 0)
                    return;

                let externalUrls = [];
                for (let ii = 0, len = response.length; ii < len; ii++) {
                    externalUrls.push(response[ii].attachment.url);
                }

                const data = await assessmentservice.uploadFilesFromList(
                    vm.assessment.assessmentId, 
                    vm.assessment.job.jobId, 
                    existingFileNames, 
                    externalUrls);

                const files = data.data;

                for(let i = 0; i < files.length; i++) {

                    const file = files[i];
                    const stubs = await assessmentdrawingservice.startProcessingPdf(
                        vm.assessment.assessmentId,
                        file.url,
                        file.fileId,
                        file.fileName,
                        file.fileId,
                        option.complianceOptionsId);

                    if(stubs && stubs.length) {
                        mergeDrawings(vm.option, stubs);
                        common.setDrawingReferences(vm.option.assessmentDrawings);
                    }
                }

                // Kickoff polling process if not already started.
                vm.allUploadingFiles = [];
                getAssessmentOptionDrawingsStillProcessing();

                waitingForCompletedDrawings = true;

            }, function () {
                //cancelled
            });
        }

        vm.stampDrawings = function (option) {
            let deferred = $q.defer();

            let numOfExpectedDrawings = 0;

            for (let i = 0; i < option.assessmentDrawings.length; i++) {
                if (option.assessmentDrawings[i].isIncludedInReport && option.assessmentDrawings[i].deleted == false) {
                    numOfExpectedDrawings++;
                }
            }

            vm.showStampDrawings(numOfExpectedDrawings, option.assessmentDrawings[0], option).then(function () {
                deferred.resolve();
            });

            return deferred.promise;
        }

        /** Opens all original documents in separate tabs */
        vm.openAllCurrentDrawings = function (option) {
            groupDrawingsViaOriginalDocument(option.assessmentDrawings)
                .filter(drawing => !drawing.archived && drawing.url)
                .forEach(drawing => window.open(drawing.url, '_blank'));
        }

        /**
         * Groups multiple single-page PDFS into an array of 'documents' based on
         * their original document ID.
         */
        function groupDrawingsViaOriginalDocument(drawings) {

            let ids = drawings?.map(x => x.documentId);
            let uniques = new Set(ids);

            let grouped = [];

            // This regex is used to strip the page number off the end of
            // the pdf title (We add it on the back end)
            const stripPattern = /_[Pp]g.*/;

            uniques.forEach(id => {

                let matching = drawings.filter(x => x.documentId === id && x.processingStatus !== "Failed");
                matching = matching.sort((a, b) => {
                    return a.drawingNumber - b.drawingNumber;
                });

                let title = matching[0].drawingDescription.replace(stripPattern, '');
                const overallProgress = matching
                        .map(x => x.processingProgress)
                        .reduce((a, b) => a + b, 0)
                    / matching.length;

                const completeDrawing = matching.find(x => x.processingStatus === 'COMPLETE' && x.attachment !== null);
                const url = completeDrawing?.attachment.originalFileUrl ?? completeDrawing?.attachment.url;

                grouped.push({
                    title: title,
                    drawings: matching,
                    // Single page PDF's never get assigned an 'originalFileUrl'
                    url: url,
                    sizeInBytes: completeDrawing?.attachment.sizeInBytes / 1024,
                    createdOn: matching[0].createdOn,
                    createdBy: matching[0].createdByName,
                    progress: overallProgress,
                    documentId: completeDrawing.documentId,
                });
            });

            return grouped;
        }

        /** Downloads all original documents in a single zip archive */
        vm.downloadAllCurrentDrawingsUrl = function (option) {

            const outputFileName = determineBuildingDescription(vm.assessment, option)

            return assessmentdrawingservice.zipArchiveForOriginalDocumentsDownloadUrl(
                vm.assessment.assessmentId, option.complianceOptionsId, outputFileName);

        }

        function determineBuildingDescription(assessment, option) {

            const clientJobNumber = assessment.assessmentProjectDetail.clientJobNumber;
            const address = assessment.assessmentProjectDetail.fullAddress;

            // Client Job Number - Project Address - Baseline/Option 1/Option 2 - Proposed/Reference/Deemed-to-Satisfy Building
            return `${clientJobNumber} - ${address} - ${optionTitle(option)}`;
        }

        function optionTitle(option) {
            return option.isBaselineSimulation === true
                ? "Baseline"
                : "Option " + option.optionIndex;
        }

        vm.downloadAllCurrentDrawings = function (option) {
            var a = document.createElement('a');
            a.href = vm.downloadAllCurrentDrawingsUrl(option);
            a.download = option.fileName;
            a.click();
        }

        let waitingForCompletedDrawings = false;
        async function getAssessmentOptionDrawingsStillProcessing() {

            // Retrieve info on any/all drawings still undergoing processing.
            // Any new info that is retrieved is
            const data = await assessmentservice.getAssessmentOptionDrawingsStillProcessing(
                vm.assessment.assessmentId, 
                vm.option.complianceOptionsId
            );

            // console.log("Incoming drawing data is: ", data);
            if(data && data.length > 0) {
                // Since we have data, we wish to get updates quicker, so short delay.
                mergeDrawings(vm.option, data);

                delayThenPollAssessmentDrawingsStillProcessing(1000);
                waitingForCompletedDrawings = true;
            } else {

                // If we were waiting for completed drawings to come through (which WON'T through the polling,
                // as they are complete...) we instead do a full check on getAssessmentDrawings. This will return
                // the entire drawing set for the entire assessment.
                if(waitingForCompletedDrawings) {
                    getAssessmentDrawings();
                    waitingForCompletedDrawings = false;
                }

                // Since there's no data, we delay for a full 10s.
                delayThenPollAssessmentDrawingsStillProcessing(10000);
            }
        }

        let pollAssessmentDrawingsStillProcessingTimeout = null;
        function delayThenPollAssessmentDrawingsStillProcessing(delay = 3000) {
            pollAssessmentDrawingsStillProcessingTimeout = setTimeout(function () {
                getAssessmentOptionDrawingsStillProcessing();
            }, 3000);
        }

        function clearAllTimeouts() {
            if (pollAssessmentDrawingsStillProcessingTimeout) {
                clearTimeout(pollAssessmentDrawingsStillProcessingTimeout);
                pollAssessmentDrawingsStillProcessingTimeout = null;
            }
        }

        vm.determineCurrentDrawingsToShow = function (showArchived) {

            const drawings = vm.option.assessmentDrawings
                ?.filter(x => x.deleted === false && x.archived === showArchived);

            return drawings;
        }

        function mergeDrawings(option, incoming) {

            for (let ii = 0; ii < incoming.length; ii++) {

                let goodMatchFound = false;
                let deletedMatchFound = false;

                const incomingDrawing = incoming[ii];
                incomingDrawing.notifiedClientOfProcessingCompletion = true;

                for (let jj = 0; jj < option.assessmentDrawings.length; jj++) {

                    const currentDrawing = option.assessmentDrawings[jj];

                    if (currentDrawing.assessmentDrawingId === incomingDrawing.assessmentDrawingId) {

                        if(currentDrawing.deleted === false) {
                            angular.merge(currentDrawing, incomingDrawing);
                            goodMatchFound = true;
                        } else {
                            deletedMatchFound = true;
                        }

                        break;
                    }
                }

                if (goodMatchFound === false && deletedMatchFound === false)
                    option.assessmentDrawings.push(incoming[ii]);
            }
            option.assessmentDrawings = option.assessmentDrawings.sort((a,b) => a.drawingNumber > b.drawingNumber ? 1 : -1);
        }

        async function getAssessmentDrawings() {

            let allDrawingsInOption = await assessmentdrawingservice.getByComplianceOption(vm.assessment.assessmentId, vm.option.complianceOptionsId);

            if(allDrawingsInOption == null || allDrawingsInOption.length === 0)
                return;

            mergeDrawings(vm.option, allDrawingsInOption);
            common.setDrawingReferences(vm.option.assessmentDrawings)

            //Wait for more files to finish, poll again
            delayThenPollAssessmentDrawingsStillProcessing();

        }

        vm.pageIsHorizontal = function(page) {

            if(page.originalWidth == null || page.originalHeight == null)
                return 'unknown';

            if (page.rotation % 4) {
                return page.originalHeight > page.originalWidth
                    ? 'true'
                    : 'false';

            } else {
                return page.originalWidth > page.originalHeight
                    ? 'true'
                    : 'false';
            }
        }

        vm.disableStampFunctionality = function() {
            return vm.disabled === true && 
                (vm.assessment.statusCode !== 'AOptionSelected' && 
                 vm.assessment.statusCode !== 'AComplete' &&
                 vm.assessment.statusCode !== 'ACompliance'    
                );
        }

        $scope.$on('$destroy', clearAllTimeouts);

        // TODO: Make an initialize() and $destroy function
        common.setDrawingReferences(vm.option.assessmentDrawings);
        getAssessmentOptionDrawingsStillProcessing();

    }

})();