// Name: userservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'userservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', userservice]);

    function userservice(common, config, $http) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'user/';

        var service = {
        /* These are the operations that are available from this service. */
            getList,
            getListCancel,
            currentFilter: function () { return currentFilter },
            getUser,
            getUserByAspNetId,
            getRolesListSelections,
            userNameAvailable,
            createUser,
            updateUser,
            updateRolesListSelections,
            deleteUser,
            undoDeleteUser,
            groupRolesByName,
        };
        
        function getList(forFilter, fromDate, toDate, pageSize,
            pageIndex, sort, filter, aggregate, clientId = null) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);

            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }

            //params.userType = userType; // Optional
            params.clientId = clientId; // Optional

            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting User list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        function getUser(UserId) {
            return $http({
                url: baseUrl + 'Get',
                params: {UserId: UserId},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting User: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
        
        function getUserByAspNetId(aspNetUserId) {
            return $http({
                url: baseUrl + 'GetByAspNetUserId',
                params: { aspNetUserId: aspNetUserId },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting User: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
        
        function getRolesListSelections(userId) {
            return $http({
                url: baseUrl + 'GetRolesListSelections',
                params: { userId: userId },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting User: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }
        
        function userNameAvailable(userName) {
            return $http({
                url: baseUrl + 'UserNameAvailable',
                params: { userName },
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting User: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createUser(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("User Created");
                return resp;
            }
            function fail(error) {
                var msg = "Error created User: " + error.data.exceptionMessage;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        /**
         * Updates the current user with the passed in dto.
         * 
         * @param {any} data UserDto to apply.
         * @param {any} enableLogin When TRUE, enables login for the user (if already existing)
         * or CREATES login details (but must provide username/password in this case).
         * @param {any} disableLogin Flags the users account as being disabled, preventing the user from logging in.
         */
        function updateUser(data, enableLogin = false, disableLogin = false) {
            var url = baseUrl + `Update?enableLogin=${enableLogin}&disableLogin=${disableLogin}`;
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("User Changes Saved");
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating User: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateRolesListSelections(listSelectionsList) {
            var url = baseUrl + `UpdateRolesListSelections`;
            return $http.post(url, listSelectionsList).then(success, fail)
            function success(resp) {
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating UpdateRolesListSelections: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteUser(UserId) {
            return $http({
                url: baseUrl + 'Delete',
                params: { UserId: UserId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting User: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteUser(UserId) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { UserId: UserId },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for User: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        const tabsOrder = {
            "assessment": 0,
            "invoicing": 1,
            "site": 2,
            "address": 3,
            "map": 4,
            "aerialimage": 5,
            "climate": 6,
            "lot": 7,
            "design": 8,
            "construction": 9,
            "openings": 10,
            "services": 11,
            "drawings": 12,
            "simulation": 13,
            "analytics": 14,
            "results": 15,
            "reports": 16,
        }
        
        /** Loops through all of our Roles (aka Permissions) and forms groups based on certain heuristics. */
        function groupRolesByName(inRoles) {
            
            let roles = angular.copy(inRoles);
            roles.sort((a,b) => a.sortOrder > b.sortOrder ? 1 : -1);

            // Extract data for each Role
            roles = roles.map(r => {
                let codeSplit = r.name.split("__");
                let groupTitleRaw = codeSplit[0].replaceAll("_", " ");
                // Special handling for energyLab to display as EnergyLab instead of Energy Lab
                let groupTitle = groupTitleRaw.toLowerCase() === "energylab" ? "EnergyLab" : common.toTitleCase(groupTitleRaw);
                let rowDescription = r.description;
                let roleHeading = common.toTitleCase(codeSplit.length > 2 ? codeSplit[2].replaceAll("_", " ") : null);
                let typeSplit = r.type.split("__");
                let type = typeSplit[0];
                let typeData = typeSplit.length > 1 ? typeSplit[1] : null;
                return {
                    roleId: r.roleId,
                    parentRoleId: r.parentRoleId,
                    name: r.name,
                    groupTitle,
                    rowDescription,
                    roleHeading,
                    type,
                    typeData,
                };
            });

            // Setup Groups
            const groups = [];
            roles.forEach(roleObj => {

                try {
                    
                    // Setup Group
                    let theGroup = groups.find(g => g.title === roleObj.groupTitle);
                    if (!theGroup) {
                        theGroup = {
                            title: roleObj.groupTitle,
                            headings: [],
                            rows: []
                        };
                        groups.push(theGroup);
                    }

                    // Add Heading
                    if (roleObj.roleHeading != null && theGroup.headings.find(h => h == roleObj.roleHeading) == null) {
                        theGroup.headings.push(roleObj.roleHeading);
                    }

                    // Setup Row
                    let theRow = theGroup.rows.find(r => r.description == roleObj.rowDescription);
                    if (!theRow) {
                        let nth = 0;
                        let tempRole = roles.find(r => r.roleId.toLowerCase() == roleObj.parentRoleId?.toLowerCase() && r.groupTitle == roleObj.groupTitle);
                        while (tempRole != null) {
                            nth++;
                            tempRole = roles.find(r => r.roleId.toLowerCase() == tempRole.parentRoleId?.toLowerCase() && r.groupTitle == tempRole.groupTitle);
                        }
                        theRow = {
                            description: roleObj.rowDescription,
                            nthChild: nth,
                            roles: []
                        };
                        theGroup.rows.push(theRow);
                    }
                    theRow.roles.push({
                        roleId: roleObj.roleId,
                        parentRoleId: roleObj.parentRoleId,
                        name: roleObj.name,
                        type: roleObj.type,
                        typeData: roleObj.typeData
                    });


                } catch(e) {
                    console.warn("Problem grouping role:", roleObj);
                    console.warn("Error: ", e);
                }

            });

            return groups;
            /*

                [{
                    title: "Home Page / Jobs Page,
                    headings: [
                        "Assessor",
                        "Client"
                    ],
                    rows: [{
                        description: "Jobs Assigned To Self",
                        nthChild: 0,
                        roles: [
                            { roleId: '308AF635-C362-43AD-BC97-AC28FC477A21', parentRoleId: 'E0E19AF3-09EB-41E3-B573-50D0C01B49DD', name: "home_page_/_job_page__assignedself__assessor", type: "Disabled", typeData: "Self"  },
                            { roleId: '6CD7F77F-01AF-4C7B-9B3C-B99FC842702F', parentRoleId: 'B3B9BE2F-9F70-41B9-95FF-3E2F634D1039', name: "home_page_/_job_page__assignedself__client",   type: "List",     typeData: "Client" }
                        ]
                    }]
                },{
                    title: "Assessment Page (Tabs/Sub-Tabs),
                    headings: [
                        "View",
                        "Edit"
                    ],
                    rows: [{
                        description: "Assessment",
                        nthChild: 0,
                        roles: [
                            { roleId: 'CA30E5A4-F16F-44E7-84D3-7DD003955E6B', parentRoleId: 'DD9FCF77-B480-4109-BBF3-51954AC2324E', name: "assessment_page_(tabs/sub-tabs)__assessment__view", type: "Checkbox" },
                            { roleId: '88D12CEB-D5F7-4E43-96CB-D0A411812B2B', parentRoleId: '02F281F6-B75F-4ACA-8EAF-BCA976B4DE32', name: "assessment_page_(tabs/sub-tabs)__assessment__edit", type: "Checkbox" }
                        ]
                    },{
                        description: "Creator",
                        nthChild: 1,
                        roles: [
                            { roleId: 'F29E0C34-C6EC-4CF1-B90C-4C8C17776603', parentRoleId: '566E99E1-15DC-4CC5-97D5-7460393AD70F', name: "assessment_page_(tabs/sub-tabs)__creator__view", type: "Checkbox" },
                            { roleId: '8F0DCA6D-8F0C-4785-BDF2-E27906A8D15C', parentRoleId: '95D980C4-9015-4A3D-BDD3-C833E0AB5265', name: "assessment_page_(tabs/sub-tabs)__creator__edit", type: "Checkbox" }
                        ]
                    }]
                }]
            */

        }

        return service;
    }
})();
