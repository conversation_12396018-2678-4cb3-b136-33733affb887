<md-autocomplete md-selected-item="vm.selectedSuburb"
                 md-search-text="vm.searchSuburbText"
                 md-items="item in vm.searchSuburb(vm.searchSuburbText)"
                 md-selected-item-change="vm.searchSuburbItemChange()"
                 md-item-text="item.name"
                 md-input-maxlength="100"
                 md-select-on-match="true"
                 md-no-cache="true"
                 md-delay="300"
                 ng-required="vm.required"
                 md-require-match
                 ng-disabled="vm.isDisabled"
                 flex-gt-sm="100"
                 md-floating-label="Suburb"
                 md-input-name="searchSuburb"
                 placeholder="Suburb"
                 ng-class="{'vertically-condensed'  : vm.condensed }"
                 ng-blur="vm.suburbBlur()">
  <md-item-template>
    <div class="custom-dropdown-option">
        <span md-highlight-text="vm.name">{{item.name}}, {{item.stateCode}}, {{item.postcode}}</span>
        <div ng-if="vm.copyAcrossEnabled" class="copy-across-button" ng-click="vm.copyOptionAcross($event, item);"><img style="margin-bottom: 15px;" src="/content/images/share.png"/></div>
    </div>
  </md-item-template>
  <md-not-found>
    No suburb found.
  </md-not-found>
</md-autocomplete>