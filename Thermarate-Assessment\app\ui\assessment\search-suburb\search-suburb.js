(function () {
	'use strict';
	angular
		.module('app')
		.component('searchSuburb', {
			bindings: {
				initialSuburbText: '<',
                selectedSuburb: '=',
				suburbChanged: '&',
                changeOnBlur: '<',
                copyAcrossEnabled: '<',
                copyAcrossData: '=?',
                copyAcrossTrigger: '&',
				condensed: '<',
				required: '<',
				isDisabled: '<',
			},
			templateUrl: 'app/ui/assessment/search-suburb/search-suburb.html',
			controller: SearchSuburb,
			controllerAs: 'vm'
		});

	SearchSuburb.$inject = ['$scope', '$timeout', 'addressservice'];

	function SearchSuburb($scope, $timeout, addressservice) {
		var vm = this;

		vm.searchSuburbText = '';

		vm.searchSuburb = searchSuburb;
        vm.searchSuburbItemChange = searchSuburbItemChange;

        var selectionChanged = false;

		vm.$onInit = function () {
			if (vm.initialSuburbText != null && vm.initialSuburbText != "")
				vm.searchSuburbText = vm.initialSuburbText;
		}

        $scope.$watch('vm.initialSuburbText', (newValue, oldValue) => {
            // IF parent has cleared suburb, also clear the displayed input text
            if (oldValue != null && newValue == null) {
                vm.searchSuburbText = '';
            } 
        });
        $scope.$watch('vm.selectedSuburb', (newValue, oldValue) => {
            // IF parent has cleared suburb, also clear the displayed input text
            if (oldValue != null && newValue == null) {
                vm.searchSuburbText = '';
            } 
        });

		function searchSuburb(search) {
			if (!search) {
				return Promise.resolve([]);
			}
			if (search.length < 3) {
				return Promise.resolve([]);
			}
			return addressservice.querySuburb(search);
		}

        function searchSuburbItemChange() {
            if (vm.selectedSuburb != null) {
                selectionChanged = true;
            }

            if (!vm.changeOnBlur && selectionChanged) {
                $timeout(() => {
                    vm.suburbChanged();
                    selectionChanged = false;
                });
            }
        }

        vm.suburbBlur = function () {
            if (vm.changeOnBlur && selectionChanged && vm.selectedSuburb != null) {
                $timeout(() => vm.suburbChanged());
                selectionChanged = false;
            }
            if ((vm.searchSuburbText == null || vm.searchSuburbText == '')) {
                $timeout(() => {
                    vm.suburbChanged();
                    selectionChanged = false;
                });
            }
        }

        vm.copyOptionAcross = function (event, suburb) {
            // Tell parent the variable and selection so it can set on every other building
            vm.copyAcrossData.field = 'suburb';
            vm.copyAcrossData.option = suburb;
            $timeout(() => {
                // Now tell parent to copy across
                vm.copyAcrossTrigger();
                // Make sure option still clicked so dropdown collapses
                event.currentTarget.previousElementSibling.click();
            });
        }

	}
})();