<form name="compliancemethodform" class="main-content-wrapper" novalidate data-ng-controller='CompliancemethodUpdateCtrl as vm'>

    <div class="widget" ng-cloak>
        <div data-cc-widget-header
                data-title="{{vm.title}}"
                data-is-modal="vm.isModal"
                data-cancel="vm.cancel()"
                data-back-button>
        </div>
        <div data-cc-widget-action-bar
                data-quick-find-model=''
                data-action-buttons='vm.actionButtons'
                data-refresh-list=''
                data-spinner-busy='vm.isBusy'
                data-new-record=""
                data-new-record-text=""
                data-is-modal="vm.isModal"
                data-hide="vm.hideActionBar">
        </div>
        <div data-cc-widget-content
                data-is-modal="vm.isModal">
            <div layout="row" layout-sm="column" layout-xs="column">
                <div class="flex-50">
                    <md-card>
                        <md-card-header>
                            Compliance Method
                        </md-card-header>
                        <md-card-content>

                          <fieldset redi-enable-roles="settings__settings__edit">

                            <!-- ******** Compliance Method Code ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                              <label>Compliance Method Code</label>
                              <input type="text" name="complianceMethodCode"
                                     ng-model="vm.compliancemethod.complianceMethodCode" 
                                     md-autofocus
                                     ng-disabled="vm.newRecord == false"
                                     md-maxlength="20"
                                     required
                              />
                              <div ng-messages="compliancemethodform.complianceMethodCode.$error">
                                <div ng-message="required">Compliance Method Code is required.</div>
                                <div ng-message="md-maxlength">Too many characters entered, max length is 20.</div>
                              </div>
                            </md-input-container>

                            <!-- ******** Description ******** -->
                            <md-input-container class="md-block" flex-gt-sm>
                              <label>Description</label>
                              <input type="text" name="description"
                                     ng-model="vm.compliancemethod.description"
                                     md-maxlength="100"
                                     required
                              />
                              <div ng-messages="compliancemethodform.description.$error">
                                <div ng-message="md-maxlength">Too many characters entered, max length is 100.</div>
                              </div>
                            </md-input-container>

                            <!-- ******** Prepared By Override User ******** -->
                            <md-autocomplete md-input-name="Prepared By Override"
                                             class="md-block"
                                             style="width:250px"
                                             flex="100"
                                             flex-gt-sm="50"
                                             md-input-minlength="2"
                                             md-min-length="0"
                                             md-selected-item="vm.compliancemethod.preparedByOverrideUser"
                                             md-search-text="vm.preparedByOverrideUserSearchText"
                                             md-items="item in vm.getemployees(vm.preparedByOverrideUserSearchText)"
                                             md-item-text="item.fullName"
                                             md-floating-label="Prepared By Override">
                                <md-item-template>
                                    <span md-highlight-text="vm.preparedByOverrideUserSearchText">{{item.fullName}}</span>
                                </md-item-template>
                            </md-autocomplete>

                          </fieldset>

                        <div class="col-md-12" ng-if="vm.newRecord==false">
                            <div rd-display-created-modified ng-model="vm.compliancemethod"></div>
                        </div>
                    </md-card-content>
                </md-card>
            </div>
            </div>
            <div data-cc-widget-button-bar
                    data-is-modal="vm.isModal">
                <div data-ng-show="vm.isBusy" data-cc-spinner="vm.spinnerOptions"></div>
                <md-button class="md-raised md-primary" 
                           ng-disabled="compliancemethodform.$invalid || vm.editPermission == false"
                           ng-show="vm.compliancemethod.deleted!=true" 
                           ng-click="vm.save()">
                  Save
                </md-button>
                <md-button class="md-raised" 
                           redi-enable-roles="settings__settings__delete"
                           ng-show="vm.compliancemethod.complianceMethodCode!=null && vm.compliancemethod.deleted!=true" 
                           ng-confirm-click="vm.delete()" 
                           ng-confirm-condition="true" 
                           ng-confirm-message="Please confirm you want to delete this record.">
                  Delete
                </md-button>
                <md-button class="md-raised"
                           redi-enable-roles="settings__settings__delete"
                           ng-show="vm.compliancemethod.deleted==true" 
                           ng-confirm-click="vm.undoDelete()" 
                           ng-confirm-condition="true" 
                           ng-confirm-message="Please confirm you want to RESTORE this record.">
                  Restore
                </md-button>
                <md-button class="md-raised" 
                           ng-click="vm.cancel()">Cancel</md-button>
                <div class="clearfix"></div>
            </div>

        </div>
    </div>
</form>       
