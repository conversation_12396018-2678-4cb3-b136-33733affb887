// Name: compliancemethodservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'compliancemethodservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', 'energyloadlimitservice', compliancemethodservice]);

    function compliancemethodservice(common, config, $http, energyloadlimitservice) {
        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'compliancemethod/';

        var service = {
            /* These are the operations that are available from this service. */
            getList: getList,
            getListCancel: getListCancel,
            currentFilter: function () { return currentFilter },
            getComplianceMethod: getComplianceMethod,
            createComplianceMethod: createComplianceMethod,
            updateComplianceMethod: updateComplianceMethod,
            deleteComplianceMethod:deleteComplianceMethod,
            undoDeleteComplianceMethod: undoDeleteComplianceMethod,
            calculateHERCompliance: calculateHERCompliance,
            calculatePerfCompliance: calculatePerfCompliance,
            calculateEleCompliance: calculateEleCompliance,
            determineAvailableSoftware: determineAvailableSoftware,
            determineAvailableComplianceMethods,
            constructHeatingLoadLimitAreaCorrectionFactor,
            constructCoolingLoadLimitAreaCorrectionFactor
        };
            
        return service;

        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = { fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server 
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    useListCache = true;
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                if (error.status == 0 || error.status == -1) {
                    return;
                }
                var msg = "Error getting ComplianceMethod list: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }
        
        function getComplianceMethod(complianceMethodCode) {
            return $http({
                url: baseUrl + 'Get',
                params: {complianceMethodCode: complianceMethodCode},
                method: 'GET',
            }).then(success, fail)
            function success(resp) {
                if (resp != null && resp.data != undefined && resp.data != null) {
                    return resp.data;
                }
                else {
                    return null;
                }
            }
            function fail(error) {
                var msg = "Error getting ComplianceMethod: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function createComplianceMethod(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Compliance Method Created");
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error created ComplianceMethod: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function updateComplianceMethod(data) {
            var url = baseUrl + 'Update';
            return $http.post(url, data).then(success, fail)
            function success(resp) {
                log.logSuccess("Compliance Method Changes Saved");
                useListCache = false;
                return resp.data;
            }
            function fail(error) {
                var msg = "Error updating ComplianceMethod: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function deleteComplianceMethod(complianceMethodCode) {
            return $http({
                url: baseUrl + 'Delete',
                params: { complianceMethodCode: complianceMethodCode },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error deleting ComplianceMethod: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }

        function undoDeleteComplianceMethod(complianceMethodCode) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { complianceMethodCode: complianceMethodCode },
                method: 'POST',
            }).then(success, fail)
            function success(resp) {
                useListCache = false;
                return resp;
            }
            function fail(error) {
                var msg = "Error undoing delete for ComplianceMethod: " + error;

                log.logError(msg, error, null, true);
                throw error; // so caller can see it
            }
        }


        // Checks to see whether the given option meets compliance and updates the given status within the supplied assessment. 
        // Note: The 'option' parameter can also refer to the 'proposedRun'
        // House Energy Rating Rules
        // Compliance is achieved when:
        // House Energy Rating >= Required Energy Rating AND
        // Heating Load <= Heating Load Limit AND
        // Cooling Load <= Cooling Load Limit
        async function calculateHERCompliance(assessment, option) {

            const NotApplicable = -1;
            
            let _minHouseEnergyRating = option.requiredHouseEnergyRating;
            var natHersClimateZone = assessment.natHERSClimateZoneCode.substr(3, 2);
            var floorType = option.proposed.lowestLivingAreaFloorType;

            // Don't run any calculations until we have this code.
            if (assessment.natHERSClimateZoneCode == null || _minHouseEnergyRating == null || option.certification == null) {

                if(option.reference.overrideEnergyLoads === false) {
                    option.reference.heating = NotApplicable;
                    option.reference.cooling = NotApplicable;
                    option.reference.totalEnergyLoad = NotApplicable;
                }
                
                if(option.proposed.overrideEnergyLoads === false)
                    option.proposed.houseEnergyRating = NotApplicable;

                return false
            }
            
            if(option.isComplianceValid === false) {
                return false;
            }
            
            let data = await energyloadlimitservice.getCombinedEnergyData(
                natHersClimateZone,
                _minHouseEnergyRating,
                floorType,
                option.proposed.totalEnergyLoad,
                option.certification.decimalStarbandRulesetCode,
                assessment.assessmentProjectDetail.stateCode
            );
            
            if (data != null) {

                if(option.proposed.overrideEnergyLoads === false) {
                    option.proposed.houseEnergyRating = data.calculatedHER;
                }

                if(option.reference.overrideEnergyLoads === false) {
                    option.reference.totalEnergyLoad = data.calculatedMaxEnergy;
                    option.reference.heating = data.heatingLoadLimit ?? NotApplicable;
                    option.reference.cooling = data.coolingLoadLimit ?? NotApplicable;
                }
            }
            else if(option.reference.overrideEnergyLoads === false) {
                option.reference.heating = NotApplicable;
                option.reference.cooling = NotApplicable;
                option.reference.totalEnergyLoad = NotApplicable;
            }
            
            if(option.heatingAndCoolingRulesetCode === 'Disabled' && option.reference.overrideEnergyLoads === false)
            {
                option.reference.heating = NotApplicable;
                option.reference.cooling = NotApplicable;
            }

            // Short circuit all this other calculation if there are null values present.
            if (option.proposed.heating == null || option.proposed.cooling == null || option.proposed.houseEnergyRating == null) {
                return false;
            }
            
            let metRating = false;
            let metHeating = false;
            let metCooling = false;

            if(option.complianceMethod.complianceMethodCode !== "CMPerfELL") {
                metRating = option.proposed.houseEnergyRating >= option.requiredHouseEnergyRating;
                metHeating = option.reference.heating === NotApplicable ? true : option.proposed.heating <= option.reference.heating;
                metCooling = option.reference.cooling === NotApplicable ? true : option.proposed.cooling <= option.reference.cooling;
            } else {
                
                if(assessment.comfortMetrics == null || assessment.comfortMetrics.heatingDegreeHours == null ||
                    assessment.comfortMetrics.coolingDegreeHours == null) {
                    return false;
                }

                let habitableZones = option.proposed?.zones?.filter(z => z.zoneType?.zoneTypeCode === "ZTHabitableRoom");
                let floorArea = habitableZones.map(x => x.floorArea).reduce((a, b) => a + b, 0);
                
                // Heating Load Limit
                const HDH = assessment.comfortMetrics.heatingDegreeHours;
                const HLLACF = constructHeatingLoadLimitAreaCorrectionFactor(floorArea);
                const heatingCalc = ((0.0044 * HDH) - 5.9) * HLLACF;
                
                option.reference.heating =  4 > heatingCalc ? 4 : heatingCalc;
                
                // Cooling Load limit
                const CDH = assessment.comfortMetrics.coolingDegreeHours;
                const CLLACF = constructCoolingLoadLimitAreaCorrectionFactor(floorArea);
                const DGH = assessment.comfortMetrics.dehumidificationGramHours;

                let coolingCalc = (5.4 + (0.00617 * (CDH + (1.85 * DGH)))) * CLLACF;
                option.reference.cooling = coolingCalc;
                
                // Thermal Energy Load Limit
                const HLL = option.reference.heating;
                const CLL = option.reference.cooling;
                const Tr = assessment.comfortMetrics.annualAvgDailyTempRange;
                
                const totalCalc = (((19.3 * HLL) + (22.6 * CLL) - 8.4) / (Tr + 10.74 )) - 15;
                option.reference.totalEnergyLoad = totalCalc;
                
                // Determine whether conditions have been met...
                metRating = option.proposed.totalEnergyLoad <=  option.reference.totalEnergyLoad;
                metHeating = option.proposed.heating <= option.reference.heating;
                metCooling = option.proposed.cooling <= option.reference.cooling;

                option.reference.heating = Number(option.reference.heating.toFixed(1));
                option.reference.cooling = Number(option.reference.cooling.toFixed(1));
                const backupTotal = option.reference.totalEnergyLoad;
                option.reference.totalEnergyLoad = Number(option.reference.totalEnergyLoad.toFixed(1));

                // Determine what the equivalent HER would have been for the 'reference' data.
       
                // Reference 'equivalent her' is always calculated for PerfELL as we can not override it.
                option.reference.lowestLivingAreaFloorType = option.proposed.lowestLivingAreaFloorType; // Reference building does not exist
                calculateEquivalentHER(assessment, option, option.reference, backupTotal);
                
            }
            
            let compliant = metRating && metHeating && metCooling;
            
                
            return compliant;

        }


        /** 
         *  Performance Solution (VURB) is compliant when
         *  Compliance is achieved if the following conditions are met:
         *  1.When NCC Climate Zone = 1 OR 2: AND ProposedBuilding Cooling Load(kWh / m�) <= ReferenceBuildingCooling Load(kWh / m�)
         *  2.When NCC Climate Zone = 7 OR 8: AND ProposedBuilding Heating Load(kWh / m�) <= ReferenceBuildingHeating Load(kWh / m�) 
         *  3.When NCC Climate Zone = 3, 4, 5 OR 6: AND ProposedBuilding Heating Load(kWh / m�) <= ReferenceBuildingHeating Load(kWh / m�) 
         *                                          AND ProposedBuilding Cooling Load(kWh / m�) <= ReferenceBuildingCooling Load(kWh / m�) */
        function calculatePerfCompliance(assessment, option) {
            
            if (isNaN(option.reference.totalEnergyLoad))
                option.reference.totalEnergyLoad = 0;
            
            if (isNaN(option.proposed.totalEnergyLoad))
                option.proposed.totalEnergyLoad = 0;

            if(option.isComplianceValid === false)
                return false;

            var zone = 0;

            if (!assessment.nccClimateZone) {
                
                if (assessment.nccClimateZoneCode != null) 
                    zone = parseInt(assessment.nccClimateZoneCode.substr(3, 1));
                else 
                    return false;
            }
            else 
                zone = parseInt(assessment.nccClimateZone.description);

            var compliant = false;

            if (zone === 1 || zone === 2) {

                if (option.proposed.cooling == null || option.reference.cooling == null)
                    compliant = false;
                else
                    compliant = option.proposed.cooling <= option.reference.cooling;
            }
            else if (zone === 7 || zone === 8) {

                if (option.proposed.heating == null || option.reference.heating == null)
                    compliant = false;
                else
                    compliant = option.proposed.heating <= option.reference.heating;
            }
            else if (zone > 2 && zone < 7) {

                if (option.proposed.cooling == null || option.reference.cooling == null ||
                    option.proposed.heating == null || option.reference.heating == null)
                    compliant = false;
                else
                    compliant =
                        option.proposed.cooling <= option.reference.cooling &&
                        option.proposed.heating <= option.reference.heating;
            }

            if(option.proposed.overrideEnergyLoads === false)
                calculateEquivalentHER(assessment, option, option.proposed, option.proposed.totalEnergyLoad);

            // reference.lowestLivingFloorArea was null, making entire result null.
            if(option.reference.overrideEnergyLoads === false)
                calculateEquivalentHER(assessment, option, option.reference, option.reference.totalEnergyLoad);

            return compliant;
        }

        // Elemental Provisions is compliant when
        // If every single check box (for all floors) is CHECKED AND a glazing calculation file is attached...
        function calculateEleCompliance(assessment, option) {
            
            if (option == null)
                return false;

            if(option.isComplianceValid === false) {
                return false;
            }

            let x = option.epComplianceData;

            if (x == null)
                return false;

            let compliant = isYesOrNA(x.insulation) &&
                isYesOrNA(x.roofAndCeiling) &&
                isYesOrNA(x.roofLights) &&
                isYesOrNA(x.externalWalls) &&
                isYesOrNA(x.externalFloors) &&
                isYesOrNA(x.class10a) &&
                isYesOrNA(x.externalGlazing) &&
                isYesOrNA(x.buildingSealing) &&
                isYesOrNA(x.airMovement);

            calculateEquivalentHER(assessment, option, option.proposed, option.proposed.totalEnergyLoad);

            return compliant;
        }

        function isYesOrNA(val) {
            return val === 'Yes' || val === 'N/A';
        }
        
        /**
         * Called on methods which do NOT require a HER calculation to determine what HER they WOULD have achieved
         * 
         * @param assessment The assessment
         * @param option The compliance option
         * @param building The building to calculate a HER for.
         * */
        function calculateEquivalentHER(assessment, option, building, totalEnergyLoad) {
            
            var natHersClimateZone = assessment.natHERSClimateZoneCode.substr(3, 2);
            
            // Determine what the equivalent HER would have been for the building building.
            energyloadlimitservice.getCombinedEnergyData(
                natHersClimateZone,
                option.requiredHouseEnergyRating,
                building.lowestLivingAreaFloorType,
                totalEnergyLoad,
                option.certification.decimalStarbandRulesetCode,
                assessment.assessmentProjectDetail.stateCode
            ).then(data => {
                building.houseEnergyRating = data.calculatedHER;
            });
            
        }

        /**
         *  Returns a list of all Assessment Software which are available to use for the supplied compliance method code.
         *  
         * @param {any} allSoftware A list of EVERY assessment software available (Unless you want to restrict it for some other reason?)
         * @param {any} complianceMethodCode The compliance method to check against.
         */
        function determineAvailableSoftware(allSoftware, complianceMethodCode) {

            let availableSoftware = allSoftware?.filter(asw => asw.assessmentSoftwareComplianceMethods
                ?.find(method => method.complianceMethodCode === complianceMethodCode && method.isAvailable === true));

            return availableSoftware;
        }
        
        function determineAvailableComplianceMethods(allComplianceMethods, worksDescriptionCode) {
            if(worksDescriptionCode === "WDAlteration")
                return allComplianceMethods;
            else
                return allComplianceMethods?.filter(x => x.complianceMethodCode !== "CMPerfWAProtocolHER" && 
                                                         x.complianceMethodCode !== "CMPerfWAProtocolEP");
        }

        // Refer to THR-276
        function constructHeatingLoadLimitAreaCorrectionFactor(floorArea) {
            let heatingLoadLimitCorrectionFactor;
            if (floorArea <= 50)
                heatingLoadLimitCorrectionFactor = 1.37;
            else if (floorArea <= 350)
                heatingLoadLimitCorrectionFactor = (5.11e-6 * floorArea * floorArea) - (3.82e-3 * floorArea) + 1.55;
            else // floorArea > 350
                heatingLoadLimitCorrectionFactor = 0.84;

            return heatingLoadLimitCorrectionFactor;
        }

        // Refer to THR-276
        function constructCoolingLoadLimitAreaCorrectionFactor(floorArea) {

            let coolingLoadLimitCorrectionFactor;
            if (floorArea <= 50)
                coolingLoadLimitCorrectionFactor = 1.34;
            else if (floorArea <= 200)
                coolingLoadLimitCorrectionFactor = (1.29e-5 * floorArea * floorArea) - (5.55e-3 * floorArea) + 1.58;
            else if (floorArea <= 1000)
                coolingLoadLimitCorrectionFactor = (3.76e-7 * floorArea * floorArea) - (7.82e-4 * floorArea) + 1.12;
            else // floorArea > 1000
                coolingLoadLimitCorrectionFactor = 0.71;

            return coolingLoadLimitCorrectionFactor;
        }

    }
})();
