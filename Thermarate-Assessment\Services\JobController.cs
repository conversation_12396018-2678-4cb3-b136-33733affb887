using AutoMapper;
using AutoMapper.QueryableExtensions;
using Kendo.DynamicLinq;
using RediSoftware.BusinessLogic;
using RediSoftware.Common;
using RediSoftware.Helpers;
using RediSoftware.Dtos;
using RediSoftware.Models;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Globalization;
using System.Linq;
using System.Web.Http;
using System.Threading.Tasks;
using System.Text;
using Newtonsoft.Json;
using System.Web.UI.WebControls;
using RediSoftware.Redi_Utility;
using AuthorizeAttribute = System.Web.Mvc.AuthorizeAttribute;
using RoutePrefixAttribute = System.Web.Http.RoutePrefixAttribute;
using HttpPostAttribute = System.Web.Http.HttpPostAttribute;

namespace RediSoftware.Services
{
    [Authorize]
    [RoutePrefix("api/Job")]
    public class JobController : ApiController
    {

        // - --------- - //
        // - VARIABLES - //
        // - --------- - //

        private readonly SqlConnection _connection;
        private readonly IMapper _mapper;
		private readonly IUnitOfWork _unitOfWork;
		private Func<Job> _jobFactory;
        private readonly NotificationFramework _notificationFramework;
        bool _canViewOwnJobs;
        bool _canViewOtherJobs;
        Guid _currentUserId;
        List<RolesUserSelectionsDataDto> _selectionsDataList;
        private string[] _activeStatuses = new string[] {
            JobStatus.JDraft.ToString("G"),
            JobStatus.JInProgress.ToString("G"),
            JobStatus.JCompliance.ToString("G"),
            JobStatus.JComplete.ToString("G")
        };
        private string STATUS_CANCELLED = JobStatus.JCancelled.ToString("G");
        private string STATUS_ISSUED = JobStatus.JIssued.ToString("G");

        private DataSourceResult<JobAssessmentProjectDetailDto> _defaultAssProjDetEmpty = new DataSourceResult<JobAssessmentProjectDetailDto>() { Data = new List<JobAssessmentProjectDetailDto>() };


        // - ----------- - //
        // - CONSTRUCTOR - //
        // - ----------- - //

        public JobController(IUnitOfWork unitOfWork,
                             IMapper mapper,
							 Func<Job> jobFactory,
                             Func<Assessment> assessmentFactory,
                             NotificationFramework notificationFramework)
        {
			_mapper = mapper;
			_unitOfWork = unitOfWork;
			_jobFactory = jobFactory;
            _notificationFramework = notificationFramework;
            _canViewOwnJobs   = UtilityFunctions.UserRoles.Contains("home_page_/_job_page__assignedself__view");
            _canViewOtherJobs = UtilityFunctions.UserRoles.Contains("home_page_/_job_page__assignedother__view");
            RSS_User dbUser = _unitOfWork.Context.RSS_User.Where(u => u.AspNetUserId == UtilityFunctions.UserID).FirstOrDefault();
            _currentUserId = dbUser.UserId;
            _selectionsDataList = _unitOfWork.Context.RSS_RolesUserSelectionsData.Where(r => r.UserId == dbUser.AspNetUserId.ToString()).ProjectTo<RolesUserSelectionsDataDto>(_mapper.ConfigurationProvider).ToList();

            _connection = new SqlConnection(ConfigurationManager.ConnectionStrings["RediSoftware"].ConnectionString);
            _connection.Open();
        }


        // - ------- - //
        // - PRIVATE - //
        // - ------- - //

        private IQueryable<RSS_JobAssessmentProjectDetail_View> InnerGet(
            string fromDate = null,
            string toDate = null,
            string statusCode = null,
            string clientCode = null,
            string assessorCode = null,
            bool? isDeleted = false,
            [FromUri] PagingParameters paging = null)
        {
            if (!_canViewOwnJobs && !_canViewOtherJobs)
                return null;

            paging?.SetDefaultSort("JobModifiedOn", "Desc");
            _unitOfWork.ReadOnly();

            IQueryable<RSS_JobAssessmentProjectDetail_View> query = null;
            switch(statusCode)
            {
                case "All":
                    query = _unitOfWork.Context.RSS_JobAssessmentProjectDetail_View.Where(a => a.AssessmentDeleted == false && a.JobDeleted == false);
                    break;
                case "Active":
                    query = _unitOfWork.Context.RSS_JobAssessmentProjectDetail_View.Where(a => (a.AssessmentDeleted == false && a.JobDeleted == false) && _activeStatuses.Contains(a.StatusCode));
                    break;
                case "Deleted":
                    query = _unitOfWork.Context.RSS_JobAssessmentProjectDetail_View.Where(a => (a.AssessmentDeleted && a.JobDeleted) || a.StatusCode == "JCancelled");
                    break;
                default:
                    query = _unitOfWork.Context.RSS_JobAssessmentProjectDetail_View.Where(a => (a.AssessmentDeleted == false && a.JobDeleted == false) && (!String.IsNullOrEmpty(statusCode) ? (a.StatusCode == statusCode) : true));
                    break;
            }

            if (fromDate != null || toDate != null)
            {
			    DateTime dtFromDate = DateTime.MinValue;
			    DateTime dtToDate = DateTime.MaxValue;
			    if (fromDate != null)
				     dtFromDate = DateTime.Parse(fromDate, null, DateTimeStyles.RoundtripKind).ToLocalTime();
			    if (toDate != null)
				     dtToDate = DateTime.Parse(toDate, null, DateTimeStyles.RoundtripKind).ToLocalTime();
                query = query.Where(a => a.JobModifiedOn >= dtFromDate && a.JobModifiedOn <= dtToDate);
            }

            if (assessorCode != null && assessorCode != "" && assessorCode != "All")
                query = query.Where(x => x.AssessorFullName == assessorCode);

            if (clientCode != null && clientCode != "" && clientCode != "All")
                query = query.Where(x => x.ClientName == clientCode);

            // Permissions
            List<string> assignedSelf_ClientIds             = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__client"               ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedSelf_StatusCodes           = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__status"               ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedSelf_BuildingDescriptions  = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__building_description" ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedSelf_Versions              = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__version"              ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_AssessorIds          = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__assessor"            ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_ClientIds            = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__client"              ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_StatusCodes          = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__status"              ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_BuildingDescriptions = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__building_description").FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_Versions             = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__version"             ).FirstOrDefault()?.SelectionsJson ?? "[]");
            query = query.Where(a =>
                (_canViewOwnJobs && a.AssessorUserId == _currentUserId && (
                        (assignedSelf_ClientIds          .Count() == 0 || assignedSelf_ClientIds            .FirstOrDefault() == "All" || assignedSelf_ClientIds           .Contains(a.ClientId.ToString()))
                    && (assignedSelf_StatusCodes         .Count() == 0 || assignedSelf_StatusCodes          .FirstOrDefault() == "All" || assignedSelf_StatusCodes         .Contains(a.AssessmentStatusCode.ToString()))
                    && (assignedSelf_BuildingDescriptions.Count() == 0 || assignedSelf_BuildingDescriptions .FirstOrDefault() == "All" || assignedSelf_BuildingDescriptions.Contains(a.ProjectDescriptionCode.ToString()))
                    && (assignedSelf_Versions            .Count() == 0 || assignedSelf_Versions             .FirstOrDefault() == "All" || assignedSelf_Versions            .Contains(a.AssessmentVersion.ToString()))
                ))
                ||
                (_canViewOtherJobs && a.AssessorUserId != _currentUserId && (
                        (assignedOther_AssessorIds        .Count() == 0 || assignedOther_AssessorIds          .FirstOrDefault() == "All" || assignedOther_AssessorIds         .Contains(a.AssessorUserId.ToString()))
                    && (assignedOther_ClientIds           .Count() == 0 || assignedOther_ClientIds            .FirstOrDefault() == "All" || assignedOther_ClientIds           .Contains(a.ClientId.ToString()))
                    && (assignedOther_StatusCodes         .Count() == 0 || assignedOther_StatusCodes          .FirstOrDefault() == "All" || assignedOther_StatusCodes         .Contains(a.AssessmentStatusCode.ToString()))
                    && (assignedOther_BuildingDescriptions.Count() == 0 || assignedOther_BuildingDescriptions .FirstOrDefault() == "All" || assignedOther_BuildingDescriptions.Contains(a.ProjectDescriptionCode.ToString()))
                    && (assignedOther_Versions            .Count() == 0 || assignedOther_Versions             .FirstOrDefault() == "All" || assignedOther_Versions            .Contains(a.AssessmentVersion.ToString()))
                ))
            );

            return query;
        }

        // Main SQL filters
        private List<FieldSqlCondition> GetSqlConditions(bool activeJobs) {
            // If user has no permissions, return a condition that will always be false
            if (!_canViewOwnJobs && !_canViewOtherJobs)
                return new List<FieldSqlCondition> { new FieldSqlCondition { fieldName = "jobId", sql = "1 = 0" } };

            List<FieldSqlCondition> sqlConditions = new List<FieldSqlCondition> {
                new FieldSqlCondition { fieldName = "assessmentDeleted", sql = "[assessmentDeleted] = 0" },
                new FieldSqlCondition { fieldName = "jobDeleted",        sql = "[jobDeleted] = 0" }
            };
            if (activeJobs) {
                sqlConditions.Add(new FieldSqlCondition { fieldName = "statusCode", sql = $"[statusCode] NOT IN ('{STATUS_CANCELLED}', '{STATUS_ISSUED}')" });
            }

            // Permissions
            List<string> assignedSelf_ClientIds             = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__client"               ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedSelf_StatusCodes           = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__status"               ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedSelf_BuildingDescriptions  = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__building_description" ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedSelf_Versions              = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__version"              ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_AssessorIds          = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__assessor"            ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_ClientIds            = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__client"              ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_StatusCodes          = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__status"              ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_BuildingDescriptions = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__building_description").FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_Versions             = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__version"             ).FirstOrDefault()?.SelectionsJson ?? "[]");

            // Build the permission condition similar to InnerGet() method
            // This creates a complex SQL condition that matches the LINQ expression in InnerGet()
            List<string> permissionConditions = new List<string>();

            // Own jobs permission condition
            if (_canViewOwnJobs)
            {
                List<string> ownJobConditions = new List<string>();
                ownJobConditions.Add("[assessorUserId] = @currentUserId");

                if (assignedSelf_ClientIds.Count > 0 && assignedSelf_ClientIds[0] != "All")
                    ownJobConditions.Add($"[clientId] IN ({string.Join(",", assignedSelf_ClientIds.Select(x => $"'{x}'"))})");

                if (assignedSelf_StatusCodes.Count > 0 && assignedSelf_StatusCodes[0] != "All")
                    ownJobConditions.Add($"[assessmentStatusCode] IN ({string.Join(",", assignedSelf_StatusCodes.Select(x => $"'{x}'"))})");

                if (assignedSelf_BuildingDescriptions.Count > 0 && assignedSelf_BuildingDescriptions[0] != "All")
                    ownJobConditions.Add($"[projectDescriptionCode] IN ({string.Join(",", assignedSelf_BuildingDescriptions.Select(x => $"'{x}'"))})");

                if (assignedSelf_Versions.Count > 0 && assignedSelf_Versions[0] != "All")
                    ownJobConditions.Add($"[assessmentVersion] IN ({string.Join(",", assignedSelf_Versions.Select(x => $"'{x}'"))})");

                // If there are additional conditions beyond assessorUserId, we need to combine them with AND
                if (ownJobConditions.Count > 1)
                {
                    permissionConditions.Add($"([assessorUserId] = @currentUserId AND ({string.Join(" OR ", ownJobConditions.Skip(1))}))");
                }
                else
                {
                    permissionConditions.Add("[assessorUserId] = @currentUserId");
                }
            }

            // Other jobs permission condition
            if (_canViewOtherJobs)
            {
                List<string> otherJobConditions = new List<string>();
                otherJobConditions.Add("([assessorUserId] <> @currentUserId OR [assessorUserId] IS NULL)");

                if (assignedOther_AssessorIds.Count > 0 && assignedOther_AssessorIds[0] != "All")
                    otherJobConditions.Add($"[assessorUserId] IN ({string.Join(",", assignedOther_AssessorIds.Select(x => $"'{x}'"))})");

                if (assignedOther_ClientIds.Count > 0 && assignedOther_ClientIds[0] != "All")
                    otherJobConditions.Add($"[clientId] IN ({string.Join(",", assignedOther_ClientIds.Select(x => $"'{x}'"))})");

                if (assignedOther_StatusCodes.Count > 0 && assignedOther_StatusCodes[0] != "All")
                    otherJobConditions.Add($"[assessmentStatusCode] IN ({string.Join(",", assignedOther_StatusCodes.Select(x => $"'{x}'"))})");

                if (assignedOther_BuildingDescriptions.Count > 0 && assignedOther_BuildingDescriptions[0] != "All")
                    otherJobConditions.Add($"[projectDescriptionCode] IN ({string.Join(",", assignedOther_BuildingDescriptions.Select(x => $"'{x}'"))})");

                if (assignedOther_Versions.Count > 0 && assignedOther_Versions[0] != "All")
                    otherJobConditions.Add($"[assessmentVersion] IN ({string.Join(",", assignedOther_Versions.Select(x => $"'{x}'"))})");

                // If there are additional conditions beyond assessorUserId, we need to combine them with AND
                if (otherJobConditions.Count > 1)
                {
                    permissionConditions.Add($"(([assessorUserId] <> @currentUserId OR [assessorUserId] IS NULL) AND ({string.Join(" OR ", otherJobConditions.Skip(1))}))");
                }
                else
                {
                    permissionConditions.Add("([assessorUserId] <> @currentUserId OR [assessorUserId] IS NULL)");
                }
            }

            // Add the combined permission condition to the SQL conditions
            if (permissionConditions.Count > 0)
            {
                sqlConditions.Add(new FieldSqlCondition {
                    fieldName = "permissions",
                    sql = $"({string.Join(" OR ", permissionConditions)})"
                });
            }

            return sqlConditions;
        }

        private string GetSqlConditionsString(bool activeJobs) {
            return string.Join(" AND ", GetSqlConditions(activeJobs).Select(c => c.sql));
        }

        // Helper method to generate a cache key based on the applied filters
        private string GenerateFilterCacheKey(FilterDataDto filterData, bool activeJobs)
        {
            string baseKey = activeJobs ? "ActiveJobsFilterCountData" : "AllJobsFilterCountData";

            // If no filters are applied, return the base key
            if (filterData == null || filterData.appliedFilters == null)
                return baseKey;

            // Create a hash of the applied filters
            StringBuilder keyBuilder = new StringBuilder(baseKey);
            keyBuilder.Append("_");

            // Add user permissions to the key
            keyBuilder.Append(_canViewOwnJobs ? "1" : "0");
            keyBuilder.Append(_canViewOtherJobs ? "1" : "0");
            keyBuilder.Append("_");

            // Add user ID to the key to ensure each user has their own cache
            keyBuilder.Append(UtilityFunctions.UserID.GetHashCode().ToString("X"));
            keyBuilder.Append("_");

            // Add selection data hash to the key to ensure cache is invalidated when user's selection data changes
            string selectionsJson = JsonConvert.SerializeObject(_selectionsDataList);
            keyBuilder.Append(selectionsJson.GetHashCode().ToString("X"));
            keyBuilder.Append("_");

            // Add a hash of the applied filters
            string filtersJson = JsonConvert.SerializeObject(filterData.appliedFilters);
            keyBuilder.Append(filtersJson.GetHashCode().ToString("X"));

            // Add search filter if present
            if (filterData.paging != null && filterData.paging.Filter != null && filterData.paging.Filter.Count > 0)
            {
                string searchJson = JsonConvert.SerializeObject(filterData.paging.Filter);
                keyBuilder.Append("_S");
                keyBuilder.Append(searchJson.GetHashCode().ToString("X"));
            }

            return keyBuilder.ToString();
        }


        // - --- - //
        // - GET - //
        // - --- - //

		/// <summary>
		/// Return a single record based on its Id
		/// </summary>
        public IHttpActionResult Get([FromUri]Guid jobId)
        {
            // TODO-341: Restrict by 'assessment assigned to' value? -- leaving as-is for the moment.
            var job = GetById(jobId);

            return Ok(job);
        }

		/// <summary>
		/// Return a list
		/// </summary>
        public IHttpActionResult Get(
            string fromDate = null,
            string toDate = null,
            bool? isDeleted = false,
            [FromUri] PagingParameters paging = null)
        {
            var query = InnerGet(fromDate, toDate, null, null, null, isDeleted, paging);

            if (query == null)
                return Ok(_defaultAssProjDetEmpty);

            var results = query.ProjectTo<JobAssessmentProjectDetailDto>(_mapper.ConfigurationProvider)
                               .ToDataSourceResult(paging.PageSize, paging.Skip, paging.Sort ?? new List<string>() { "JobModifiedOn@@desc" }, paging.Filter, paging.Aggregate, paging.Export);

            return Ok(results);
        }

        [HttpPost]
        public IHttpActionResult GetMultiFiltered([FromBody] FilterDataDto filterData)
        {
            var query = InnerGet(null, null, null, null, null, null, filterData.paging);

            if (query == null)
                return Ok(_defaultAssProjDetEmpty);

            if (filterData.appliedFilters != null)
                query = AlterQueryFromMultiFilters(query, filterData);

            var results = query
                    .ProjectTo<JobAssessmentProjectDetailDto>(_mapper.ConfigurationProvider)
                    .ToDataSourceResult(filterData.paging.PageSize, filterData.paging.Skip, filterData.paging.Sort ?? new List<string>() { "JobModifiedOn@@desc" }, filterData.paging.Filter, filterData.paging.Aggregate, filterData.paging.Export);

            return Ok(results);
        }

        public IHttpActionResult GetAvailableCodesForFilter(string fromDate = null,
            string toDate = null,
            string statusCode = null,
            string clientCode = null,
            string assessorCode = null,
            bool? isDeleted = false)
        {
            var query = InnerGet(fromDate, toDate, statusCode, clientCode, assessorCode, isDeleted);

            var availableStatusCodes = query.Select(x => x.StatusCode).Distinct().ToArray();
            var availableClients = query.Select(x => x.ClientName).Distinct().ToArray();
            var availableAssessors = query.Select(x => x.AssessorFullName).Distinct().ToArray();

            var available = new
            {
                availableStatusCodes,
                availableClients,
                availableAssessors
            };

            return Ok(available);
        }

        /// <summary>
        /// Return a list
        /// </summary>
        public IHttpActionResult GetByClient([FromUri] Guid clientId, string fromDate = null, string toDate = null, string statusCode = null, bool? isDeleted = false, [FromUri] PagingParameters paging = null)
        {
            if (!_canViewOwnJobs && !_canViewOtherJobs)
                return Ok(_defaultAssProjDetEmpty);

            // TODO-341: Restrict by 'assessment assigned to' value ?
            DateTime dtFromDate = DateTime.MinValue;
            DateTime dtToDate = DateTime.MaxValue;
            if (fromDate != null)
                dtFromDate = DateTime.Parse(fromDate, null, DateTimeStyles.RoundtripKind).ToLocalTime();

            if (toDate != null)
                dtToDate = DateTime.Parse(toDate, null, DateTimeStyles.RoundtripKind).ToLocalTime();

            paging.SetDefaultSort("JobModifiedOn", "Desc");
            _unitOfWork.ReadOnly();

            var query = _unitOfWork.Context.RSS_JobAssessmentProjectDetail_View.Where(a => (a.JobModifiedOn >= dtFromDate && a.JobModifiedOn <= dtToDate) && a.ClientId == clientId);
            switch (statusCode)
            {
                case "All":
                    break;
                case "Active":
                    query = query.Where(a => a.JobDeleted == false && _activeStatuses.Contains(a.StatusCode));
                    break;
                case "Deleted":
                    query = query.Where(a => a.JobDeleted == true || a.StatusCode == "JCancelled");
                    break;
                default:
                    query = query.Where(a => a.JobDeleted == false && !String.IsNullOrEmpty(statusCode) ? (a.StatusCode == statusCode) : false);
                    break;
            }

            var username = UtilityFunctions.CurrentUserFullName;
            if (_canViewOtherJobs == false)
                query = query.Where(x => x.AssessorFullName == username);

            // Permissions
            RSS_User dbUser = _unitOfWork.Context.RSS_User.Where(u => u.AspNetUserId == UtilityFunctions.UserID).FirstOrDefault();
            List<string> assignedSelf_ClientIds            = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__client"                         ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedSelf_StatusCodes          = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__status"                         ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedSelf_BuildingDescriptions = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__building_description").FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedSelf_Versions             = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__version"             ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_AssessorIds          = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__assessor"                       ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_ClientIds            = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__client"                         ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_StatusCodes          = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__status"                         ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_BuildingDescriptions = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__building_description").FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_Versions             = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__version"             ).FirstOrDefault()?.SelectionsJson ?? "[]");
            query = query.Where(a =>
                (_canViewOwnJobs && a.AssessorUserId == _currentUserId && (
                        (assignedSelf_ClientIds          .Count() == 0 || assignedSelf_ClientIds            .FirstOrDefault() == "All" || assignedSelf_ClientIds           .Contains(a.ClientId.ToString()))
                    && (assignedSelf_StatusCodes         .Count() == 0 || assignedSelf_StatusCodes          .FirstOrDefault() == "All" || assignedSelf_StatusCodes         .Contains(a.StatusCode.ToString()))
                    && (assignedSelf_BuildingDescriptions.Count() == 0 || assignedSelf_BuildingDescriptions .FirstOrDefault() == "All" || assignedSelf_BuildingDescriptions.Contains(a.ProjectDescriptionCode.ToString()))
                    && (assignedSelf_Versions            .Count() == 0 || assignedSelf_Versions             .FirstOrDefault() == "All" || assignedSelf_Versions            .Contains(a.AssessmentVersion.ToString()))
                ))
                ||
                (_canViewOtherJobs && (a.AssessorUserId != _currentUserId || a.AssessorUserId == null) && (
                        (assignedOther_AssessorIds        .Count() == 0 || assignedOther_AssessorIds          .FirstOrDefault() == "All" || assignedOther_AssessorIds         .Contains(a.AssessorUserId.ToString()))
                    && (assignedOther_ClientIds           .Count() == 0 || assignedOther_ClientIds            .FirstOrDefault() == "All" || assignedOther_ClientIds           .Contains(a.ClientId.ToString()))
                    && (assignedOther_StatusCodes         .Count() == 0 || assignedOther_StatusCodes          .FirstOrDefault() == "All" || assignedOther_StatusCodes         .Contains(a.StatusCode.ToString()))
                    && (assignedOther_BuildingDescriptions.Count() == 0 || assignedOther_BuildingDescriptions .FirstOrDefault() == "All" || assignedOther_BuildingDescriptions.Contains(a.ProjectDescriptionCode.ToString()))
                    && (assignedOther_Versions            .Count() == 0 || assignedOther_Versions             .FirstOrDefault() == "All" || assignedOther_Versions            .Contains(a.AssessmentVersion.ToString()))
                ))
            );

            var result = query.ProjectTo<JobAssessmentProjectDetailDto>(_mapper.ConfigurationProvider)
                    .ToDataSourceResult(paging.PageSize, paging.Skip, paging.Sort, paging.Filter, paging.Aggregate, paging.Export);

            return Ok(result);
        }

        /// <summary>
        /// Return a list
        /// </summary>
        public IHttpActionResult GetBasicByClient([FromUri] Guid clientId)
        {
            _unitOfWork.ReadOnly();
            var query = _unitOfWork.Context.RSS_Job.Where(a => a.ClientId == clientId && a.Deleted == false);
            var result = query.ProjectTo<BasicJobDto>(_mapper.ConfigurationProvider).ToList();
            return Ok(result);
        }

        public IHttpActionResult GetActiveJobs(int pageSize = 100, int pageIndex = 1)
        {
            var query = InnerGetActiveJobs();

            if (query == null)
                return Ok(_defaultAssProjDetEmpty);

            var skip = (pageIndex - 1) * pageSize;
            var result = query
                .ProjectTo<JobAssessmentProjectDetailDto>(_mapper.ConfigurationProvider)
                .ToDataSourceResult(pageSize, skip, new List<string>() { "JobModifiedOn@@desc" }, null, null, null);

            return Ok(result);
        }

        // Optimized version for incremental loading - only gets new records beyond the current count
        public IHttpActionResult GetActiveJobsIncremental(int currentCount = 0, int pageSize = 100)
        {
            var query = InnerGetActiveJobs();

            if (query == null)
                return Ok(_defaultAssProjDetEmpty);

            // Skip the records we already have and get the next batch
            var result = query
                .ProjectTo<JobAssessmentProjectDetailDto>(_mapper.ConfigurationProvider)
                .ToDataSourceResult(pageSize, currentCount, new List<string>() { "JobModifiedOn@@desc" }, null, null, null);

            return Ok(result);
        }

        [HttpPost]
        public IHttpActionResult GetActiveJobsMultiFiltered([FromBody] FilterDataDto filterData)
        {
            var query = InnerGetActiveJobs();

            if (query == null)
                return Ok(_defaultAssProjDetEmpty);

            if (filterData.appliedFilters != null)
                query = AlterQueryFromMultiFilters(query, filterData);

            var pageSize = filterData.paging?.PageSize ?? 100;
            var skip = filterData.paging?.Skip ?? 0;

            var result = query
                .ProjectTo<JobAssessmentProjectDetailDto>(_mapper.ConfigurationProvider)
                .ToDataSourceResult(pageSize, skip, filterData.paging.Sort ?? new List<string>() { "JobModifiedOn@@desc" }, filterData.paging.Filter, null, null);

            return Ok(result);
        }

        // Optimized version for incremental loading with filters
        [HttpPost]
        public IHttpActionResult GetActiveJobsMultiFilteredIncremental([FromBody] FilterDataDto filterData)
        {
            var query = InnerGetActiveJobs();

            if (query == null)
                return Ok(_defaultAssProjDetEmpty);

            if (filterData.appliedFilters != null)
                query = AlterQueryFromMultiFilters(query, filterData);

            var pageSize = filterData.paging?.PageSize ?? 100;
            var currentCount = filterData.paging?.CurrentCount ?? 0; // New parameter to track current record count

            var result = query
                .ProjectTo<JobAssessmentProjectDetailDto>(_mapper.ConfigurationProvider)
                .ToDataSourceResult(pageSize, currentCount, filterData.paging.Sort ?? new List<string>() { "JobModifiedOn@@desc" }, filterData.paging.Filter, null, null);

            return Ok(result);
        }

        public IHttpActionResult GetActiveJobsForUser([FromUri]string userName)
        {
            if (UtilityFunctions.CurrentUserName == userName && !_canViewOwnJobs)
                return Ok(_defaultAssProjDetEmpty);
            else if (UtilityFunctions.CurrentUserName != userName && !_canViewOtherJobs)
                return Ok(_defaultAssProjDetEmpty);

            var query = InnerGetActiveJobsForUser(userName);

            if (query == null)
                return Ok(_defaultAssProjDetEmpty);

            var result = query
                .ProjectTo<JobAssessmentProjectDetailDto>(_mapper.ConfigurationProvider)
                .ToDataSourceResult(10000, 0, new List<string>() { "JobModifiedOn@@desc" }, null, null, null);

            return Ok(result);
        }

        private IQueryable<RSS_JobAssessmentProjectDetail_View> InnerGetActiveJobs()
        {
            if (!_canViewOtherJobs)
                return InnerGetActiveJobsForUser(UtilityFunctions.CurrentUserName);

            _unitOfWork.ReadOnly();

            var query = _unitOfWork.Context.RSS_JobAssessmentProjectDetail_View.Where(a =>
                a.JobDeleted == false &&
                a.StatusCode != STATUS_CANCELLED &&
                a.StatusCode != STATUS_ISSUED
            );

            // Permissions
            RSS_User dbUser = _unitOfWork.Context.RSS_User.Where(u => u.AspNetUserId == UtilityFunctions.UserID).FirstOrDefault();
            List<string> assignedSelf_ClientIds             = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__client"               ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedSelf_StatusCodes           = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__status"               ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedSelf_BuildingDescriptions  = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__building_description" ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedSelf_Versions              = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__version"              ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_AssessorIds          = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__assessor"            ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_ClientIds            = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__client"              ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_StatusCodes          = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__status"              ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_BuildingDescriptions = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__building_description").FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_Versions             = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__version"             ).FirstOrDefault()?.SelectionsJson ?? "[]");
            query = query.Where(a =>
                (_canViewOwnJobs && a.AssessorUserId == _currentUserId && (
                        (assignedSelf_ClientIds          .Count() == 0 || assignedSelf_ClientIds            .FirstOrDefault() == "All" || assignedSelf_ClientIds           .Contains(a.ClientId.ToString()))
                    && (assignedSelf_StatusCodes         .Count() == 0 || assignedSelf_StatusCodes          .FirstOrDefault() == "All" || assignedSelf_StatusCodes         .Contains(a.AssessmentStatusCode.ToString()))
                    && (assignedSelf_BuildingDescriptions.Count() == 0 || assignedSelf_BuildingDescriptions .FirstOrDefault() == "All" || assignedSelf_BuildingDescriptions.Contains(a.ProjectDescriptionCode.ToString()))
                    && (assignedSelf_Versions            .Count() == 0 || assignedSelf_Versions             .FirstOrDefault() == "All" || assignedSelf_Versions            .Contains(a.AssessmentVersion.ToString()))
                ))
                ||
                (_canViewOtherJobs && (a.AssessorUserId != _currentUserId || a.AssessorUserId == null) && (
                        (assignedOther_AssessorIds        .Count() == 0 || assignedOther_AssessorIds          .FirstOrDefault() == "All" || assignedOther_AssessorIds         .Contains(a.AssessorUserId.ToString()))
                    && (assignedOther_ClientIds           .Count() == 0 || assignedOther_ClientIds            .FirstOrDefault() == "All" || assignedOther_ClientIds           .Contains(a.ClientId.ToString()))
                    && (assignedOther_StatusCodes         .Count() == 0 || assignedOther_StatusCodes          .FirstOrDefault() == "All" || assignedOther_StatusCodes         .Contains(a.AssessmentStatusCode.ToString()))
                    && (assignedOther_BuildingDescriptions.Count() == 0 || assignedOther_BuildingDescriptions .FirstOrDefault() == "All" || assignedOther_BuildingDescriptions.Contains(a.ProjectDescriptionCode.ToString()))
                    && (assignedOther_Versions            .Count() == 0 || assignedOther_Versions             .FirstOrDefault() == "All" || assignedOther_Versions            .Contains(a.AssessmentVersion.ToString()))
                ))
            );

            return query;
        }

        private IQueryable<RSS_JobAssessmentProjectDetail_View> InnerGetActiveJobsForUser(string userName)
        {
            if (UtilityFunctions.CurrentUserName == userName && !_canViewOwnJobs)
                return null;
            else if(UtilityFunctions.CurrentUserName != userName && !_canViewOtherJobs)
                return null;

            _unitOfWork.ReadOnly();

            if (string.IsNullOrEmpty(userName)) { throw new Exception("Error getting active jobs for employee - username is null or empty."); }
            userName = userName.ToLower();
            AspNetUser aspNetUser = _unitOfWork.Context.AspNetUsers.Where(s => s.LoweredUserName == userName).FirstOrDefault();
            if (aspNetUser == null) { throw new Exception("Error getting active jobs for employee - could not find user to match username '" + userName + "'."); }

            RSS_User assessor = _unitOfWork.Context.RSS_User.Where(s => s.AspNetUserId == aspNetUser.Id).FirstOrDefault();

            // Users are not necessarily assessors. So if we can't find an assessor, it's not an error....
            if (assessor == null)
                return null;

            Guid assessorId = assessor.UserId;
            var query = _unitOfWork.Context.RSS_JobAssessmentProjectDetail_View.Where(a =>
                a.JobDeleted == false
                && a.StatusCode != STATUS_CANCELLED
                && a.StatusCode != STATUS_ISSUED
            );

            // Permissions
            RSS_User dbUser = _unitOfWork.Context.RSS_User.Where(u => u.AspNetUserId == UtilityFunctions.UserID).FirstOrDefault();
            List<string> assignedSelf_ClientIds             = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__client"               ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedSelf_StatusCodes           = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__status"               ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedSelf_BuildingDescriptions  = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__building_description" ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedSelf_Versions              = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedself__version"              ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_AssessorIds          = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__assessor"            ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_ClientIds            = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__client"              ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_StatusCodes          = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__status"              ).FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_BuildingDescriptions = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__building_description").FirstOrDefault()?.SelectionsJson ?? "[]");
            List<string> assignedOther_Versions             = JsonConvert.DeserializeObject<List<string>>(_selectionsDataList.Where(d => d.RoleName == "home_page_/_job_page__assignedother__version"             ).FirstOrDefault()?.SelectionsJson ?? "[]");
            query = query.Where(a =>
                (_canViewOwnJobs && a.AssessorUserId == _currentUserId && (
                        (assignedSelf_ClientIds          .Count() == 0 || assignedSelf_ClientIds            .FirstOrDefault() == "All" || assignedSelf_ClientIds           .Contains(a.ClientId.ToString()))
                    && (assignedSelf_StatusCodes         .Count() == 0 || assignedSelf_StatusCodes          .FirstOrDefault() == "All" || assignedSelf_StatusCodes         .Contains(a.AssessmentStatusCode.ToString()))
                    && (assignedSelf_BuildingDescriptions.Count() == 0 || assignedSelf_BuildingDescriptions .FirstOrDefault() == "All" || assignedSelf_BuildingDescriptions.Contains(a.ProjectDescriptionCode.ToString()))
                    && (assignedSelf_Versions            .Count() == 0 || assignedSelf_Versions             .FirstOrDefault() == "All" || assignedSelf_Versions            .Contains(a.AssessmentVersion.ToString()))
                ))
                ||
                (_canViewOtherJobs && (a.AssessorUserId != _currentUserId || a.AssessorUserId == null) && (
                        (assignedOther_AssessorIds        .Count() == 0 || assignedOther_AssessorIds         .FirstOrDefault() == "All" || assignedOther_AssessorIds         .Contains(a.AssessorUserId.ToString()))
                    && (assignedOther_ClientIds           .Count() == 0 || assignedOther_ClientIds           .FirstOrDefault() == "All" || assignedOther_ClientIds           .Contains(a.ClientId.ToString()))
                    && (assignedOther_StatusCodes         .Count() == 0 || assignedOther_StatusCodes         .FirstOrDefault() == "All" || assignedOther_StatusCodes         .Contains(a.AssessmentStatusCode.ToString()))
                    && (assignedOther_BuildingDescriptions.Count() == 0 || assignedOther_BuildingDescriptions.FirstOrDefault() == "All" || assignedOther_BuildingDescriptions.Contains(a.ProjectDescriptionCode.ToString()))
                    && (assignedOther_Versions            .Count() == 0 || assignedOther_Versions            .FirstOrDefault() == "All" || assignedOther_Versions            .Contains(a.AssessmentVersion.ToString()))
                ))
            );

            return query;
        }

        public IHttpActionResult GetAllAssessors()
        {
            var result = _unitOfWork.Context.RSS_Assessment.Where(a => a.RSS_User_Assessor != null && a.Deleted == false)
                                                           .Select(a => new
                                                           {
                                                               a.RSS_User_Assessor.UserId,
                                                               a.RSS_User_Assessor.FullName
                                                           })
                                                           .Distinct()
                                                           .ToList();
            return Ok(result);
        }

        public IHttpActionResult GetAllVersions()
        {
            var result = _unitOfWork.Context.RSS_AssessmentProjectDetail.Where(a => a.Deleted == false).Select(a => new { value = a.AssessmentVersion.ToString() }).OrderBy(v => v.value).Distinct().ToList();
            return Ok(result);
        }


        // - ------ - //
        // - CREATE - //
        // - ------ - //

        [Authorize(Roles="assessment_page_(tabs/sub-tabs)__assessment__edit")]
        [HttpPost]
        public async Task<IHttpActionResult> Create([FromBody]ExtendedJobDto jobDto)
        {
            try
            {
                Job jobContext = _jobFactory();

                jobContext.Create(jobDto);

                _unitOfWork.Commit();

                // Note: For new job + recert we checked notifications + send immediately, without adding to queue.
                await _notificationFramework.CheckAgainstNotificationRules(jobDto.CurrentAssessment, null, jobDto.CurrentAssessment.StatusCode);

                // Make sure next Jobs list retrievals bypass cache so can get any updated values
                SystemParameter.UpdateIntParm("BypassFilterCountDataCacheAdmin",     1);
                SystemParameter.UpdateIntParm("BypassMultiFilterOptionsCacheAdmin",  1);
                SystemParameter.UpdateIntParm("BypassFilterCountDataCacheClient",    1);
                SystemParameter.UpdateIntParm("BypassMultiFilterOptionsCacheClient", 1);

                // Return the created assessment id
                return Ok(jobDto.CurrentAssessment.AssessmentId);
            }
            catch(Exception ex)
            {
                throw ex;
            }

        }


        // - ------ - //
        // - UPDATE - //
        // - ------ - //

        [Authorize(Roles="assessment_actions__editassessment")]
        [HttpPost]
        public IHttpActionResult Update([FromBody]ExtendedJobDto jobDto)
        {
			Guid jobIdKey = jobDto.JobId;
            Job job = _jobFactory().Get(jobIdKey);
            job.Update(jobDto);

            // Make sure next Jobs list retrievals bypass cache so can get any updated values
            SystemParameter.UpdateIntParm("BypassFilterCountDataCacheAdmin",     1);
            SystemParameter.UpdateIntParm("BypassMultiFilterOptionsCacheAdmin",  1);
            SystemParameter.UpdateIntParm("BypassFilterCountDataCacheClient",    1);
            SystemParameter.UpdateIntParm("BypassMultiFilterOptionsCacheClient", 1);

			_unitOfWork.Commit();

			// Return the updated model as dto.
			return Ok(GetById(jobIdKey));
		}


        // - ------ - //
        // - DELETE - //
        // - ------ - //

		[Authorize(Roles="assessment_actions__deleteassessment")]
        [HttpPost]
        public IHttpActionResult Delete([FromUri]Guid jobId)
        {
            Job job = _jobFactory().Get(jobId);
            job.Delete();

			_unitOfWork.Commit();

			// Return the deleted model as dto.
            return Ok(GetById(jobId));
        }

		[Authorize(Roles="assessment_actions__deleteassessment")]
        [HttpPost]
        public IHttpActionResult UndoDelete([FromUri]Guid jobId)
        {
            Job job = _jobFactory().Get(jobId);
            job.UndoDelete();

			_unitOfWork.Commit();

			// Return the un-deleted model as dto.
            return Ok(GetById(jobId));
        }


        // - ----- - //
        // - OTHER - //
        // - ----- - //

        [Authorize(Roles="assessment_actions__cancelassessment")]
        [HttpPost]
        public IHttpActionResult Cancel([FromUri]Guid jobId)
        {
            Job job = _jobFactory().Get(jobId);
            string code = job.Cancel();

            _unitOfWork.Commit();

            // Return the un-deleted model as dto.
            return Ok(code);
        }

        [Authorize(Roles="assessment_actions__reinstateassessment")]
        [HttpPost]
        public IHttpActionResult Reinstate([FromUri] Guid jobId)
        {
            Job job = _jobFactory().Get(jobId);
            string code = job.Reinstate();

            _unitOfWork.Commit();

            // Return the un-deleted model as dto.
            return Ok(code);
        }

        private ExtendedJobDto GetById(Guid jobId)
		{
            return _jobFactory().Get(jobId).ExtendedDto;
		}

        [Authorize(Roles="assessment_actions__copyassessment")]
        [HttpPost]
        public IHttpActionResult Copy([FromBody]ExtendedJobDto jobDto)
        {
            // NOTE: Now the front end must supply data (via jobDto) exactly
            // as they want it saved in the DB (Sans GUID's which may be re-assigned)...?
            try
            {
                Guid copiedAssessmentId = _jobFactory().Copy(jobDto);
                return Ok(copiedAssessmentId);
            }
            catch(Exception)
            {
                throw;
            }
        }


        // - ------------- - //
        // - MULTI-FILTERS - //
        // - ------------- - //

        [HttpPost]
        public async Task<IHttpActionResult> GetMultiFilterOptions(bool activeJobs, [FromBody] List<MultiFilterFieldDto> fieldsList)
        {
            // Generate a cache key based on the fields list
            string fieldsHash = string.Join(",", fieldsList.Select(f => f.field)).GetHashCode().ToString("X");
            string optionsKeySelectionsHash = JsonConvert.SerializeObject(_selectionsDataList).GetHashCode().ToString("X");
            string cacheKey = $"{(activeJobs ? "ActiveJobsMultiFilterOptions" : "AllJobsMultiFilterOptions")}_{fieldsHash}_{_canViewOwnJobs}_{_canViewOtherJobs}_{UtilityFunctions.UserID.GetHashCode().ToString("X")}_{optionsKeySelectionsHash}";

            // Filter data to be returned (first try get from cache)
            bool bypasseCache = Convert.ToBoolean(SystemParameter.GetIntParm("BypassMultiFilterOptionsCacheAdmin", true));

            // Declare filterOptions variable
            Dictionary<string, List<FilterOption>> filterOptions = null;

            if (!bypasseCache)
            {
                // Try to get from the specific cache key first
                filterOptions =
                    (Dictionary<string, List<FilterOption>>)CacheHandler.LookupCache(
                        CacheHandler.DataType_MultiFilterOptions,
                        cacheKey);

                if (filterOptions != null)
                {
                    return Ok(filterOptions);
                }

                // Fall back to the general cache
                filterOptions =
                    (Dictionary<string, List<FilterOption>>)CacheHandler.LookupCache(
                        CacheHandler.DataType_MultiFilterOptions,
                        activeJobs ? "ActiveJobsMultiFilterOptions" : "AllJobsMultiFilterOptions");

                Dictionary<string, object> permissionsCache =
                    (Dictionary<string, object>)CacheHandler.LookupCache(
                        CacheHandler.DataType_MultiFilterOptions_Permissions,
                        "AllJobsMultiFilterOptions_Permissions");

                string cachedSelectionsHash = (string)CacheHandler.LookupCache(
                    CacheHandler.DataType_MultiFilterOptions_Permissions,
                    "AllJobsMultiFilterOptions_SelectionsHash");

                string currentMultiFilterSelectionsHash = JsonConvert.SerializeObject(_selectionsDataList).GetHashCode().ToString("X");

                if (
                       filterOptions != null
                    && permissionsCache != null
                    && permissionsCache["canViewOwnJobs"].Equals(_canViewOwnJobs)
                    && permissionsCache["canViewOtherJobs"].Equals(_canViewOtherJobs)
                    && cachedSelectionsHash == currentMultiFilterSelectionsHash
                    && (string)permissionsCache["userId"] == UtilityFunctions.UserID
                )
                {
                    return Ok(filterOptions);
                }
            }

            // IF not found in cache, get from using SQL then set to cache for default 30 mins
            filterOptions = new Dictionary<string, List<FilterOption>>();
            // Get unique values for each field
            // Returns a single row, with a column for each field, each with a value of a comma-separated string of all unique values for that field
            string sql = $@"
                SELECT
                    {string.Join(",", fieldsList.Select((field => {
                        // Handle special cases
                        string sqlFieldFormatted = $"[{field.field}]";
                        if (field.field == "assessmentProjectDetailLGA")
                        {
                            sqlFieldFormatted = @"(CASE WHEN [AssessmentProjectDetailLGAShort] IS NOT NULL
                                                    THEN [AssessmentProjectDetailLGAShort]
                                                    ELSE REPLACE(REPLACE(REPLACE([AssessmentProjectDetailLGA], 'city of ', ''), 'shire of ', ''), 'town of ', '')
                                                    END) [assessmentProjectDetailLGA]";
                        }
                        // SQL
                        return $@"
                        (
                            SELECT STRING_AGG(
                                CAST(
                                    (CASE WHEN CAST([{field.field}] AS NVARCHAR(MAX)) IS NULL
                                        THEN 'Not Specified'
                                        ELSE CAST([{field.field}] AS NVARCHAR(MAX))
                                        END)
                                    AS NVARCHAR(MAX)
                                ),
                                ','
                            ) FROM (
                                SELECT DISTINCT {sqlFieldFormatted} FROM [dbo].[RSS_JobAssessmentProjectDetail_View]
                                WHERE {GetSqlConditionsString(activeJobs)}
                                ORDER BY [{field.field}] OFFSET 0 ROWS
                            ) [uniqueResults]
                        ) {field.field}";
                    })
                    ))}
            ";
            var cmd = new SqlCommand(sql, _connection);
            cmd.Parameters.AddWithValue("currentUserId", _currentUserId);

            try
            {
                using (var sqlReader = await cmd.ExecuteReaderAsync())
                {
                    sqlReader.Read();
                    // Add each field result to the "filterOptions" dictionary, also with "Any" added
                    foreach (MultiFilterFieldDto field in fieldsList)
                    {
                        try
                        {
                            string thisResult = sqlReader[field.field] as string;
                            filterOptions.Add(
                                field.field,
                                (new List<FilterOption>() { new FilterOption { name = "Any", value = "Any" } }).Concat(
                                    (thisResult as string)?.Split(',').Select(v => new FilterOption { name = v, value = v })
                                ).ToList()
                            );

                        }
                        catch (Exception)
                        {
                            filterOptions.Add(
                                field.field,
                                new List<FilterOption>() { new FilterOption { name = "Any", value = "Any" } }
                            );
                        }
                    }
                    sqlReader.Close();
                }
                // Save results to both specific and general cache
                CacheHandler.SaveToCache(CacheHandler.DataType_MultiFilterOptions, cacheKey, filterOptions);
                CacheHandler.SaveToCache(CacheHandler.DataType_MultiFilterOptions, activeJobs ? "ActiveJobsMultiFilterOptions" : "AllJobsMultiFilterOptions", filterOptions);
                CacheHandler.SaveToCache(CacheHandler.DataType_MultiFilterOptions_Permissions, "AllJobsMultiFilterOptions_Permissions", new Dictionary<string, object>() {
                        { "canViewOwnJobs", _canViewOwnJobs },
                        { "canViewOtherJobs", _canViewOtherJobs },
                        { "userId", UtilityFunctions.UserID }
                });

                string optionsSelectionsHash = JsonConvert.SerializeObject(_selectionsDataList).GetHashCode().ToString("X");
                CacheHandler.SaveToCache(CacheHandler.DataType_MultiFilterOptions_Permissions, "AllJobsMultiFilterOptions_SelectionsHash", optionsSelectionsHash);

                // Make sure next call uses cache
                if (bypasseCache)
                {
                    SystemParameter.UpdateIntParm("BypassMultiFilterOptionsCacheAdmin", 0);
                }
                return Ok(filterOptions);
            }
            catch (Exception e)
            {
                throw e;
            }

        }

        [HttpPost]
        public async Task<IHttpActionResult> GetFilterCountData(bool activeJobs, [FromBody] FilterDataDto filterData)
        {
            if (!_canViewOwnJobs && !_canViewOtherJobs)
                return Ok(new Dictionary<string, Dictionary<string, int>>());

            // Generate a cache key based on the applied filters
            string cacheKey = GenerateFilterCacheKey(filterData, activeJobs);

            // Current filters applied
            bool anyFiltersApplied = false;
            Dictionary<string, List<string>> currentFilters = new Dictionary<string, List<string>>();
            foreach (MultiFilterFieldDto field in filterData.fields.Where(f => !f.isDate))
            {
                List<string> fieldSelections = JsonConvert.DeserializeObject<List<string>>(filterData.appliedFilters[field.field].ToString());
                bool hasFilter = fieldSelections.Where(s => s.ToString()?.ToLower() == "any").Count() == 0;
                currentFilters[field.field] = hasFilter ? fieldSelections : null;
                if (!anyFiltersApplied && hasFilter) { anyFiltersApplied = true; }
            }
            // Current date filters applied
            Dictionary<string, DateFilterDto> currentDateFilters = new Dictionary<string, DateFilterDto>();
            foreach (MultiFilterFieldDto field in filterData.fields.Where(f => f.isDate))
            {
                DateFilterDto dateFilter = JsonConvert.DeserializeObject<DateFilterDto>(filterData.appliedFilters[field.field].ToString());
                bool hasFilter = dateFilter != null && dateFilter.label.ToLower() != "all time";
                currentDateFilters[field.field] = hasFilter ? dateFilter : null;
                if (!anyFiltersApplied && hasFilter) { anyFiltersApplied = true; }
            }

            // Check if we can use the cache
            bool bypasseCache = Convert.ToBoolean(SystemParameter.GetIntParm("BypassFilterCountDataCacheAdmin", true));

            // Try to get from cache first, even if filters are applied (using the filter-specific cache key)
            if (!bypasseCache)
            {
                // Try to get from the filter-specific cache
                Dictionary<string, Dictionary<string, int>> cacheFilterCountData =
                    (Dictionary<string, Dictionary<string, int>>)CacheHandler.LookupCache(
                        CacheHandler.DataType_MultiFilterCountData,
                        cacheKey);

                if (cacheFilterCountData != null)
                {
                    return Ok(cacheFilterCountData);
                }

                // If no filters are applied, try the general cache
                if (!anyFiltersApplied)
                {
                    cacheFilterCountData =
                        (Dictionary<string, Dictionary<string, int>>)CacheHandler.LookupCache(
                            CacheHandler.DataType_MultiFilterCountData,
                            activeJobs ? "ActiveJobsFilterCountData" : "AllJobsFilterCountData");

                    Dictionary<string, object> permissionsCache =
                        (Dictionary<string, object>)CacheHandler.LookupCache(
                            CacheHandler.DataType_MultiFilterCountData_Permissions,
                            "AllJobsFilterCountData_Permissions");

                    string cachedSelectionsHash = (string)CacheHandler.LookupCache(
                        CacheHandler.DataType_MultiFilterCountData_Permissions,
                        "AllJobsFilterCountData_SelectionsHash");

                    string currentOptionsSelectionsHash = JsonConvert.SerializeObject(_selectionsDataList).GetHashCode().ToString("X");

                    if (
                           cacheFilterCountData != null
                        && permissionsCache != null
                        && permissionsCache["canViewOwnJobs"].Equals(_canViewOwnJobs)
                        && permissionsCache["canViewOtherJobs"].Equals(_canViewOtherJobs)
                        && cachedSelectionsHash == currentOptionsSelectionsHash
                        && (string)permissionsCache["userId"] == UtilityFunctions.UserID
                    )
                    {
                        return Ok(cacheFilterCountData);
                    }
                }
            }

            // Resulting filter count data
            Dictionary<string, Dictionary<string, int>> filterCountData = new Dictionary<string, Dictionary<string, int>>();

            // Build sql condition for current filters
            List<FieldSqlCondition> currentFiltersSqlConditions = GetSqlConditions(activeJobs);

            // FOR EACH field
            foreach (MultiFilterFieldDto field in filterData.fields)
            {
                string fieldName = field.field;
                string fieldCondition = null;
                string fieldAppliedFilters = filterData.appliedFilters[field.field].ToString();
                // Date
                if (field.isDate)
                {
                    if (currentDateFilters[fieldName] != null)
                    {
                        fieldCondition = $"[{fieldName}] > '{currentDateFilters[fieldName].startDate.ToString("yyyy-MM-dd")}' AND [{fieldName}] < '{currentDateFilters[fieldName].endDate.ToString("yyyy-MM-dd")}'";
                    }
                }
                // Normal
                else
                {
                    if (currentFilters[fieldName] != null && currentFilters[fieldName]?.Where(s => s.ToString()?.ToLower() != "not specified").Count() > 0)
                    {
                        fieldCondition = $"[{fieldName}] IN ({string.Join(",", currentFilters[fieldName].Where(s => s.ToString()?.ToLower() != "not specified").Select(s => field.isDecimal ? $"{s}" : $"'{s}'"))})"; // Eg. "[clientName] IN ('ARO Homes', 'Best Builders', 'HomeGroup')"
                        if (currentFilters[fieldName].Select(s => s.ToLower()).Contains("not specified"))
                        {
                            fieldCondition = $"({fieldCondition} OR [{fieldName}] IS NULL)"; // Eg. "([clientName] IN ('ARO Homes', 'Best Builders', 'HomeGroup') OR [clientName] IS NULL)"
                        }
                    }
                    else if (currentFilters[fieldName]?.Where(s => s.ToString()?.ToLower() == "not specified").Count() > 0)
                    {
                        fieldCondition = $"[{fieldName}] IS NULL";
                    }
                }
                // Add to current filters list for sql
                if (!string.IsNullOrEmpty(fieldCondition))
                {
                    currentFiltersSqlConditions.Add(new FieldSqlCondition { fieldName = fieldName, sql = fieldCondition });
                }
            }
            // Sql
            string sql = string.Join(
                // Union each field's results
                " UNION ",
                // Generate list of options results for each field
                filterData.fields.Where(f => !f.isDate).Select(field => {
                    List<FilterOption> options = filterData.filterOptions[field.field].Where(option => option.name.ToLower() != "any" && option.name.ToLower() != "not specified").ToList();
                    List<FieldSqlCondition> conditions = currentFiltersSqlConditions.Where(c => c.fieldName != field.field).ToList();
                    // Handle special cases
                    string sqlFieldFormatted = $"[{field.field}]";
                    if (field.field == "assessmentProjectDetailLGA")
                    {
                        sqlFieldFormatted = @"(CASE WHEN [AssessmentProjectDetailLGAShort] IS NOT NULL
                                                THEN [AssessmentProjectDetailLGAShort]
                                                ELSE REPLACE(REPLACE(REPLACE([AssessmentProjectDetailLGA], 'city of ', ''), 'shire of ', ''), 'town of ', '')
                                                END)";
                    }
                    return $@"
                        {(options.Count() > 0
                            ? $@"
                                SELECT '{field.field}'             [FieldName]
                                        ,CAST([option] AS VARCHAR) [OptionName]
                                        ,(
                                            SELECT COUNT([JobId])
                                            FROM [dbo].[RSS_JobAssessmentProjectDetail_View]
                                            WHERE {(string.Join(" AND ", conditions.Select(c => c.sql)))} AND {sqlFieldFormatted} = [option]
                                        ) [Count]
                                FROM (VALUES {string.Join(",", options.Select(option => field.isDecimal ? $"({option.name})" : $"('{option.name}')"))}) [options] ([option])
                                UNION
                            "
                            : "")}
                        SELECT '{field.field}' [FieldName]
                               ,'Any'          [OptionName]
                               ,COUNT([JobId]) [Count]
                        FROM [dbo].[RSS_JobAssessmentProjectDetail_View]
                        WHERE {(string.Join(" AND ", conditions.Select(c => c.sql)))}
                        UNION
                        SELECT '{field.field}'  [FieldName]
                               ,'Not Specified' [OptionName]
                               ,COUNT([JobId])  [Count]
                        FROM [dbo].[RSS_JobAssessmentProjectDetail_View]
                        WHERE {(string.Join(" AND ", conditions.Select(c => c.sql)))} AND [{field.field}] IS NULL
                    ";
                })
            );

            var cmd = new SqlCommand(sql, _connection);
            cmd.Parameters.AddWithValue("currentUserId", _currentUserId);

            try
            {
                using (var sqlReader = await cmd.ExecuteReaderAsync())
                {
                    // Read each option in each field
                    while (sqlReader.Read())
                    {
                        string fieldName = (string)sqlReader["FieldName"];
                        string optionName = (string)sqlReader["OptionName"];
                        int count = (int)sqlReader["Count"];
                        Dictionary<string, int> fieldCountData = null;
                        if (filterCountData.TryGetValue(fieldName, out fieldCountData))
                            fieldCountData[optionName] = count;
                        else
                            filterCountData[fieldName] = new Dictionary<string, int> { { optionName, count } };
                    }
                    sqlReader.Close();
                }
                // Save to cache with appropriate key
                filterCountData["_permissions"] = new Dictionary<string, int>() {
                      { "canViewOwnJobs", Convert.ToInt32(_canViewOwnJobs) },
                      { "canViewOtherJobs", Convert.ToInt32(_canViewOtherJobs) }
                };

                // Always save to filter-specific cache
                CacheHandler.SaveToCache(CacheHandler.DataType_MultiFilterCountData, cacheKey, filterCountData);

                // If no filters applied, also save to the general cache
                if (!anyFiltersApplied)
                {
                    CacheHandler.SaveToCache(CacheHandler.DataType_MultiFilterCountData, activeJobs ? "ActiveJobsFilterCountData" : "AllJobsFilterCountData", filterCountData);
                    CacheHandler.SaveToCache(CacheHandler.DataType_MultiFilterCountData_Permissions, "AllJobsFilterCountData_Permissions", new Dictionary<string, object>() {
                          { "canViewOwnJobs", _canViewOwnJobs },
                          { "canViewOtherJobs", _canViewOtherJobs },
                          { "userId", UtilityFunctions.UserID }
                    });

                    string countDataSelectionsHash = JsonConvert.SerializeObject(_selectionsDataList).GetHashCode().ToString("X");
                    CacheHandler.SaveToCache(CacheHandler.DataType_MultiFilterCountData_Permissions, "AllJobsFilterCountData_SelectionsHash", countDataSelectionsHash);
                }

                // Make sure next call uses cache
                if (bypasseCache)
                {
                    SystemParameter.UpdateIntParm("BypassFilterCountDataCacheAdmin", 0);
                }
                return Ok(filterCountData);
            }
            catch (Exception e)
            {
                throw e;
            }
        }

        // Adjust query given a set of multi-select filters
        private IQueryable<RSS_JobAssessmentProjectDetail_View> AlterQueryFromMultiFilters(IQueryable<RSS_JobAssessmentProjectDetail_View> query, FilterDataDto filterData)
        {
            IQueryable<RSS_JobAssessmentProjectDetail_View> newQuery = query;

            foreach (MultiFilterFieldDto field in filterData.fields)
            {
                string fieldName = field.field;
                // Date field
                if (field.isDate)
                {
                    DateFilterDto dateFilter = JsonConvert.DeserializeObject<DateFilterDto>(filterData.appliedFilters[fieldName].ToString());
                    if (dateFilter.label != "All Time")
                    {
                        switch (fieldName)
                        {
                            case "jobCreatedOn":
                                newQuery = newQuery.Where(x => x.JobCreatedOn >= dateFilter.startDate && x.JobCreatedOn <= dateFilter.endDate); break;
                            case "assessmentCerficateDate":
                                newQuery = newQuery.Where(x => x.AssessmentCerficateDate >= dateFilter.startDate && x.AssessmentCerficateDate <= dateFilter.endDate); break;
                            case "jobModifiedOn":
                                newQuery = newQuery.Where(x => x.JobModifiedOn >= dateFilter.startDate && x.JobModifiedOn <= dateFilter.endDate); break;
                        };
                    }
                }
                // Normal field
                else
                {
                    List<string> fieldSelections = JsonConvert.DeserializeObject<List<string>>(filterData.appliedFilters[fieldName].ToString()).Select(x => x?.ToLower()).ToList();
                    // IF selections made AND doesn't include 'Any', filter
                    if (fieldSelections.Count() > 0 && fieldSelections.Where(x => x.ToString()?.ToLower() == "any").Count() == 0)
                    {
                         List<decimal> selectionsFormatted = null;
                        switch (fieldName)
                        {
                            case "clientName":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.ClientName.ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.ClientName))); break;
                            case "projectDescriptionDescription":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.ProjectDescriptionDescription.ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.ProjectDescriptionDescription))); break;
                            case "assessmentDesign":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.AssessmentDesign.ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.AssessmentDesign))); break;
                            case "assessmentPriorityDescription":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.AssessmentPriorityDescription.ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.AssessmentPriorityDescription))); break;
                            case "assessmentProjectDetailSuburb":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.AssessmentProjectDetailSuburb.ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.AssessmentProjectDetailSuburb))); break;
                            case "assessmentProjectDetailLGA":
                                newQuery = newQuery.Where(x =>
                                    !string.IsNullOrEmpty(x.AssessmentProjectDetailLGAShort)
                                        ? fieldSelections.Contains(x.AssessmentProjectDetailLGAShort.ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.AssessmentProjectDetailLGAShort))
                                        : fieldSelections.Contains(x.AssessmentProjectDetailLGA.ToLower().Replace("city of ", "").Replace("shire of ", "").Replace("town of ", "")) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.AssessmentProjectDetailLGA))
                                );
                                break;
                            case "jobStatusDescription":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.JobStatusDescription.ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.JobStatusDescription))); break;
                            case "clientAssigneeFullName":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.ClientAssigneeFullName.ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.ClientAssigneeFullName))); break;
                            case "assessorFullName":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.AssessorFullName.ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.AssessorFullName))); break;
                            case "creatorFullName":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.CreatorFullName.ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.CreatorFullName))); break;
                            case "assessmentVersion":
                                selectionsFormatted = fieldSelections.Where(x => x?.ToLower() != "not specified").Select(x => Convert.ToDecimal(x)).ToList();
                                newQuery = newQuery.Where(x => selectionsFormatted.Contains((decimal)x.AssessmentVersion) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.AssessmentVersion.ToString()))); break;
                            case "assessmentNatHERSClimateZone":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.AssessmentNatHERSClimateZone.ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.AssessmentNatHERSClimateZone))); break;
                            case "assessmentNCCClimateZone":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.AssessmentNCCClimateZone.ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.AssessmentNCCClimateZone))); break;
                            case "northOffset":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.NorthOffset.ToString().ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.NorthOffset.ToString()))); break;
                            case "assessmentProjectDetailLotDescription":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.AssessmentProjectDetailLotDescription.ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.AssessmentProjectDetailLotDescription))); break;
                            case "assessmentProjectDetailLotWidth":
                                selectionsFormatted = fieldSelections.Where(x => x?.ToLower() != "not specified").Select(x => Convert.ToDecimal(x)).ToList();
                                newQuery = newQuery.Where(x => selectionsFormatted.Contains((decimal)x.AssessmentProjectDetailLotWidth) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.AssessmentProjectDetailLotWidth.ToString()))); break;
                            case "assessmentProjectDetailLotLength":
                                selectionsFormatted = fieldSelections.Where(x => x?.ToLower() != "not specified").Select(x => Convert.ToDecimal(x)).ToList();
                                newQuery = newQuery.Where(x => selectionsFormatted.Contains((decimal)x.AssessmentProjectDetailLotLength) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.AssessmentProjectDetailLotLength.ToString()))); break;
                            case "assessmentProjectDetailParcelArea":
                                selectionsFormatted = fieldSelections.Where(x => x?.ToLower() != "not specified").Select(x => Convert.ToDecimal(x)).ToList();
                                newQuery = newQuery.Where(x => selectionsFormatted.Contains((decimal)x.AssessmentProjectDetailParcelArea) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.AssessmentProjectDetailParcelArea.ToString()))); break;
                            case "assessmentProjectDetailCornerBlock":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.AssessmentProjectDetailCornerBlock.ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.AssessmentProjectDetailCornerBlock))); break;
                            case "assessmentProjectDetailRearLaneway":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.AssessmentProjectDetailRearLaneway == true ? "1" : x.AssessmentProjectDetailRearLaneway == false ? "0" : null) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.AssessmentProjectDetailRearLaneway.ToString()))); break;
                            case "assessmentProjectDetailRuralLot":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.AssessmentProjectDetailRuralLot == true ? "1" : x.AssessmentProjectDetailRuralLot == false ? "0" : null) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.AssessmentProjectDetailRuralLot.ToString()))); break;
                            case "garageLocation":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.GarageLocation.ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.GarageLocation))); break;
                            case "outdoorLivingLocation":
                                newQuery = newQuery.Where(x => fieldSelections.Contains(x.OutdoorLivingLocation.ToLower()) || (fieldSelections.Contains("not specified") && string.IsNullOrEmpty(x.OutdoorLivingLocation))); break;
                        };
                    }
                }
            }

            return newQuery;
        }
    }

}