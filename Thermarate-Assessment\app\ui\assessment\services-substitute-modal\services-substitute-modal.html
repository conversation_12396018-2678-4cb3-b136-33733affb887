<form class="main-content-wrapper"
      novalidate
      data-ng-controller='servicesSubstituteModalController as vm'
      style="width: auto; min-width: auto; max-width: none;">

    <div class="widget"
         ng-form="servicesSubstituteModalForm"
         ng-cloak>
        <div data-cc-widget-header
             data-title="{{vm.title}}"
             data-is-modal="true"
             data-cancel="vm.cancel()"
             data-back-button>
        </div>
        <div data-cc-widget-content
             data-is-modal="true">

                <!--'Rename' body-->
                <div ng-if="vm.action === 'rename'">

                    <!-- Description (Editable) -->
                    <md-input-container class="md-block" flex="100">
                        <label>Description</label>
                        <input ng-disabled="vm.disabled"
                               ng-required="true"
                               ng-model="vm.data.overrideDisplayDescription" />
                    </md-input-container>

                    <!-- Original Description (Disabled) -->
                    <div style="display: grid; grid-template-columns: 1fr 100px;">
                        <md-input-container class="md-block vertically-condensed" flex="100">
                            <label>Original Description</label>
                            <input ng-disabled="true"
                                   ng-model="vm.data.originalDescription" />
                        </md-input-container>
                        <md-button class="md-raised"
                                   ng-disabled="vm.disabled"
                                   ng-click="vm.data.overrideDisplayDescription = vm.data.originalDescription;">
                            Restore
                        </md-button>
                    </div>

                    <!-- Insulation fields - only show for construction categories -->
                    <div ng-if="vm.isConstructionCategory()">
                        <!-- Insulation (Editable) -->
                        <md-input-container class="md-block" flex="100">
                            <label>Insulation</label>
                            <input ng-disabled="vm.disabled"
                                   ng-model="vm.data.overrideInsulationDescription" />
                        </md-input-container>

                        <!-- Original Insulation (Disabled) -->
                        <div style="display: grid; grid-template-columns: 1fr 100px;">
                            <md-input-container class="md-block vertically-condensed" flex="100">
                                <label>Original Insulation</label>
                                <input ng-disabled="true"
                                       ng-model="vm.data.originalInsulation" />
                            </md-input-container>
                            <md-button class="md-raised"
                                       ng-disabled="vm.disabled"
                                       ng-click="vm.data.overrideInsulationDescription = vm.data.originalInsulation;">
                                Restore
                            </md-button>
                        </div>
                    </div>



                </div>

                <div ng-if="vm.action == 'substitute'">

                    <!-- Services Database Content -->
                    <md-toolbar class="widget-actionbar-wrapper header-container" flex>
                        <div class="md-toolbar-tools widget-actionbar header-inner-container" style="margin-bottom:20px; font-size:12px; background-color:rgb(255,255,255);">
                            <div class="filters-container" style="margin-top: 20px;">
                                <!-- Search -->
                                <div class="filter">
                                    <div class="filter-label">Search</div>
                                    <div class="search-input-container">
                                        <input class="search-input"
                                               type="text"
                                               placeholder="Quick Filter"
                                               ng-model="vm.searchString"
                                               ng-keydown="$event.keyCode == 13 ? vm.refreshList() : null"
                                               ng-blur="vm.refreshList()">
                                        <img src="/content/images/cross.png"
                                             class="search-clear-button"
                                             ng-show="vm.searchString"
                                             ng-click="vm.searchString = ''; vm.refreshList();">
                                    </div>
                                </div>
                            </div>

                            <!-- Header bottom section with space-between layout -->
                            <div class="header-bottom-section">
                                <div class="current-filter-description">
                                    {{vm.totalFilteredServices}} of {{vm.totalServices}} services
                                </div>

                                <!-- Show Display Description Toggle -->
                                <div class="show-display-desc-toggle">
                                    <md-switch ng-model="vm.showDisplayDesc">Show Display Description</md-switch>
                                </div>
                            </div>

                            <!-- Services Database Table -->
                            <div class="table-responsive-vertical shadow-z-1 table-container">
                        <table class="table table-striped table-hover table-condensed table-full-width"
                               st-table="vm.servicesList"
                               st-table-filtered-list="exportList"
                               st-global-search="vm.listFilter"
                               st-persist="servicesList"
                               st-pipe="vm.orderBy">
                            <thead>
                                <tr>
                                    <th st-sort="description" class="can-sort text-left">Description</th>
                                    <th st-sort="manufacturer" class="can-sort text-center">Manufacturer</th>
                                    <th style="width: 50px;" ng-show="vm.actionButtons.length > 0 && vm.hasPermissionForAnyAction()"></th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr ng-repeat="row in vm.servicesList track by row.serviceTemplateId"
                                    class="list-row clickable"
                                    ng-class="{'selected-row': vm.selectedSubstitute && vm.selectedSubstitute.serviceTemplateId === row.serviceTemplateId}"
                                    ng-click="vm.selectService(row)">
                                    <td data-title="Description">
                                        <div style="width: 100%; padding-left: 10px; padding-right: 10px; box-sizing: border-box; text-align: left;">
                                            <!-- Favourite Star -->
                                            <i ng-if="row.isFavourite==true"
                                               class="fa fa-star"
                                               style="color: gold; margin-right: 5px;"
                                               aria-hidden="true"></i>
                                            {{vm.showDisplayDesc ? row.displayDescription : row.description }}
                                        </div>
                                    </td>
                                    <td data-title="Manufacturer" class="text-center">{{row.manufacturerDescription}}</td>
                                    <td ng-show="vm.actionButtons.length > 0 && vm.hasPermissionForAnyAction()">
                                        <div class="three-dot-menu-container">
                                            <md-menu md-position-mode="target-right target" md-offset="0 0">
                                                <md-button aria-label="Open menu" class="md-icon-button three-dot-menu-button" ng-click="$mdMenu.open($event)">
                                                    <md-icon md-menu-origin class="material-icons">more_vert</md-icon>
                                                </md-button>
                                                <md-menu-content width="4">
                                                    <md-menu-item ng-repeat="action in vm.actionButtons"
                                                                  ng-if="vm.hasPermissionForAction(action)"
                                                                  ng-click="vm.performAction(action, row)">
                                                        <md-button>
                                                            {{action.title}}
                                                        </md-button>
                                                    </md-menu-item>
                                                </md-menu-content>
                                            </md-menu>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div ng-show="vm.showingToCnt < vm.totalFilteredServices"
                             style="width:max-content; margin:auto; margin-top: -10px; margin-bottom: 20px;"
                             class="clickable"
                             ng-click="vm.getMoreResults()">
                            <span ng-show="!vm.showMoreBusy">Show more</span>
                            <i ng-show="vm.showMoreBusy" class="fa fa-spinner fa-spin"></i>
                        </div>
                        <div class="widget-pager" style="text-align: center;">
                            <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
                        </div>
                        </div>
                    </md-toolbar>

                    <div style="margin-top: 20px; padding: 0 20px;">
                        <span ng-show="vm.selectedSubstitute != null">
                            Clicking save will substitute <strong>{{vm.data.overrideDisplayDescription}}</strong>
                            with <strong> {{vm.showDisplayDesc ? vm.selectedSubstitute.displayDescription : vm.selectedSubstitute.description}}</strong>
                        </span>
                    </div>

                </div>

        </div>

        <!-- Action Buttons -->
        <div data-cc-widget-button-bar
             data-is-modal="true"
             style="height:70px;">

            <md-button class="md-raised md-primary"
                       redi-allow-roles="['assessment_page_(tabs/sub-tabs)__services__edit']"
                       ng-disabled="!vm.selectedSubstitute"
                       ng-click="vm.save()">
                Save
            </md-button>
            <md-button class="md-raised"
                       ng-click="vm.cancel()">
                Cancel
            </md-button>
            <div class="clearfix"></div>
        </div>

    </div>

        </div>
    </div>

</form>

<style>
    /* Multi-filter styles - display flow in a row with content-fitting filters */
    .filters-container {
        flex: none; /* Don't expand to fill space */
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        align-items: flex-start;
        width: auto; /* Size to content */
    }

    .filter {
        position: relative;
        margin-top: 32px; /* To account for heading above that is positioned absolute */
        font-size: 1.2rem !important;
        flex: 0 0 auto; /* Don't grow or shrink, size to content */
        max-width: 200px; /* Maximum width constraint */
        min-width: max(150px, max-content); /* At least 150px, but expand to fit content if larger */
        width: fit-content; /* Size to content */
    }

    .search-input-container {
        position: relative;
        width: 100%;
        min-width: max(150px, max-content); /* At least 150px, but expand to fit content if larger */
    }

    .search-input {
        width: 100%;
        min-width: max(150px, max-content); /* At least 150px, but expand to fit content if larger */
        height: 45px;
        margin-top: -6px;
        padding: 7px 30px 6px 10px; /* Increased right padding to make room for clear button */
        box-sizing: border-box;
        border: none;
        background-color: #e2e2e2;
        color: black !important;
    }

    .search-input:focus-visible {
        border: none !important;
    }

    .search-input::placeholder {
        color: black !important;
    }

    .search-input:-ms-input-placeholder {
        color: black !important;
    }

    /* Ensure search input text has proper margin from clear button */
    .search-input {
        text-indent: 0;
        padding-right: 30px; /* Space for clear button */
    }

    .search-clear-button {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 16px;
        height: 16px;
        padding: 4px;
        border-radius: 50%;
        cursor: pointer;
    }

    .search-clear-button:hover {
        background-color: #eeeeee;
    }

    .filter-label {
        position: absolute;
        top: -32px;
        left: 15px;
        font-size: 12px;
        color: #8e888e;
    }

    .current-filter-description {
        color: #7b7b7b;
        font-size: 12px;
        flex-grow: 1;
    }

    /* Selected row highlighting - forced with high specificity */
    .table-striped tbody tr.selected-row,
    .table-striped tbody tr.selected-row:hover,
    .table-hover tbody tr.selected-row:hover,
    tbody tr.selected-row,
    tbody tr.selected-row:hover,
    tr.selected-row,
    tr.selected-row:hover {
        background-color: #bfdaa1 !important;
    }

    .selected-row td,
    .selected-row:hover td {
        background-color: #bfdaa1 !important;
    }

    .header-container {
        justify-content: center;
        width: auto; /* Allow header to size to content */
        min-width: auto;
    }

    .header-container-expanded {
        min-height: 226px !important;
    }

    .header-inner-container {
        gap: 1%;
        max-height: max-content;
        height: max-content;
        align-items: flex-start;
        flex-direction: column;
        width: auto; /* Allow inner container to size to content */
    }

    .three-dot-menu-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
    }

    .three-dot-menu-button {
        margin: 0;
        min-width: 36px;
        width: 36px;
        height: 36px;
    }

    /* Header bottom section with space-between layout */
    .header-bottom-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 0 20px;
    }

    /* Show Display Description Toggle styling */
    .show-display-desc-toggle {
        font-size: 12px;
        flex-shrink: 0;
    }

    /* Table width expansion */
    .table-container {
        width: auto; /* Allow table to size to content */
        padding-bottom: 15px;
    }

    .table-container .list-row {
        height: 50px;
    }

    .table-full-width {
        width: auto; /* Allow table to size to content */
        min-width: auto;
    }

    /* Services substitute modal column sizing */
    .table-container th,
    .table-container td {
        width: max-content;
        min-width: 300px;
    }

    /* Height transition animation to match Orientate modal */
    md-dialog {
        transition: height 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    }
</style>