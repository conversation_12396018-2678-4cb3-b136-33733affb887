<section id="construction-list-view"
         class="main-content-wrapper"
         data-ng-controller="ServiceTemplateListCtrl as vm">

    <div class="widget">

        <!-- Title + Upload Spreadsheet Widget -->
        <div layout="row"
             style="padding: 10px 0px; ">
            <h1 style="margin: auto 0px; font-size: 20px; line-height: 28px; font-weight: 400;">
                {{vm.title}}
            </h1>
            <div layout-align="center center"
                 style="margin-top: 4px; margin-left: 20px;">
                <md-button class="md-primary"
                           ngf-select="vm.uploadFile($file)"
                           redi-allow-roles="['settings__uploaddatasets__view']">
                    Upload Spreadsheet
                </md-button>
                <md-button class="md-primary"
                           ng-click="vm.exportDatabase()"
                           redi-allow-roles="['settings__uploaddatasets__view']">
                    Export
                </md-button>
            </div>
        </div>

        <md-toolbar class="widget-actionbar-wrapper header-container" flex>
            <div class="md-toolbar-tools widget-actionbar header-inner-container">
                <div class="filters-container">
                    <!-- Search -->
                    <div class="filter">
                        <div class="filter-label">Search</div>
                        <div class="search-input-container">
                            <input class="search-input"
                                   type="text"
                                   placeholder="Quick Filter"
                                   ng-model="vm.searchString"
                                   ng-keydown="$event.keyCode == 13 ? vm.refreshList() : null"
                                   ng-blur="vm.refreshList()">
                            <img src="/content/images/cross.png"
                                 class="search-clear-button"
                                 ng-show="vm.searchString"
                                 ng-click="vm.searchString = null; vm.refreshList(); $event.stopPropagation()">
                        </div>
                    </div>

                    <!-- Multi-Filters -->
                    <div ng-repeat="filter in vm.filters track by filter.field"
                         ng-if="filter.name != null"
                         class="filter {{vm.anyOptionsSelectedOnField(filter, vm.appliedFilters) ? 'options-selected' : ''}}">

                        <!-- Label -->
                        <div ng-if="filter.name != '[blank]' && filter.name != '[moreLessButton]'" class="filter-label">{{vm.keyToName(filter.name)}}</div>

                        <!-- Clear Button -->
                        <img src="/content/images/cross.png"
                             class="filter-clear-button"
                             ng-click="vm.clearFilter(filter);$event.stopPropagation()"
                             ng-show="vm.anyOptionsSelectedOnField(filter, vm.appliedFilters)"
                        />

                        <!-- Normal Field -->
                        <md-select ng-if="filter.name != '[blank]' && filter.name != '[moreLessButton]' && !filter.isDate"
                                   class="filter-dropdown"
                                   multiple="true"
                                   md-selected-text="vm.getFilterSelectedText(filter, vm.filterOptions, vm.appliedFilters)"
                                   ng-model="vm.appliedFilters[filter.field]"
                                   ng-change="vm.refreshList()">

                            <!-- Normal Option -->
                            <md-option ng-show="vm.getFilterCountItem(filter, item) > 0 || !vm.initialiseComplete"
                                       ng-repeat="item in vm.filterOptions[filter.field] track by item.value"
                                       ng-value="item.value">
                                {{vm.optionName(item, filter.isBool, filter.isDecimal)}} <span ng-if="vm.initialiseComplete && vm.getFilterCountItem(filter, item) > 0">({{vm.getFilterCountItem(filter, item)}})</span>
                            </md-option>

                        </md-select>

                        <!-- Date Field -->
                        <md-select ng-if="filter.name != '[blank]' && filter.name != '[moreLessButton]' && filter.isDate"
                                    class="filter-dropdown"
                                    multiple="true"
                                    md-selected-text="vm.appliedFilters[filter.field].label"
                                    ng-model="vm.appliedFilters[filter.field]">
                        </md-select>
                        <div ng-if="filter.isDate"
                                rd-date-range-picker
                                class="date-range-picker"
                                ng-model="vm.appliedFilters[filter.field]"
                                format="MMMM D, YYYY"
                                ranges="vm.ranges"
                                on-change="vm.refreshList">
                        </div>

                        <!-- Blank -->
                        <div ng-if="filter.name == '[blank]'"></div>
                    </div>
                </div>
            </div>

            <!-- Number of Items -->
            <div class="current-filter-description add-margin" ng-show="!vm.filtersApplied">
                Showing {{vm.totalRecords}} services
            </div>
            <div class="current-filter-description add-margin" ng-show="vm.filtersApplied">
                {{vm.totalFilteredRecords}} of {{vm.totalRecords}} services match your filters
                (<u ng-click="vm.clearFilters()" style="text-decoration: underline; cursor:pointer;">clear filters</u>)
            </div>

            <!-- Show Display Description Toggle -->
            <div style="font-size: 12px; position: absolute; bottom: 0px; right: 26px;">
                <md-switch ng-model="vm.showDisplayDesc">Show Display Description</md-switch>
            </div>
        </md-toolbar>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.serviceTemplateList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="serviceTemplateList"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th style="width: 50px;">
                            <div style="display: grid; justify-content: center;">
                                <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                             ng-model="vm.bulkSelected"
                                             ng-click="vm.bulkSelect(!vm.bulkSelected);"></md-checkbox>
                            </div>
                        </th>
                        <th st-sort="description" class="can-sort text-left">Description</th>
                        <th st-sort="serviceCategoryTitle" class="can-sort text-center">Category</th>
                        <th st-sort="manufacturer" class="can-sort text-center">Manufacturer</th>
                        <th st-sort="isFavourite" class="can-sort text-center" style="width: 1%; white-space: nowrap;">Favourite</th>
                        <th style="width: 50px;"></th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.serviceTemplateList" class="list-row clickable">
                        <td>
                            <div style="display: grid; justify-content: center;">
                                <md-checkbox style="margin: auto; text-align: center; width: 0;"
                                             ng-model="row.isBulkSelected"
                                             ng-click="$event.stopPropagation();">
                                </md-checkbox>
                            </div>
                        </td>
                        <td data-title="Description" ng-click="vm.goToServiceTemplate(row.serviceTemplateId)">
                            <div style="width: 100%; padding-left: 10px; padding-right: 40px; box-sizing: border-box; text-align: left;">
                                {{vm.showDisplayDesc ? row.displayDescription : row.description }}
                                <div class="go-to-variation-button" style="order:3;"> <img src="/content/images/arrow-right.png" /> </div>
                            </div>
                        </td>
                        <td data-title="Type"
                            class="text-center"
                            ng-click="vm.goToServiceTemplate(row.serviceTemplateId)">
                            {{::row.serviceCategoryTitle}}
                        </td>
                        <td data-title="Manufacturer"
                            class="text-center"
                            ng-click="vm.goToServiceTemplate(row.serviceTemplateId)">
                            {{::row.manufacturerDescription}}
                        </td>
                        <td data-title="Favourite" class="text-center">
                            <div style="display: flex; justify-content: center; align-items: center;">
                                <md-checkbox ng-model="row.isFavourite"
                                             style="margin: 0;"
                                             ng-click="vm.setFavouriteStatus(row.serviceTemplateId, !row.isFavourite);$event.stopPropagation();"/>
                            </div>
                        </td>
                        <td class="text-center">
                            <div style="display: flex; justify-content: center; align-items: center;">
                                <md-menu>
                                    <img md-menu-origin
                                         class="clickable"
                                         ng-click="$mdOpenMenu(); $event.stopPropagation();"
                                         src="/content/feather/more-horizontal.svg"/>
                                    <md-menu-content>
                                        <!-- Duplicate -->
                                        <md-menu-item>
                                            <md-button ng-click="vm.clone(row)">
                                                Duplicate
                                            </md-button>
                                        </md-menu-item>
                                        <md-menu-divider></md-menu-divider>
                                        <!-- Delete -->
                                        <md-menu-item>
                                            <md-button ng-click="vm.delete(row)">
                                                <span style="color: orangered;">Delete</span>
                                            </md-button>
                                        </md-menu-item>
                                    </md-menu-content>
                                </md-menu>
                            </div>
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                <tr>
                    <td colspan="9999" class="text-center">
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr;">

                            <!-- Just empty. -->
                            <div></div>

                            <!-- Pagination Display -->
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>

                            <!-- Just empty. -->
                            <div></div>
                        </div>
                    </td>
                </tr>
                </tfoot>
            </table>
            <div ng-show="vm.showingToCnt < vm.totalFilteredRecords"
                 style="width:max-content; margin:auto; margin-top: -10px; margin-bottom: 20px;"
                 class="clickable"
                 ng-click="vm.getMoreResults()">
                <span ng-show="!vm.showMoreBusy">Show more</span>
                <i ng-show="vm.showMoreBusy" class="fa fa-spinner fa-spin"></i>
            </div>
            <div class="widget-pager" style="text-align: center;">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: -36px; margin-left: 10px;">
                <!-- Bulk Edit Button -->
                <md-button ng-click="vm.showBulkEditModal()"
                           ng-disabled="!vm.bulkSelectionsExist()"
                           class="md-raised md-primary"
                           style="margin-bottom: 20px;">
                    BULK EDIT
                </md-button>

                <div></div>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>

<style>
    .list-row {
        height: 52px;
    }

    .list-row:hover .go-to-variation-button {
        visibility: visible;
    }

    .go-to-variation-button {
        visibility: hidden;
        position: absolute;
        top: 50%; transform: translateY(-50%);
        right: 7%;
        width: 25px;
        height: 25px;
        min-width: 25px;
        min-height: 25px;
        border-radius: 4px;
        cursor: pointer;
    }

    .go-to-variation-button:hover {
        background-color: #d1d1d1;
    }

    .go-to-variation-button > img {
        position: absolute;
        top: 50%;
        left: 54%;
        transform: translate(-50%, -50%);
        width: 60%;
        height: auto;
    }

    .header-container {
        min-height: 150px !important;
        justify-content: center;
    }

    .header-container-expanded {
        min-height: 226px !important;
    }

    .header-inner-container {
        gap: 1%;
        max-height: max-content;
        height: max-content;
        align-items: flex-start;
    }

    /* Multi-filter styles copied from Jobs page */
    .filters-container {
        flex: 10;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
        column-gap: 2%;
        row-gap: 15px;
    }

    .filter {
        position: relative;
        margin-top: 32px; /* To account for heading above that is positioned absolute */
        font-size: 1.2rem !important;
    }

    .search-input-container {
        position: relative;
        width: 100%;
    }

    .search-input {
        width: 100%;
        height: 45px;
        margin-top: -6px;
        padding: 7px 10px 6px 10px;
        box-sizing: border-box;
        border: none;
        background-color: #e2e2e2;
        color: black !important;
    }

    .search-input:focus-visible {
        border: none !important;
    }

    .search-input::placeholder {
        color: black !important;
    }

    .search-input:-ms-input-placeholder {
        color: black !important;
    }

    .search-clear-button {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 16px;
        height: 16px;
        padding: 4px;
        border-radius: 50%;
        cursor: pointer;
    }

    .search-clear-button:hover {
        background-color: #eeeeee;
    }

    .filter-label {
        position: absolute;
        top: -32px;
        left: 15px;
        font-size: 12px;
        color: #8e888e;
    }

    .filter-dropdown {
        margin: -6px 0 0 2px;
        width: 100%;
        height: 45px;
        padding: 7px 10px 6px 10px;
        box-sizing: border-box;
        background-color: #e2e2e2;
        color: black !important;
    }

    .filter-dropdown span {
        margin-top: -2px;
    }

    .filter-dropdown .md-select-placeholder {
        color: black !important;
    }

    .filter-dropdown .md-select-value {
        border-bottom: none !important;
    }

    .filter-clear-button {
        display: none;
        position: absolute;
        right: 3px;
        bottom: 13px;
        z-index: 50;
        width: 16px;
        height: auto;
        padding: 4px;
        border-radius: 50%;
        cursor: pointer;
    }

    .filter-clear-button:hover {
        background-color: #eeeeee;
    }

    .filter.options-selected > .filter-clear-button {
        display: inherit !important;
    }

    .filter.options-selected .md-select-icon {
        margin-left: -36px;
    }

    .current-filter-description {
        margin-left: 20px;
        margin-top: 20px;
        color: #7b7b7b;
        font-size: 12px;
    }

    .current-filter-description.add-margin {
        margin-top: 30px;
    }
</style>
