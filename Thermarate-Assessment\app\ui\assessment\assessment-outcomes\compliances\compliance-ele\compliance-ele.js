(function () {
    'use strict';
    angular
        .module('app')
        .component('complianceEle', {
            bindings: {
                assessment: '<',
                complianceData: '<',
                isOption: '<',
                onComplianceChanged: '&',
                assessmentSoftwareList: '<',
                jobFiles: '<',
                isLocked: '<',
                complianceMethodList: '<',
                complianceTypeChanged: '&',
                purchaseOrderDefault: '<',
            },
            templateUrl: 'app/ui/assessment/assessment-outcomes/compliances/compliance-ele/compliance-ele.html',
            controller: ComplianceEle,
            controllerAs: 'vm'
        });

    ComplianceEle.$inject = ['$scope', 'security', 'clientservice', 'compliancemethodservice', 'certificationservice'];

    function ComplianceEle($scope, securityservice, clientservice, compliancemethodservice, certificationservice) {
        var vm = this;
        var _formValid = false;

        vm.calculateCompliance = calculateCompliance;
        vm.purchaseOrderList = clientservice.purchaseOrderSettingsList;

        vm.$onInit = onInit;
        vm.$onChanges = onChanges;

        // Permissions
        vm.permission_field_certification_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__certification__view']);
        vm.permission_field_certification_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__certification__edit']);
        vm.permission_field_assessmentmethod_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentmethod__view']);
        vm.permission_field_assessmentmethod_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentmethod__edit']);
        vm.permission_field_assessmentsoftware_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentsoftware__view']);
        vm.permission_field_assessmentsoftware_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__assessmentsoftware__edit']);
        vm.permission_field_herrequired_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__herrequired__view']);
        vm.permission_field_herrequired_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__herrequired__edit']);
        vm.permission_action_overrideenergyloads = securityservice.immediateCheckRoles(['assessment_actions__overrideenergyloads']);
        vm.permission_field_valid_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__valid__view']);
        vm.permission_field_valid_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__valid__edit']);
        vm.permission_field_showtoclient_view = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__showtoclient__view']);
        vm.permission_field_showtoclient_edit = securityservice.immediateCheckRoles(['assessment_page_(tabs/sub-tabs)__showtoclient__edit']);

        vm.sectorDeterminationList = [];
        let sectorsPromise = certificationservice.getSectorDeterminations()
            .then(data => {
                vm.sectorDeterminationList = data;
            });

        vm.certificationList = [];
        var certificationPromise = certificationservice.getList()
            .then(function (data) {
                vm.allCertificationList = data.data;
                vm.certificationList = vm.allCertificationList;
            });

        function onInit() {
            if (vm.complianceData != null && vm.complianceData.epComplianceData) {
                calculateCompliance();
            }
            $scope.$watch('complianceEleForm.$valid', function (newVal) {
                _formValid = newVal && vm.complianceData.epComplianceData != null;
                calculateCompliance();
            });

            vm.complianceData.availableSoftware = compliancemethodservice.determineAvailableSoftware(vm.assessmentSoftwareList, vm.complianceData.complianceMethod.complianceMethodCode);
        }
        function onChanges(changes) {
            if (changes.complianceData && changes.complianceData.currentValue && vm.complianceData.epComplianceData != null) {
                calculateCompliance();
            }
        }

        // Elemental Provisions is compliant when
        // If every single check box (for all floors) is CHECKED AND a glazing calculation file is attached...
        function calculateCompliance() {

            let compliant = compliancemethodservice.calculateEleCompliance(vm.assessment, vm.complianceData);

            // Form must also be filled. To prevent this from un-selecting already selected option
            // (e.g. when the form is loading on startup) we skip this check if the item is ALREADY selected.
            if (_formValid == false && !vm.complianceData.isSelected)
                compliant = false;

            vm.complianceData.isCompliant = compliant;
            if (!compliant) {
                vm.complianceData.isSelected = false;
            }

            // If we're compliant AND it's NO OPTIONS 
            // OR
            // If this is the baseline and we're compliant
            // Then-select the baseline
            if ((compliant && vm.assessment.allComplianceOptions.length == 1) ||
                compliant && vm.complianceData.isBaselineSimulation == true)
                vm.complianceData.isSelected = true;

            vm.onComplianceChanged({ compliant: compliant });
        }
        function isYesOrNA(val) {
            return val == 'Yes' || val == 'N/A';
        }

        vm.isSoftwareAvailable = function (code) {
            return vm.complianceData.availableSoftware?.find(x => x.assessmentSoftwareCode == code) != null;
        }

        vm.determineUINumber = function () {
            return vm.complianceData.optionIndex;
        }

        vm.classificationChanged = function (value) {
            vm.complianceData.classification = value;
        }

        vm.availableComplianceMethods = function() {
            return compliancemethodservice.determineAvailableComplianceMethods(vm.complianceMethodList, vm.assessment.worksDescription.worksDescriptionCode);
        }

        vm.updateDataLinkedToCertification = function() {
            setTimeout(() => {
                certificationservice.updateDataLinkedToCertification(vm.assessment, vm.complianceData);
            }, 100);
        }
    }
})();